# 转诊管理 API 文档

> **注意**: 本文档提供转诊管理 API 的详细接口规范。用户功能说明请参考 [FUNCTIONAL_MANUAL.md](../FUNCTIONAL_MANUAL.md#10-转诊管理-api-集成)。

## 概述

本文档详细描述转诊管理 API 的接口规范，包括转诊医生管理、患者关联管理等功能。该 API 管理"转诊医生"（可以转诊患者的个人）及其与"患者"的关联关系。

## API 端点

### 添加推荐人

**Endpoint:** `POST /referral-management/referrer`

## Request Body

**Required:** Yes

### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | 推荐人的姓名 | Yes |
| phone | string | 推荐人的电话 | Yes |

**Examples:**

*default*

```json
{
  "name": "张三",
  "phone": "12345678901"
}
```

## Responses

### Status Code: 201

推荐人添加成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| referrerId | string | 返回创建的推荐人ID | Yes |

**Examples:**

*default*

```json
{
  "referrerId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
}
```

### Status Code: 409

推荐人手机号已存在

---

# 分页查询推荐人

**Endpoint:** `GET /referral-management/referrer`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| limit | query | 请求数量限制 | No | number |
| after | query | 下一页数据的游标 | No | string,null |
| before | query | 上一页数据的游标 | No | string,null |
| sortBy | query | 排序字段 | No | string |
| sortOrder | query | 排序顺序 | No | string |
| referrerName | query | 按推荐人姓名模糊搜索 | No | string |
| referrerPhone | query | 按推荐人手机号精确或模糊搜索 | No | string |

## Responses

### Status Code: 200

推荐人查询成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| items | array | 当前页数据列表 | Yes |
| nextCursor | string,null | 下一页的游标。如果为 null，表示没有更多数据。 | No |
| previousCursor | string,null | 用于获取上一页数据的游标 (来自下一页响应的 previousCursor) | No |
| sortBy | string | 排序字段 (通常是游标字段) | No |
| sortOrder | string | 排序顺序 | No |

**Examples:**

*default*

```json
{
  "items": [
    {
      "id": "string",
      "name": "string",
      "phone": "string",
      "createdAt": "2019-08-24T14:15:22Z",
      "updatedAt": "2019-08-24T14:15:22Z"
    }
  ],
  "nextCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "previousCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "sortBy": "createdAt",
  "sortOrder": "asc"
}
```

---

# 更新推荐人

**Endpoint:** `PUT /referral-management/referrer/{id}`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| id | path |  | Yes | string |

## Request Body

**Required:** Yes

### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| name | string | 推荐人的姓名 | Yes |
| phone | string | 推荐人的电话 | Yes |

**Examples:**

*default*

```json
{
  "name": "张三",
  "phone": "12345678901"
}
```

## Responses

### Status Code: 200

推荐人更新成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| referrerId | string | 返回更新的推荐人ID | Yes |

**Examples:**

*default*

```json
{
  "referrerId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
}
```

### Status Code: 404

推荐人不存在

### Status Code: 409

推荐人手机号已存在

---

# 查询推荐人

**Endpoint:** `GET /referral-management/referrer/{id}`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| id | path |  | Yes | string |

## Responses

### Status Code: 200

推荐人查询成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| id | string | 推荐人ID | Yes |
| name | string | 推荐人名称 | Yes |
| phone | string | 推荐人手机号 | Yes |
| createdAt | string | 创建时间 | Yes |
| updatedAt | string | 更新时间 | Yes |

**Examples:**

*default*

```json
{
  "id": "string",
  "name": "string",
  "phone": "string",
  "createdAt": "2019-08-24T14:15:22Z",
  "updatedAt": "2019-08-24T14:15:22Z"
}
```

---

# 创建推荐人与患者的关联

**Endpoint:** `POST /referral-management/association`

## Request Body

**Required:** Yes

### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| referrerId | string | 推荐人 ID | Yes |
| patientMBGLId | number | 慢病管理患者 ID | Yes |
| patientGCPMId | string | GCPM 患者 UUID | No |

**Examples:**

*default*

```json
{
  "referrerId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "patientMBGLId": 123,
  "patientGCPMId": "yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy"
}
```

## Responses

### Status Code: 201

---

# 分页查询推荐人与患者的关联记录(基于游标)

**Endpoint:** `GET /referral-management/association`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| limit | query | 请求数量限制 | No | number |
| after | query | 下一页数据的游标 | No | string,null |
| before | query | 上一页数据的游标 | No | string,null |
| sortBy | query | 排序字段 | No | string |
| sortOrder | query | 排序顺序 | No | string |
| patientName | query | 按患者姓名模糊搜索 | No | string |
| patientPhone | query | 按患者手机号精确或模糊搜索 | No | string |
| referrerName | query | 按推荐人姓名模糊搜索 | No | string |
| referrerPhone | query | 按推荐人手机号精确或模糊搜索 | No | string |

## Responses

### Status Code: 200

推荐人与患者的关联记录查询成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| items | array | 当前页数据列表 | Yes |
| nextCursor | string,null | 下一页的游标。如果为 null，表示没有更多数据。 | No |
| previousCursor | string,null | 用于获取上一页数据的游标 (来自下一页响应的 previousCursor) | No |
| sortBy | string | 排序字段 (通常是游标字段) | No |
| sortOrder | string | 排序顺序 | No |

**Examples:**

*default*

```json
{
  "items": [
    {
      "id": "string",
      "referrer": {
        "id": "string",
        "name": "张三",
        "phone": "13800138000"
      },
      "patient": {
        "mbglId": 9001,
        "gcpmId": "string",
        "name": "string",
        "phone": "string"
      },
      "createdAt": "2019-08-24T14:15:22Z",
      "updatedAt": "2019-08-24T14:15:22Z"
    }
  ],
  "nextCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "previousCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "sortBy": "createdAt",
  "sortOrder": "asc"
}
```

---

# 更新推荐人与患者的关联

**Endpoint:** `PUT /referral-management/association/{id}`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| id | path | 关联记录ID | Yes | string |

## Request Body

**Required:** Yes

### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| referrerId | string | 推荐人 ID | Yes |
| patientMBGLId | number | 慢病管理患者 ID | Yes |
| patientGCPMId | string | GCPM 患者 UUID | No |

**Examples:**

*default*

```json
{
  "referrerId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "patientMBGLId": 123,
  "patientGCPMId": "yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy"
}
```

## Responses

### Status Code: 200

推荐人与患者的关联记录更新成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| associationRecordId | string | 关联记录 ID | Yes |

**Examples:**

*default*

```json
{
  "associationRecordId": "string"
}
```

### Status Code: 404

关联记录不存在

---

# 查询推荐人与患者的关联记录

**Endpoint:** `GET /referral-management/association/{id}`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| id | path |  | Yes | string |

## Responses

### Status Code: 200

推荐人与患者的关联记录查询成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| id | string | 关联记录 ID | Yes |
| referrer | object | 推荐人 | Yes |
| patient | object | 患者 | Yes |
| createdAt | string | 创建时间 | Yes |
| updatedAt | string | 更新时间 | Yes |

**Examples:**

*default*

```json
{
  "id": "string",
  "referrer": {
    "id": "string",
    "name": "张三",
    "phone": "13800138000"
  },
  "patient": {
    "mbglId": 9001,
    "gcpmId": "string",
    "name": "string",
    "phone": "string"
  },
  "createdAt": "2019-08-24T14:15:22Z",
  "updatedAt": "2019-08-24T14:15:22Z"
}
```

---

# 查询没有推荐人的慢病管理患者

**Endpoint:** `GET /referral-management/no-referrer-mbgl-patients`

## Parameters

| Name | Located In | Description | Required | Type |
| ---- | ---------- | ----------- | -------- | ---- |
| limit | query | 请求数量限制 | No | number |
| after | query | 慢病管理患者id,下一页数据的游标 | No | string,null |
| before | query | 慢病管理患者id,上一页数据的游标 | No | string,null |
| sortBy | query | 排序字段 | No | string |
| sortOrder | query | 排序顺序 | No | string |
| patientName | query | 按患者姓名模糊搜索 | No | string |
| patientPhone | query | 按患者手机号精确或模糊搜索 | No | string |

## Responses

### Status Code: 200

没有推荐人的慢病管理患者查询成功

#### Media Type: application/json

**Schema Type:** object

**Properties:**

| Name | Type | Description | Required |
| ---- | ---- | ----------- | -------- |
| items | array | 当前页数据列表 | Yes |
| nextCursor | string,null | 下一页的游标。如果为 null，表示没有更多数据。 | No |
| previousCursor | string,null | 用于获取上一页数据的游标 (来自下一页响应的 previousCursor) | No |
| sortBy | string | 排序字段 (通常是游标字段) | No |
| sortOrder | string | 排序顺序 | No |

**Examples:**

*default*

```json
{
  "items": [
    {
      "mbglId": "string",
      "patientName": "string",
      "patientPhone": "string",
      "createdAt": "2019-08-24T14:15:22Z",
      "updatedAt": "2019-08-24T14:15:22Z"
    }
  ],
  "nextCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "previousCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "sortBy": "user_id",
  "sortOrder": "asc"
}
```

## 相关文档

- [功能使用手册 - 转诊管理 API 集成](../FUNCTIONAL_MANUAL.md#10-转诊管理-api-集成)
- [系统配置管理](../FUNCTIONAL_MANUAL.md#7-系统配置管理)

---

