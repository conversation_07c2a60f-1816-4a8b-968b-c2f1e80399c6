# Repository Guidelines

## Project Structure & Module Organization
- `src/` — SvelteKit app: routes under `src/routes/*` (`+page.svelte|ts`), shared components in `src/lib/`, app shell in `src/app.html` and styles in `src/app.css`.
- `public/` and `static/` — static assets served as-is.
- `build/` — Vite build output consumed by <PERSON><PERSON>.
- `src-tauri/` — Tauri (Rust) backend: commands in `src-tauri/src/commands/*`, services in `src-tauri/src/services/*`, models in `src-tauri/src/models/*`, config in `src-tauri/tauri.conf.json`, tests in `src-tauri/tests/`.
- `docs/` — project documentation; `scripts/` — helper scripts.

## Build, Test, and Development Commands
- Frontend dev: `npm run dev` — starts Vite on port 1420.
- Desktop dev: `npm run tauri dev` — runs Rust + Vite with live reload.
- Frontend build: `npm run build` — outputs to `build/`.
- Desktop build: `npm run tauri build` — produces release bundles.
- Type check: `npm run check` (Svelte + TS).
- Rust: `cd src-tauri && cargo build` | `cargo test`.

## Coding Style & Naming Conventions
- Svelte/TS: 2‑space indent; components `PascalCase.svelte` (e.g., `SqliteDictionaryForm.svelte`); route files follow SvelteKit (`+page.svelte`, `+layout.svelte`). Keep modules under `src/lib/*`.
- Tailwind: prefer utility classes; avoid inline styles.
- Rust: `snake_case` for functions/vars, `PascalCase` for types; format with `cargo fmt` and lint with `cargo clippy` before PRs.

## Testing Guidelines
- Rust unit/integration tests live in `src-tauri/tests/*` or module `mod tests {}`; name files `*_test.rs`. Run with `cargo test`.
- Frontend: no unit tests yet; keep components pure and add minimal repro routes under `src/routes/test/*` when helpful.

## Commit & Pull Request Guidelines
- Use Conventional Commits: `feat:`, `fix:`, `refactor:`, `chore:`, `docs:`, `test:` (e.g., `feat: add batch delete for dictionary items`).
- PRs: include a clear description, linked issues, screenshots for UI changes, and notes on migration/config updates. Ensure `npm run check`, `cargo fmt`, and `cargo test` pass.

## Security & Configuration Tips
- Never commit secrets. Local env goes in `.env` (frontend vars should use `VITE_*`).
- When adding capabilities/plugins, update `src-tauri/tauri.conf.json` and `src-tauri/capabilities/*` with least privilege.
