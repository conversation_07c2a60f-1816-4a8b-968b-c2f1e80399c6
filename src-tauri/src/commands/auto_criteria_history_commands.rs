use log::{error, info};
use tauri::command;

use crate::models::auto_criteria_history::{
    AutoCriteriaHistory, AutoCriteriaHistoryQuery, CreateAutoCriteriaHistoryRequest,
};
use crate::services::auto_criteria_history_service::AutoCriteriaHistoryService;

#[command]
pub fn init_auto_criteria_history_tables(db_path: String) -> Result<bool, String> {
    let svc = AutoCriteriaHistoryService::new(db_path);
    match svc.init_tables() {
        Ok(_) => Ok(true),
        Err(e) => {
            error!("init history tables failed: {}", e);
            Err(e.to_string())
        }
    }
}

#[command]
pub fn save_auto_criteria_history(
    db_path: String,
    request: CreateAutoCriteriaHistoryRequest,
) -> Result<i64, String> {
    info!("save auto criteria history");
    let svc = AutoCriteriaHistoryService::new(db_path);
    svc.init_tables().ok();
    match svc.create(&request) {
        Ok(id) => Ok(id),
        Err(e) => {
            error!("save history failed: {}", e);
            Err(e.to_string())
        }
    }
}

#[command]
pub fn get_auto_criteria_histories(
    db_path: String,
    query: AutoCriteriaHistoryQuery,
) -> Result<Vec<AutoCriteriaHistory>, String> {
    let svc = AutoCriteriaHistoryService::new(db_path);
    svc.init_tables().ok();
    match svc.list(&query) {
        Ok(list) => Ok(list),
        Err(e) => {
            error!("list history failed: {}", e);
            Err(e.to_string())
        }
    }
}

#[command]
pub fn get_auto_criteria_history(
    db_path: String,
    history_id: i64,
) -> Result<Option<AutoCriteriaHistory>, String> {
    let svc = AutoCriteriaHistoryService::new(db_path);
    match svc.get(history_id) {
        Ok(item) => Ok(item),
        Err(e) => {
            error!("get history failed: {}", e);
            Err(e.to_string())
        }
    }
}

#[command]
pub fn delete_auto_criteria_history(db_path: String, history_id: i64) -> Result<bool, String> {
    let svc = AutoCriteriaHistoryService::new(db_path);
    match svc.delete(history_id) {
        Ok(_) => Ok(true),
        Err(e) => {
            error!("delete history failed: {}", e);
            Err(e.to_string())
        }
    }
}
