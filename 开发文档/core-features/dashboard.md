# 项目管理仪表盘开发文档

> **注意**: 本文档提供仪表盘功能的详细技术实现信息。用户功能说明请参考 [FUNCTIONAL_MANUAL.md](../FUNCTIONAL_MANUAL.md#6-数据分析仪表盘)。

## 概述

本文档详细描述项目管理仪表盘的技术设计、实现架构和开发指南。该仪表盘从项目数据库中提取关键运营指标，通过直观的图表形式展示，帮助项目管理者实时了解项目状态、监控进度、优化资源分配。

## 2. 目标用户

- 项目经理 (PM)
- 临床研究协调员 (CRC)
- 临床运营主管
- 研究机构管理层
- 申办方代表 (部分视图)

## 3. 设计原则

- **清晰直观**：信息易于理解，避免不必要的复杂性。
- **数据驱动**：所有指标均基于实时或近实时数据库信息。
- **关键指标优先**：突出对项目成功至关重要的信息。
- **可操作性**：提供有助于识别问题和采取行动的洞察。
- **美观与用户体验**：界面友好，符合现代审美。

## 4. 关键运营指标 (KPIs) 与图表建议

以下指标根据现有数据库表结构 (`projects`, `dictionary_items`, `project_personnel_roles`, `subsidies`, `project_criteria` 等) 设计。

### 4.1. 项目组合概览

| 指标名称                     | 描述                                               | 数据来源 (表.字段)                                                                 | 推荐图表类型                                 |
| :--------------------------- | :------------------------------------------------- | :--------------------------------------------------------------------------------- | :------------------------------------------- |
| **活动项目总数**             | 当前所有未关闭或未暂停的项目数量。                 | `projects` (计数 `project_id` WHERE `project_status_item_id` != '已完成/关闭')     | KPI 指标卡                                   |
| **项目状态分布**             | 各状态（如：进行中、已完成、暂停中）的项目数量及占比。 | `projects.project_status_item_id` JOIN `dictionary_items.item_id`                  | 柱状图 / 饼图 (状态种类不多时)               |
| **项目阶段分布**             | 各临床阶段（如：I期, II期, III期）的项目数量及占比。  | `projects.project_stage_item_id` JOIN `dictionary_items.item_id`                   | 柱状图 / 饼图                                |
| **招募状态分布**             | 各招募状态（如：招募中、暂停招募、招募完成）的项目数量。 | `projects.recruitment_status_item_id` JOIN `dictionary_items.item_id`            | 柱状图                                       |
| **治疗领域分布**             | 各疾病领域/适应症的项目数量。                         | `projects.disease_item_id` JOIN `dictionary_items.item_id`                         | 柱状图 / 树图                                |
| **申办方项目分布**           | 各申办方当前合作的项目数量。                         | `project_sponsors.sponsor_item_id` JOIN `dictionary_items.item_id` GROUP BY sponsor | 条形图                                       |
| **每月新启动项目数**         | 按月份统计的新启动项目数量趋势。                     | `projects.project_start_date`                                                      | 折线图 / 柱状图                              |
| **项目路径快速访问**         | 提供项目文件夹路径的直接访问列表。                   | `projects.project_path`                                                            | 表格 (带链接)                                |

### 4.2. 资源与人员管理

| 指标名称                 | 描述                                       | 数据来源 (表.字段)                                                               | 推荐图表类型   |
| :----------------------- | :----------------------------------------- | :------------------------------------------------------------------------------- | :------------- |
| **各项目分配人员数**     | 每个项目当前分配的人员总数。                 | `project_personnel_roles` (COUNT `personnel_id` GROUP BY `project_id`)           | 表格 / 柱状图  |
| **关键角色分布**         | PI、主要研究者等关键角色在各项目中的分布情况。 | `project_personnel_roles.role_item_id` JOIN `dictionary_items.item_id`           | 堆叠柱状图     |
| **人员工作负荷概览**     | （可选）每位员工参与的项目数量。             | `project_personnel_roles` (COUNT `project_id` GROUP BY `personnel_id`) JOIN `staff` | 表格 / 柱状图  |

### 4.3. 财务与补贴

| 指标名称             | 描述                                           | 数据来源 (表.字段)                                                                    | 推荐图表类型     |
| :------------------- | :--------------------------------------------- | :------------------------------------------------------------------------------------ | :--------------- |
| **项目补贴总览**     | 各项目的计划补贴总额。                             | `subsidies.total_amount` (SUM GROUP BY `project_id`)                                  | 柱状图 / 表格    |
| **补贴类型分布**     | 不同补贴类型 (`subsidy_type_item_id`) 的金额占比。 | `subsidies` (SUM `total_amount` GROUP BY `subsidy_type_item_id`) JOIN `dictionary_items` | 饼图 / 堆叠柱状图 |
| **补贴方案数量**     | 各项目拥有的补贴方案 (`subsidy_schemes`) 数量。    | `subsidy_schemes` (COUNT `scheme_id` GROUP BY `project_id`)                           | 柱状图           |

### 4.4. 研究药物与方案

| 指标名称             | 描述                                         | 数据来源 (表.字段)                                               | 推荐图表类型   |
| :------------------- | :------------------------------------------- | :--------------------------------------------------------------- | :------------- |
| **各项目研究药物数** | 每个项目涉及的研究药物种类数量。                 | `research_drugs` (COUNT `research_drug` GROUP BY `project_id`) | 表格 / 柱状图  |
| **药物分组概览**     | 各项目中的药物分组 (`drug_groups`) 及其份额。    | `drug_groups`                                                    | 表格 / 堆叠柱状图|

### 4.5. 入排标准概览

| 指标名称               | 描述                                       | 数据来源 (表.字段)                                                                    | 推荐图表类型 |
| :--------------------- | :----------------------------------------- | :------------------------------------------------------------------------------------ | :----------- |
| **项目标准数量**       | 各项目定义的入选/排除标准总数。                | `project_criteria` (COUNT `project_criterion_id` GROUP BY `project_id`)             | 柱状图       |
| **标准类型分布**       | 各项目入选 (`inclusion`) 与排除 (`exclusion`) 标准的数量。 | `project_criteria.criterion_type` (COUNT GROUP BY `project_id`, `criterion_type`) | 堆叠柱状图   |
| **活动标准占比**       | （如果适用）项目中激活状态的标准占比。         | `project_criteria.is_active`                                                          | 仪表盘图     |

## 5. 仪表盘布局建议 (草图)

仪表盘可以设计为多区域布局：

- **顶部区域 (KPIs)**: 放置核心指标卡，如活动项目总数、招募中项目数等。
- **左侧/主区域 (项目概览)**: 放置项目状态分布、阶段分布、治疗领域分布等图表。
- **右侧/次区域 (详细信息/专项指标)**: 放置申办方项目分布、每月新启动项目数、补贴概览等。
- **底部区域 (列表/表格)**: 可放置项目路径快速访问表、各项目分配人员数等详细列表。

用户可以通过筛选器（如按项目状态、申办方、日期范围）与仪表盘交互。

## 6. 图表库技术选型

考虑到本项目的技术栈 (SvelteKit, TypeScript, Tailwind CSS) 以及对美观和交互性的追求，推荐以下图表库：

### 6.1. 主要推荐: Apache ECharts

- **集成方式**: 使用 `svelte-echarts` Svelte 封装库。
- **优点**:
    - **功能强大**: 提供极其丰富的图表类型和配置选项。
    - **高度可定制**: 外观、动画、交互等方面均可深度定制，易于实现"美观"需求。
    - **性能优越**: 对大数据量和动态数据有良好支持。
    - **声明式API**: 配置直观。
    - 良好的社区和文档支持。
- **考虑因素**:
    - 包体积相对较大，但可通过按需引入模块优化。

### 6.2. 备选方案: Chart.js

- **集成方式**: 使用 `svelte-chartjs` Svelte 封装库。
- **优点**:
    - **轻量级**: 包体积较小，适合对性能要求极致的项目。
    - **上手简单**: API 相对简洁，学习曲线平缓。
    - **常用图表齐全**: 满足大部分标准图表需求。
    - 与 Svelte 集成良好。
- **考虑因素**:
    - 高级定制化和复杂图表能力不如 ECharts。
    - 视觉效果的丰富度可能稍逊一筹。

**选型结论**: 优先推荐 **Apache ECharts**，因为它能更好地满足对仪表盘美观度和功能丰富度的要求。如果项目初期需求较为简单或对包体积有严格限制，Chart.js 也是一个不错的选择。

## 7. 数据获取与更新

- 仪表盘数据将通过前端 `services` 调用后端 Tauri 命令从 SQLite 数据库获取。
- 后端 Rust 命令负责执行相应的数据库查询、聚合和格式化操作。
- 数据更新频率可以根据需求设定（例如：页面加载时获取，或设置定时刷新机制）。

## 8. 未来增强

- **下钻功能**: 点击图表元素可查看更详细的数据或跳转到相关项目详情。
- **自定义报告**: 允许用户选择指标和图表，生成自定义报告。
- **告警与通知**: 对特定指标阈值设置告警（例如：项目长时间未更新）。
- **更复杂的时间序列分析**: 例如，患者招募速率趋势。
- **用户权限控制**: 根据用户角色显示不同的仪表盘视图和数据。

## 9. 前端开发实施细节 (SvelteKit + ECharts/Chart.js)

本节提供仪表盘前端开发的具体指南，旨在实现美观、一目了然的界面。

### 9.1. 页面与组件结构

- **仪表盘主页面**:
    - 路径: `src/routes/dashboard/project-management/+page.svelte` (建议新建此路由)
    - 职责: 整体布局、加载数据、组织和渲染各个图表及筛选组件。
- **可复用图表组件** (`src/lib/components/dashboard/charts/`):
    - `BarChart.svelte`: 封装柱状图/条形图逻辑 (基于 `svelte-echarts` 或 `svelte-chartjs`)。
    - `PieChart.svelte`: 封装饼图/环形图逻辑。
    - `LineChart.svelte`: 封装折线图逻辑。
    - `KpiCard.svelte`: 用于展示单个关键指标的卡片组件。
    - `DataTable.svelte`: 用于展示表格数据。
    - _设计原则_: 每个组件应接收 `options` (图表配置) 和 `data` 作为 props，并处理图表的初始化和更新。
- **筛选器组件** (`src/lib/components/dashboard/filters/`):
    - `DateRangeFilter.svelte`: 日期范围选择。
    - `ProjectStatusFilter.svelte`: 项目状态下拉筛选。
    - `SponsorFilter.svelte`: 申办方筛选。
    - _设计原则_: 筛选组件通过 Svelte stores 或事件回调与主页面通信，触发数据重新加载或图表更新。

### 9.2. 数据获取与状态管理

- **仪表盘服务 (`src/lib/services/dashboardService.ts`)**:
    - 封装所有与仪表盘相关的后端 Tauri 命令调用。
    - 方法示例: `fetchProjectStatusDistribution()`, `fetchMonthlyNewProjects(params)`, `fetchSubsidyOverview(projectId)`。
- **数据加载**:
    - 在 `+page.svelte` 的 `load` 函数中预加载初始数据，以提升首屏性能。
    - 对于动态更新或依赖筛选器的数据，可以在组件 `onMount` 或通过响应式语句触发 `dashboardService` 调用。
- **状态管理 (Svelte Stores - `src/lib/stores/dashboardStores.ts`)**:
    - `writable` store: 存储从后端获取的原始仪表盘数据集合。
    - `writable` store: 存储当前激活的筛选条件。
    - `derived` stores: 根据原始数据和筛选条件计算出各个图表所需的具体数据和配置。这有助于保持逻辑分离和高效更新。
    - 示例:
      ```typescript
      // dashboardStores.ts
      import { writable, derived } from 'svelte/store';
      
      export const rawDashboardData = writable<any>({}); // 从后端获取的聚合数据
      export const activeFilters = writable<any>({ dateRange: null, status: 'all' });
      
      export const projectStatusChartData = derived(
          [rawDashboardData, activeFilters],
          ([$rawDashboardData, $activeFilters]) => {
              // ... 根据 $rawDashboardData 和 $activeFilters 计算并返回 ECharts/Chart.js 的 option 对象
              return { /* ... ECharts options ... */ };
          }
      );
      ```

### 9.3. 样式与美学 (Tailwind CSS)

- **设计理念**: 追求"美观、一目了然"。界面应简洁、现代，信息层级清晰。
- **配色方案**:
    - 定义清晰的主色、辅色、强调色。可参考 Tailwind CSS 默认调色板或自定义主题。
    - 图表颜色应保持一致性，不同系列使用对比鲜明但和谐的颜色。ECharts 提供丰富的主题和调色板配置。
- **布局**:
    - 使用 CSS Grid 或 Flexbox (通过 Tailwind 类) 构建响应式仪表盘布局。
    - 保证足够的留白，避免信息过于密集。
- **排版**:
    - 清晰的字体层级 (标题、副标题、图表标签、数据文本)。
    - 选择易读性高的无衬线字体。
- **图标**:
    - 使用高质量 SVG 图标 (如 Heroicons, Phosphor Icons) 增强视觉效果和信息表达。可创建为 Svelte 组件。
- **交互反馈**:
    - 筛选器、按钮等交互元素应有明确的 hover, focus, active 状态。
    - 图表应启用工具提示 (tooltips) 显示详细数据。
    - 加载状态：使用骨架屏 (Skeleton) 或加载指示器 (Spinner) 提升用户等待体验。
- **参考**: 可以从 Dribbble、Behance 等设计社区或成熟的 BI 产品 (如 Tableau, PowerBI, Google Data Studio) 获取仪表盘设计灵感。

### 9.4. ECharts/Chart.js 使用要点

- **封装**: 将 ECharts/Chart.js 的初始化和更新逻辑封装在各自的 Svelte 组件内。
- **响应式更新**: 当 `props` (如 `data` 或 `options`) 变化时，组件应能正确更新或重新渲染图表。
    - ECharts: `chart.setOption(newOptions, true);`
    - Chart.js: `chart.data = newData; chart.update();`
- **事件处理**: 如需图表点击事件等交互，在封装组件内监听并派发 Svelte 事件。
- **按需引入 (ECharts)**: 为减小包体积，ECharts 支持按需引入所需图表类型和组件。
  ```typescript
  // 在使用 ECharts 的 Svelte 组件或服务中
  import * as echarts from 'echarts/core';
  import { BarChart, LineChart, PieChart } // ... 等具体图表类型
  from 'echarts/charts';
  import { TitleComponent, TooltipComponent, GridComponent, LegendComponent } // ... 等组件
  from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';

  echarts.use([
    TitleComponent, TooltipComponent, GridComponent, LegendComponent,
    BarChart, LineChart, PieChart,
    CanvasRenderer
  ]);
  ```

## 10. 后端开发实施细节 (Rust + Tauri)

### 10.1. Tauri 命令 (`src-tauri/src/commands/dashboard_commands.rs`)

- **模块化**: 将所有仪表盘相关的数据查询命令组织在此新模块中。
- **命名规范**: 命令名应清晰反映其功能，如 `get_project_count_by_status`, `get_monthly_started_projects`, `get_project_subsidy_summary`。
- **参数**: 接收前端传递的筛选参数 (如日期范围、状态ID列表等)，定义为强类型 Rust struct。
- **返回值**: 返回结构化的数据，优化为前端图表直接使用或稍作转换即可使用。通常是 `Result<Vec<ChartDataPoint>, AppError>`，其中 `ChartDataPoint` 是一个自定义的 struct。
  ```rust
  // dashboard_commands.rs
  use crate::models::dashboard::ChartDataPoint; // 假设定义
  use crate::AppState; // 包含数据库连接池等
  use crate::error::AppError;
  
  #[derive(serde::Deserialize)]
  pub struct DateRangeParams {
      start_date: Option<String>,
      end_date: Option<String>,
  }

  #[tauri::command]
  pub async fn get_project_count_by_status(
      state: tauri::State<'_, AppState>,
      // params: DateRangeParams, // 如有需要
  ) -> Result<Vec<ChartDataPoint>, AppError> {
      // ... 调用 dashboard_service ...
      // Ok(vec![
      //     ChartDataPoint { name: "进行中".to_string(), value: 10.0 },
      //     ChartDataPoint { name: "已完成".to_string(), value: 25.0 },
      // ])
      todo!() // 替换为实际逻辑
  }
  ```

### 10.2. 服务层 (`src-tauri/src/services/dashboard_service.rs`)

- **职责**: 包含仪表盘数据聚合和业务逻辑。调用仓储层获取原始数据，然后进行处理。
- **方法**: 与 Tauri 命令对应，如 `fetch_project_count_by_status(&self, db_pool: &SqlitePool /*, params: DateRangeParams */) -> Result<Vec<ChartDataPoint>, AppError>`。
- **数据转换**: 将从数据库查询到的结果转换为前端图表易于消费的格式。

### 10.3. 数据聚合与 SQL 查询 (在 `repositories` 层)

- **高效查询**: 利用 SQL 的聚合函数 (`COUNT`, `SUM`, `AVG`), `GROUP BY` 子句, `JOIN` 操作 (特别是与 `dictionary_items` 表关联以获取可读名称)。
- **示例 SQL 片段** (概念性，具体需根据 `sqlx` 语法调整):
  ```sql
  -- 项目状态分布
  SELECT di.item_value as status_name, COUNT(p.project_id) as project_count
  FROM projects p
  JOIN dictionary_items di ON p.project_status_item_id = di.item_id
  -- WHERE p.created_at BETWEEN ? AND ? -- 如果有日期筛选
  GROUP BY di.item_value;

  -- 每月新启动项目
  SELECT strftime('%Y-%m', project_start_date) as month, COUNT(project_id) as count
  FROM projects
  GROUP BY month
  ORDER BY month;
  ```
- **仓储层方法**: 在 `src-tauri/src/repositories/project_management_repository.rs` (或新建的 `dashboard_repository.rs`) 中实现这些查询。

### 10.4. 数据模型 (`src-tauri/src/models/dashboard.rs`)

- 定义用于仪表盘数据传输的 Rust struct，如 `ChartDataPoint`。
  ```rust
  // models/dashboard.rs
  #[derive(serde::Serialize, Debug)]
  pub struct ChartDataPoint {
      pub name: String,  // 例如: 类别名称, x轴标签
      pub value: f64,    // y轴数值
      // pub series: Option<String>, // 可选的系列名称，用于堆叠图等
  }
  ```

### 10.5. 性能与错误处理

- **异步处理**: 所有数据库查询和潜在的耗时操作都应使用 `async/await`。
- **连接池**: 正确使用 `sqlx::SqlitePool`。
- **错误处理**: 统一使用项目中定义的 `AppError` 类型，确保错误信息能清晰传递到前端。
- **日志**: 在关键数据获取和处理步骤添加日志记录 (使用 `log` crate)。

## 11. 仪表盘开发涉及文件清单

以下列出了在开发此项目管理仪表盘时主要涉及到的前端和后端文件：

### 11.1. 前端文件 (SvelteKit)

-   **仪表盘主页面**:
    -   `src/routes/dashboard/project-management/+page.svelte`
-   **可复用图表组件** (位于 `src/lib/components/dashboard/charts/` 目录下):
    -   `src/lib/components/dashboard/charts/BarChart.svelte`
    -   `src/lib/components/dashboard/charts/PieChart.svelte`
    -   `src/lib/components/dashboard/charts/LineChart.svelte`
    -   `src/lib/components/dashboard/charts/KpiCard.svelte`
    -   `src/lib/components/dashboard/charts/DataTable.svelte`
-   **筛选器组件** (位于 `src/lib/components/dashboard/filters/` 目录下):
    -   `src/lib/components/dashboard/filters/DateRangeFilter.svelte`
    -   `src/lib/components/dashboard/filters/ProjectStatusFilter.svelte`
    -   `src/lib/components/dashboard/filters/SponsorFilter.svelte`
-   **数据服务**:
    -   `src/lib/services/dashboardService.ts`
-   **状态管理 (Svelte Stores)**:
    -   `src/lib/stores/dashboardStores.ts`

### 11.2. 后端文件 (Rust + Tauri)

-   **Tauri 命令**:
    -   `src-tauri/src/commands/dashboard_commands.rs`
-   **服务层**:
    -   `src-tauri/src/services/dashboard_service.rs`
-   **仓储层 (数据访问)**:
    -   `src-tauri/src/repositories/project_management_repository.rs` (或可能新建的 `src-tauri/src/repositories/dashboard_repository.rs`)
-   **数据模型**:
    -   `src-tauri/src/models/dashboard.rs`

### 11.3. 辅助提示

当后续需要基于此仪表盘文档进行代码读取、分析或修改时，请重点关注以上列出的文件。这些文件是实现仪表盘功能的核心。

## 12. 仪表盘增强功能

### 12.1. 多视图仪表盘

仪表盘现在支持五个主要视图：

- **概览视图** - 核心项目指标和分布图表
- **财务指标** - 补助金额、类型分布和项目排行
- **人员分析** - 人员角色分布、工作负荷和PI统计
- **时间线分析** - 项目启动趋势、持续时间和即将到期项目
- **申办方分析** - 申办方项目分布和状态筛选

### 12.2. 申办方分析功能实现

#### 12.2.1. 功能概述

申办方分析功能将原本在概览视图中的申办方项目分布单独作为专门的分析页面，提供更详细的申办方数据分析。

#### 12.2.2. 核心功能

1. **项目状态筛选功能**
   - 支持四种项目状态筛选：
     - 未启动 (ID: 37)
     - 在研 (ID: 38)
     - 已结束 (ID: 39)
     - 暂停中 (ID: 40)
   - 支持多选筛选
   - 实时更新申办方数据

2. **申办方项目数量展示**
   - **申办方项目数量分布图**: 使用柱状图显示各申办方的项目数量
   - **申办方项目数量详情表格**: 包含申办方名称、项目数量、占比

#### 12.2.3. 技术实现

**前端实现** (`src/routes/dashboard/project-management/+page.svelte`):

```typescript
// 申办方分析数据类型定义(简化版)
let sponsorMetrics: {
  sponsorDistribution: any[];
} = {
  sponsorDistribution: []
};

// 项目状态筛选
let selectedProjectStatusIds: number[] = [];
let projectStatusOptions: { id: number; name: string }[] = [
  { id: 37, name: '未启动' },
  { id: 38, name: '在研' }, 
  { id: 39, name: '已结束' },
  { id: 40, name: '暂停中' }
];

// 处理项目状态筛选变更
function handleProjectStatusFilterChange() {
  // 更新筛选条件并重新加载数据
}
```

**UI组件**:
- 使用 `Briefcase` 图标标识申办方分析tab
- 项目状态筛选器采用多选复选框形式
- 响应式设计，支持深色模式
- 良好的视觉层级和用户体验

**数据集成**:
- 复用现有的后端申办方服务 `getSponsorDistribution()`
- 集成申办方图表配置 `$sponsorChartOptions`
- 实现数据加载和错误处理机制
- 集成项目状态字典数据，支持按状态筛选

#### 12.2.4. 数据流向

1. 前端调用 `dashboardService.getSponsorDistribution()`
2. 后端 `get_sponsor_distribution` 命令
3. `DashboardService.get_sponsor_distribution()` 
4. `DashboardRepository.get_sponsor_distribution()` 执行SQL查询
5. 返回 `SponsorDistribution` 结构数据

#### 12.2.5. 技术特点

1. **模块化设计**
   - 独立的申办方分析视图
   - 可复用的KPI卡片组件
   - 统一的图表配置管理

2. **数据处理**
   - 基于项目状态筛选条件动态获取申办方分布数据
   - 实时响应筛选条件变化
   - 完整的加载状态和错误处理

3. **用户体验**
   - 清晰的信息层级
   - 一致的视觉设计
   - 良好的交互反馈

#### 12.2.6. 实现状态

- ✅ 前端编译通过
- ✅ UI组件正常渲染
- ✅ 数据流集成完成
- ✅ 已完成，可投入使用

#### 12.2.7. 使用方式

1. 启动应用：`npm run tauri dev`
2. 导航到项目管理仪表盘
3. 点击"申办方分析"tab
4. 使用项目状态筛选功能选择感兴趣的项目状态
5. 查看申办方项目数量分布和详细信息

### 12.3. 增强的 KPI 卡片

- 支持多种数据格式（货币、百分比、持续时间）
- 添加了副标题和图标
- 改进的颜色主题和悬停效果
- 响应式设计

### 12.4. 新的图表组件

创建了 `MetricCard` 组件，支持：
- 饼图、柱状图、折线图和表格视图
- 统一的错误处理和加载状态
- 可配置的图表高度和样式

### 12.5. 改进的视觉设计

- 使用 Tailwind CSS 的现代化设计系统
- 支持明暗主题切换
- 改进的间距和布局
- 一致的颜色方案和阴影效果

### 12.6. 新增的后端服务

1. **财务指标服务** (`get_financial_metrics`)
   - 计算总补助金额和平均值
   - 获取补助类型分布
   - 生成项目补助排行

2. **人员指标服务** (`get_personnel_metrics`)
   - 统计总人员数和角色分布
   - 分析人员工作负荷
   - PI分布统计

3. **时间线指标服务** (`get_timeline_metrics`)
   - 项目启动时间分布
   - 平均项目持续时间
   - 即将到期项目提醒

## 13. 扩展建议

### 13.1. 申办方分析功能增强

1. **筛选功能增强**
   - 添加更多筛选维度（疾病领域、项目阶段等）
   - 支持日期范围筛选
   - 添加筛选条件保存功能

2. **交互功能**
   - 申办方详情页面链接
   - 点击申办方名称查看关联项目列表
   - 数据导出功能

3. **展示优化**
   - 添加排序功能（按项目数量、申办方名称等）
   - 支持分页显示大量申办方
   - 增加数据汇总统计

### 13.2. 其他扩展建议

1. **图表库集成**
   - 集成 ECharts 或 Chart.js 实现真实的图表渲染
   - 添加交互功能（缩放、筛选、钻取）

2. **实时数据**
   - 实现数据自动刷新
   - 添加 WebSocket 支持实时更新

3. **导出功能**
   - 支持 PDF/Excel 导出
   - 添加打印友好的样式

4. **高级筛选**
   - 添加更多筛选维度
   - 支持自定义日期范围
   - 保存筛选配置

5. **用户个性化**
   - 支持自定义仪表盘布局
   - 添加收藏指标功能
   - 个人化的默认视图设置

## 相关文档

- [功能使用手册 - 数据分析仪表盘](../FUNCTIONAL_MANUAL.md#6-数据分析仪表盘)
- [项目管理开发文档](./项目管理开发文档.md)

