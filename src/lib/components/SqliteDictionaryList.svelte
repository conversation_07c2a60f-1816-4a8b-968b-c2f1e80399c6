<script lang="ts">
  import { sqliteDictionaryService, type SqliteDict, type SqliteDictQuery } from '$lib/services/sqliteDictionaryService';
  import { Button } from '$lib/components/ui/button';
  import { onMount } from 'svelte';
  import { fade } from 'svelte/transition';
  import { invoke } from '@tauri-apps/api/core';

  // 状态管理
  let dicts = $state<SqliteDict[]>([]);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let query = $state<SqliteDictQuery>({});
  let showSearchForm = $state(false);

  // 分页
  let currentPage = $state(1);
  let pageSize = $state(20);
  let totalItems = $state(0);

  // 排序
  let sortField = $state<string>('name');
  let sortDirection = $state<'asc' | 'desc'>('asc');

  // 计算分页数据
  let paginatedDicts = $derived(dicts.slice((currentPage - 1) * pageSize, currentPage * pageSize));
  let totalPages = $derived(Math.ceil(dicts.length / pageSize));

  // 加载字典列表
  async function loadDicts() {
    isLoading = true;
    error = null;

    try {
      dicts = await sqliteDictionaryService.getAllDicts();
      totalItems = dicts.length;
      sortDicts();
    } catch (err: any) {
      error = err.message || '加载字典失败';
      console.error('加载字典失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 定义响应类型
  interface DictResponse {
    success: boolean;
    error?: string;
    data?: any;
  }

  // 确认对话框状态
  let showConfirmDialog = $state(false);
  let confirmDialogTitle = $state('');
  let confirmDialogMessage = $state('');
  let confirmDialogCallback = $state<(() => void) | null>(null);

  // 显示确认对话框
  function showConfirm(title: string, message: string, callback: () => void) {
    confirmDialogTitle = title;
    confirmDialogMessage = message;
    confirmDialogCallback = callback;
    showConfirmDialog = true;
  }

  // 删除字典
  async function deleteDict(id: number) {
    console.log(`开始删除字典，ID = ${id}, 类型 = ${typeof id}`);

    // 使用自定义确认对话框
    showConfirm(
      '删除字典',
      '确定要删除此字典吗？此操作不可撤销。',
      async () => {
        console.log('用户确认删除字典');

        try {
          // 直接使用 invoke 调用后端命令
          console.log(`准备调用 sqlite_delete_dict 命令，ID = ${id}`);

          // 尝试将 ID 转换为数字
          const numericId = Number(id);
          console.log(`转换后的 ID: ${numericId}, 类型: ${typeof numericId}`);

          const response = await invoke<DictResponse>('sqlite_delete_dict', { id: numericId });
          console.log('调用响应:', response);

          if (response.success) {
            console.log('删除成功，重新加载字典列表');
            // 重新加载字典列表
            await loadDicts();
          } else {
            console.error('删除失败:', response.error);
            error = response.error || '删除字典失败';
          }
        } catch (err: any) {
          console.error('调用失败:', err);
          error = err.message || '删除字典失败';
        }
      }
    );
  }

  // 搜索字典
  async function searchDicts() {
    isLoading = true;
    error = null;
    currentPage = 1; // 重置到第一页

    try {
      dicts = await sqliteDictionaryService.queryDicts(query);
      totalItems = dicts.length;
      sortDicts();
    } catch (err: any) {
      error = err.message || '搜索字典失败';
      console.error('搜索字典失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 清空搜索条件
  function clearSearch() {
    query = {};
    loadDicts();
    showSearchForm = false;
  }

  // 排序字典
  function sortDicts() {
    dicts = [...dicts].sort((a, b) => {
      let aValue = a[sortField as keyof SqliteDict];
      let bValue = b[sortField as keyof SqliteDict];

      // 处理可能为undefined的情况
      if (aValue === undefined) aValue = '';
      if (bValue === undefined) bValue = '';

      // 字符串比较
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // 数字比较
      return sortDirection === 'asc'
        ? (aValue < bValue ? -1 : 1)
        : (bValue < aValue ? -1 : 1);
    });
  }

  // 切换排序
  function toggleSort(field: string) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
    sortDicts();
  }

  // 注意: 此函数将在未来的表格视图中使用

  // 切换页面
  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }

  // 组件挂载时加载字典
  onMount(() => {
    loadDicts();
  });
</script>

<div class="dictionary-list">
  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-4 text-red-800 dark:text-red-200 mb-6" transition:fade>
      <p>{error}</p>
    </div>
  {/if}

  <!-- 字典列表 -->
  <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg shadow-slate-200/50 dark:shadow-slate-900/50 overflow-hidden border border-slate-200 dark:border-slate-700">
    <div class="flex justify-between items-center p-6 border-b border-slate-200 dark:border-slate-700 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-800">
      <div class="flex items-center gap-3">
        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database text-blue-600 dark:text-blue-400"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M3 5v14c0 1.5 3 3 9 3"/><path d="M3 12c0 1.5 3 3 9 3"/><path d="M21 5v4"/><path d="M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16"/><path d="M12 12v4h4"/></svg>
        </div>
        <div>
          <h2 class="text-xl font-bold text-slate-800 dark:text-slate-200">字典列表 (SQLite)</h2>
          <p class="text-xs text-slate-500 dark:text-slate-400 mt-0.5">管理您的字典数据</p>
        </div>
        <span class="text-xs bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full font-semibold">
          {totalItems} 条记录
        </span>
      </div>

      <div class="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          class="flex items-center gap-1"
          onclick={() => showSearchForm = !showSearchForm}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
          {showSearchForm ? '隐藏搜索' : '搜索'}
        </Button>
        <Button variant="outline" size="sm" class="flex items-center gap-1" href="/sqlite-dictionaries/new">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          新建字典
        </Button>
        <Button variant="ghost" size="sm" class="flex items-center gap-1" href="/sqlite-dictionaries/migrate">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database-backup"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M3 5v14c0 1.5 3 3 9 3"/><path d="M3 12c0 1.5 3 3 9 3"/><path d="M21 5v4"/><path d="M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16"/><path d="M12 12v4h4"/></svg>
          数据迁移
        </Button>
      </div>
    </div>

    <!-- 搜索表单 -->
    {#if showSearchForm}
      <div class="border-b border-slate-200 dark:border-slate-700 p-6 bg-slate-50/50 dark:bg-slate-800/50" transition:fade={{duration: 300}}>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label for="dict-name" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">字典名称</label>
            <input
              id="dict-name"
              type="text"
              bind:value={query.name}
              placeholder="输入字典名称"
              class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label for="dict-type" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">字典类型</label>
            <input
              id="dict-type"
              type="text"
              bind:value={query.type_}
              placeholder="输入字典类型"
              class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label for="dict-tags" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">标签</label>
            <input
              id="dict-tags"
              type="text"
              placeholder="输入标签，用逗号分隔"
              class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              oninput={(e) => {
                const value = e.currentTarget.value;
                query.tags = value ? value.split(',').map(tag => tag.trim()).filter(Boolean) : undefined;
              }}
            />
          </div>
        </div>

        <div class="flex justify-end mt-3 gap-2">
          <Button variant="ghost" size="sm" onclick={clearSearch}>
            清空
          </Button>
          <Button size="sm" onclick={searchDicts}>
            搜索
          </Button>
        </div>
      </div>
    {/if}

    {#if isLoading}
      <div class="flex flex-col justify-center items-center p-12">
        <div class="relative">
          <div class="w-12 h-12 border-4 border-slate-200 dark:border-slate-700 rounded-full"></div>
          <div class="absolute top-0 left-0 w-12 h-12 border-4 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin"></div>
        </div>
        <p class="mt-4 text-slate-600 dark:text-slate-400 font-medium">正在加载字典数据...</p>
        <p class="text-xs text-slate-500 dark:text-slate-500 mt-1">请稍候</p>
      </div>
    {:else if dicts.length === 0}
      <div class="flex flex-col items-center justify-center p-12 text-center">
        <div class="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database text-slate-400 dark:text-slate-600"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M3 5v14c0 1.5 3 3 9 3"/><path d="M3 12c0 1.5 3 3 9 3"/><path d="M21 5v4"/><path d="M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16"/><path d="M12 12v4h4"/></svg>
        </div>
        <p class="text-slate-600 dark:text-slate-400 font-medium mb-2">暂无字典数据</p>
        <p class="text-sm text-slate-500 dark:text-slate-500">点击"新建字典"开始创建您的第一个字典</p>
      </div>
    {:else}
      <!-- 卡片视图 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
        {#each paginatedDicts as dict}
          <div class="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden hover:shadow-md hover:shadow-slate-200/30 dark:hover:shadow-slate-900/30 transition-all duration-200 flex flex-col min-h-[140px] group">
            <div class="p-2 border-b border-slate-200 dark:border-slate-700 flex-grow">
              <div class="flex justify-between items-start mb-1">
                <h3 class="text-base font-bold text-slate-900 dark:text-slate-100 truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors leading-tight bg-gradient-to-r from-slate-800 to-slate-700 dark:from-slate-200 dark:to-slate-300 bg-clip-text text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-blue-500 dark:group-hover:from-blue-400 dark:group-hover:to-blue-300">{dict.name}</h3>
                <span class="text-xs bg-blue-100 dark:bg-blue-900/60 text-blue-700 dark:text-blue-300 px-1.5 py-0.5 rounded-full font-semibold shrink-0 ml-1 border border-blue-200 dark:border-blue-800">
                  {dict.id}
                </span>
              </div>

              {#if dict.description}
                <p class="text-xs text-slate-600 dark:text-slate-400 line-clamp-2 leading-tight">{dict.description}</p>
              {/if}

              <div class="flex flex-wrap gap-1 mt-1">
                {#if dict.tags && dict.tags.length > 0}
                  {#each dict.tags as tag}
                    <span class="px-1 py-0.5 text-xs rounded bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300 font-medium">
                      {tag}
                    </span>
                  {/each}
                {/if}
              </div>

              <div class="text-xs text-slate-500 dark:text-slate-400 mt-1 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar"><path d="M8 2v4"/><path d="M16 2v4"/><rect width="18" height="18" x="3" y="4" rx="2" ry="2"/><path d="M3 10h18"/></svg>
                {dict.created_at ? new Date(dict.created_at).toLocaleString() : '-'}
              </div>
            </div>

            <div class="px-2 py-1.5 flex justify-end items-center bg-slate-50 dark:bg-slate-800/50 border-t border-slate-200 dark:border-slate-700 mt-auto">
              <div class="flex gap-0.5">
                <a
                  href={`/sqlite-dictionaries/${dict.id}`}
                  class="inline-flex items-center gap-0.5 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 px-1.5 py-0.5 rounded transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye"><path d="M2.5 5A2.5 2.5 0 0 1 5 2.5c.33 0 .64.1.92.28L10 5.5l4.08-2.72c.28-.18.59-.28.92-.28A2.5 2.5 0 0 1 17 5.5c0 .33-.1.64-.28.92L13.5 10l2.72 4.08c.18.28.28.59.28.92A2.5 2.5 0 0 1 14 17.5c-.33 0-.64-.1-.92-.28L10 14.5l-4.08 2.72c-.28.18-.59.28-.92.28A2.5 2.5 0 0 1 2.5 15c0-.33.1-.64.28-.92L6.5 10 3.78 5.92A2.5 2.5 0 0 1 2.5 5Z"/></svg>
                  查看
                </a>
                <a
                  href={`/sqlite-dictionaries/${dict.id}/edit`}
                  class="inline-flex items-center gap-0.5 text-xs font-medium text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 px-1.5 py-0.5 rounded transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-edit"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5Z"/></svg>
                  编辑
                </a>
                <button
                  type="button"
                  class="inline-flex items-center gap-0.5 text-xs font-medium text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 px-1.5 py-0.5 rounded transition-colors"
                  onclick={() => {
                    console.log(`列表中删除字典按钮被点击，ID = ${dict.id || 0}`);
                    deleteDict(dict.id || 0);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                  删除
                </button>
              </div>
            </div>
          </div>
        {/each}
      </div>

      <!-- 分页控件 -->
      {#if totalPages > 1}
        <div class="flex justify-between items-center p-6 border-t border-slate-200 dark:border-slate-700 bg-slate-50/30 dark:bg-slate-800/30">
          <div class="text-sm text-slate-600 dark:text-slate-400 font-medium">
            显示 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalItems)} 条，共 {totalItems} 条
          </div>

          <div class="flex gap-1">
            <button
              class="w-8 h-8 flex items-center justify-center rounded-md border border-slate-300 dark:border-slate-600 {currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-100 dark:hover:bg-slate-700'}"
              disabled={currentPage === 1}
              onclick={() => goToPage(currentPage - 1)}
              aria-label="上一页"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"/></svg>
            </button>

            {#each Array(totalPages).fill(0).map((_, i) => i + 1) as page}
              {#if page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)}
                <button
                  class="w-8 h-8 flex items-center justify-center rounded-md border {page === currentPage ? 'bg-blue-50 border-blue-300 text-blue-600 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400' : 'border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700'}"
                  onclick={() => goToPage(page)}
                >
                  {page}
                </button>
              {:else if page === currentPage - 2 || page === currentPage + 2}
                <span class="w-8 h-8 flex items-center justify-center">...</span>
              {/if}
            {/each}

            <button
              class="w-8 h-8 flex items-center justify-center rounded-md border border-slate-300 dark:border-slate-600 {currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-100 dark:hover:bg-slate-700'}"
              disabled={currentPage === totalPages}
              onclick={() => goToPage(currentPage + 1)}
              aria-label="下一页"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>
            </button>
          </div>
        </div>
      {/if}
    {/if}
  </div>

  <!-- 自定义确认对话框 -->
  {#if showConfirmDialog}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold mb-2">{confirmDialogTitle}</h3>
        <p class="text-slate-600 dark:text-slate-300 mb-6">{confirmDialogMessage}</p>
        <div class="flex justify-end gap-3">
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-slate-200 text-slate-800 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600"
            onclick={() => showConfirmDialog = false}
          >
            取消
          </button>
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600"
            onclick={() => {
              showConfirmDialog = false;
              if (confirmDialogCallback) {
                confirmDialogCallback();
              }
            }}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
