# 字典数据库开发文档

## 概述

本文档描述了临床研究项目管理系统中的字典数据库结构和内容，为开发过程中使用字典数据提供参考。

## 数据库表结构

### 1. dictionaries 表

字典主表，存储字典的基本信息。

| 字段名 | 类型 | 是否为空 | 默认值 | 描述 |
|--------|------|----------|--------|------|
| id | INTEGER | NOT NULL | - | 主键，自增ID |
| mongo_oid | TEXT | NULL | - | MongoDB对象ID（用于数据同步） |
| name | TEXT | NOT NULL | - | 字典名称 |
| description | TEXT | NULL | - | 字典描述 |
| type | TEXT | NOT NULL | 'list' | 字典类型 |
| created_at | TEXT | NOT NULL | - | 创建时间 |
| updated_at | TEXT | NOT NULL | - | 更新时间 |
| version | INTEGER | NOT NULL | 1 | 版本号 |

### 2. dictionary_items 表

字典项表，存储具体的字典项数据。

| 字段名 | 类型 | 是否为空 | 默认值 | 描述 |
|--------|------|----------|--------|------|
| item_id | INTEGER | NOT NULL | - | 主键，自增ID |
| dictionary_id | INTEGER | NOT NULL | - | 关联字典ID |
| item_key | TEXT | NOT NULL | - | 字典项键值 |
| item_value | TEXT | NOT NULL | - | 字典项显示值 |
| item_description | TEXT | NULL | - | 字典项描述 |
| status | TEXT | NOT NULL | 'active' | 状态（active/inactive） |

### 3. dictionary_tags 表

字典标签关联表，用于字典和标签的多对多关联。

| 字段名 | 类型 | 是否为空 | 默认值 | 描述 |
|--------|------|----------|--------|------|
| dictionary_id | INTEGER | NOT NULL | - | 字典ID（复合主键） |
| tag_id | INTEGER | NOT NULL | - | 标签ID（复合主键） |

## 字典分类及详细内容

### 1. 基础信息类字典

#### 1.1 办理方 (sponsor)
- **描述**: 临床试验的申办方公司列表
- **条目数**: 32
- **主要内容包括**: 罗氏制药、艾力斯、南新制药、健康元药业集团等国内外制药公司

#### 1.2 招募公司 (recruitment_company)
- **描述**: 招募公司列表
- **条目数**: 13
- **主要内容包括**: 恒瑞医药、迈威生物、正大天晴、奥菲斯等

#### 1.3 疾病 (disease)
- **描述**: 呼吸系统相关疾病标签
- **条目数**: 13
- **主要内容包括**: 
  - 哮喘 (asthma)
  - 慢阻肺 (COPD)
  - 肺癌 (lung cancer)
  - 社区获得性肺炎
  - 院内获得性肺炎
  - 难治性咳嗽
  - 睡眠呼吸暂停等

### 2. 研究相关字典

#### 2.1 研究分期 (research_phase)
- **描述**: 临床试验的不同阶段
- **条目数**: 8
- **内容包括**:
  - I期 (phase1)
  - II期 (phase2)
  - III期 (phase3)
  - IV期 (phase4)
  - I/II期 (phase1_2)
  - II/III期 (phase2_3)
  - 真实世界研究 (ris)
  - 其他 (other)

#### 2.2 研究阶段 (research_stage)
- **描述**: 项目状态的不同阶段
- **条目数**: 4
- **内容包括**:
  - 未启动 (not_started)
  - 在研 (in_progress)
  - 暂停中 (pause)
  - 已结束 (completed)

#### 2.3 研究角色 (research_role)
- **描述**: 临床研究中的各种角色
- **条目数**: 20
- **主要内容包括**:
  - 主要研究者 (PI)
  - 协调研究医生 (SubI)
  - 监察员 (CRA)
  - 临床协调员 (CRC)
  - 研究医生 (Research_doctor)
  - 研究护士 (Research_Nurse)
  - 药品管理员 (Drug_administrator)
  - 质控员 (Quality_controller)
  - 肺功能师 (Pulmonary_function_specialist)
  - CT技师 (CT_technician)
  - 盲态/非盲态相关角色等

### 3. 用户管理字典

#### 3.1 用户角色 (user_role)
- **描述**: 系统用户角色
- **条目数**: 5
- **内容包括**: CRA、CRC、医生、护士、技师

### 4. 药物相关字典

#### 4.1 药物分类 (drug_category)
- **描述**: 药物分类（化学药物、生物制品等）
- **条目数**: 8

#### 4.2 药物类型 (drug_type)
- **描述**: 药物类型（研究药物、对照药物、安慰剂）
- **条目数**: 3

#### 4.3 药物作用机制 (Drug_mechanism_of_action)
- **描述**: 药物作用机制
- **条目数**: 0（暂无数据）

#### 4.4 用药方法 (administration_method)
- **描述**: 药物使用方法
- **条目数**: 11
- **内容包括**:
  - 口服 (oral)
  - 注射 (injection)
  - 静脉注射 (intravenous)
  - 肌肉注射 (intramuscular)
  - 皮下注射 (subcutaneous)
  - 吸入 (inhalation)
  - 舌下含服 (sublingual)
  - 直肠给药 (rectal)
  - 外用 (topical)
  - 透皮给药 (transdermal)
  - 其他 (other)

#### 4.5 用药频率 (administration_frequency)
- **描述**: 药物使用频率
- **条目数**: 13
- **内容包括**:
  - 每日一次 (qd)
  - 每日两次 (bid)
  - 每日三次 (tid)
  - 每日四次 (qid)
  - 每6小时一次 (q6h)
  - 每8小时一次 (q8h)
  - 每12小时一次 (q12h)
  - 每周一次 (weekly)
  - 每两周一次 (biweekly)
  - 每月一次 (monthly)
  - 必要时 (prn)
  - 单次给药 (single_dose)
  - 其他 (other)

#### 4.6 给药途径 (administration_route)
- **描述**: 药物给药途径
- **条目数**: 17
- **内容包括**: 口服、静脉、肌肉、皮下等各种给药途径

### 5. 补贴相关字典

#### 5.1 补贴类型 (subsidy_type)
- **描述**: 补贴的类型
- **条目数**: 14
- **主要内容包括**:
  - 交通补贴 (Transportation_subsidy)
  - 营养补贴 (Nutritional_subsidies)
  - 误工和住宿补贴 (Lost_work_and_accommodation_allowance)
  - 实验室检查补贴 (Laboratory_test_subsidy)
  - 肺功能检查补贴 (Subsidy_for_lung_function_tests)
  - 普通采血补贴 (Ordinary_blood_collection_subsidy)
  - PK采血补贴 (PK_blood_collection_subsidy)
  - PD采血补贴 (PD_blood_collection_subsidy)
  - 免疫原性采血补贴 (Immunogenic_blood_collection_subsidy)
  - 背景用药补贴 (Background_medication_subsidy)
  - 依从性补贴 (Compliance_subsidy)
  - 青少年父母陪同 (Teenagers_accompanied_by_their_parents)
  - FeNO
  - IgE

#### 5.2 补贴的单位 (subsidy_unit)
- **描述**: 补贴的计算单位
- **条目数**: 11
- **内容包括**:
  - 天 (day)
  - 次 (every_time, visit)
  - 一次性 (disposable)
  - 项 (item)
  - 公里 (km)
  - 月 (month)
  - 人次 (person)
  - 时间 (time)
  - 年 (year)
  - 其他 (other)

### 6. 招募相关字典

#### 6.1 招募状态 (recruitment_status)
- **描述**: 项目招募状态
- **条目数**: 3
- **内容包括**:
  - 招募中 (Recruiting_in_progress)
  - 结束招募 (Stop_recruiting)
  - 暂停招募 (Suspend_recruitment)

## 数据访问方式

### 1. 后端Rust访问

系统使用Repository模式进行数据访问，主要的Repository包括：

- `DictionaryRepository`: 处理字典的CRUD操作
- `DictionaryItemRepository`: 处理字典项的CRUD操作

### 2. 前端访问

前端通过Tauri命令调用后端API：

- `get_dictionaries`: 获取字典列表
- `get_dictionary_items`: 获取指定字典的项
- `create_dictionary`: 创建新字典
- `update_dictionary`: 更新字典
- `delete_dictionary`: 删除字典

## 字典管理功能

### 1. 字典管理界面
系统提供字典管理界面，支持：
- 字典的增删改查
- 字典项的增删改查
- 字典版本管理
- 字典状态管理

### 2. 字典同步
支持与MongoDB进行数据同步：
- `mongo_oid`字段用于关联MongoDB记录
- 版本控制确保数据一致性

## 开发建议

### 1. 字典使用
- 在前端使用字典时，建议使用`item_key`作为程序内部标识
- 使用`item_value`作为用户界面显示
- 注意检查字典项的`status`字段，只使用状态为`active`的项

### 2. 字典扩展
- 添加新字典时，建议先评估是否可以扩展现有字典
- 遵循现有的命名规范
- 提供清晰的字典描述

### 3. 性能考虑
- 字典数据相对稳定，可以考虑在前端缓存
- 频繁访问的字典可以预加载到内存中

## 版本历史

- v1.0: 初始版本，包含基础字典结构
- v1.1: 添加药物相关字典
- v1.2: 完善补贴相关字典

## 维护说明

- 字典的修改需要谨慎，避免影响现有功能
- 建议定期清理无用的字典项
- 重要字典的修改需要记录变更历史

---

*文档创建时间: 2025-09-06*
*最后更新: 2025-09-06*