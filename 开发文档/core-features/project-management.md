# 项目管理开发文档

## 文档说明

> **注意**: 本文档提供项目管理功能的详细技术实现信息。用户功能说明请参考 [功能使用手册](../../FUNCTIONAL_MANUAL.md#1-项目管理)。

## 概述

本文档详细描述项目管理模块的技术架构、代码组织和实现细节，主要面向开发人员。项目管理模块是系统的核心功能，实现了完整的临床研究项目生命周期管理。

## 前端实现 (src/routes/projects/)

### 核心页面与组件

- **项目列表页:** @`src/routes/projects/+page.svelte`
  - 功能：显示项目列表，支持搜索、筛选和分页。提供创建新项目的入口，以及导航到项目详情/编辑页和**项目入排标准配置页**的入口。
  - **操作列:** 每行提供以下操作按钮：
    - **编辑:** 跳转到 [`src/routes/projects/[id]/edit/+page.svelte`](mdc:src/routes/projects/[id]/edit/+page.svelte)。
    - **配置:** 跳转到 [`src/routes/projects/[projectId]/criteria/+page.svelte`](mdc:src/routes/projects/[projectId]/criteria/+page.svelte) (参考 @@inclusion_exclusion_rules.mdc)。
    - **文件夹:** 调用 `fileSystemService.openFolder` 打开项目路径。
    - **删除:** 调用 `projectManagementService.deleteProject` 并显示确认对话框。
- **新建项目页:** @`src/routes/projects/new/+page.svelte`
  - **功能:** 提供表单用于创建新的项目。
  - **实现细节:** (见下文 `ProjectForm.svelte` 及相关组件)
    - 页面加载时提供模板选择。
    - 使用核心表单组件 @`src/lib/components/project/ProjectForm.svelte`。
    - 调用 @`src/lib/services/projectManagementService.ts` 的 `saveProjectWithDetails` 保存数据 (此时 `project_id` 为空)。
- **项目详情/编辑页:** (动态路由，例如 [`src/routes/projects/[id]/+page.svelte`](mdc:src/routes/projects/[id]/+page.svelte))
  - **功能:** 显示单个项目的详细信息，并允许用户编辑。
  - **加载流程:**
    - 页面加载时，从 URL 参数获取 `projectId`。
    - 调用前端服务 `projectManagementService.getProjectDetails(projectId)`。
    - 前端服务调用后端 Tauri 命令 `get_project_details` 获取项目完整数据。
    - 将获取到的 `projectDetails` 数据传递给核心表单组件 `ProjectForm.svelte` 进行渲染。
  - **编辑与保存流程:**
    - **复用表单组件:** 使用与"新建项目"页**相同**的核心表单组件 @`src/lib/components/project/ProjectForm.svelte` 来显示和修改数据。
    - **触发保存:** 用户点击表单中的"保存项目"按钮，调用本页面 (`[id]/+page.svelte`) 中定义的保存处理函数 (`onSave` prop)。
    - **页面处理:** 保存处理函数获取更新后的 `projectDetails` (包含 `project_id`)，调用 `sanitizeProjectData` (@`src/lib/utils/projectDataUtils.ts`)。
    - **复用保存服务:** 调用**相同**的前端服务方法 `projectManagementService.saveProjectWithDetails(updatedProjectDetails)`。
    - **复用后端命令:** 前端服务调用**相同**的后端 Tauri 命令 `save_project_with_details`。
    - **后端处理 (更新):** 后端仓储层 `project_management_repository.save_project_with_details` 方法检测到 `project_id` 存在，执行**更新**逻辑：在事务中删除旧的关联数据，更新 `projects` 主表记录，插入新的关联数据。
- **核心表单组件:** @`src/lib/components/project/ProjectForm.svelte`
  - **功能:** 作为新建和编辑页面的核心 UI，负责展示和管理项目数据的输入。
  - **结构:** 通过 `activeSection` 状态控制显示以下四个子组件之一，并通过进度条和导航按钮控制流程：
      - **基本信息:** @`ProjectBasicInfo.svelte`
        - 功能: 处理项目核心字段（名称、简称、状态等）和申办方列表 (`projectDetails.project`, `projectDetails.sponsors`)。
        - 交互: 标准输入、下拉菜单（疾病、分期、状态等）、申办方搜索与选择。
        - 数据获取: 使用 `sqliteDictionaryService` 获取下拉菜单所需的字典项。
      - **研究药物:** @`ProjectDrugs.svelte`
        - 功能: 管理研究药物和药物分组 (`projectDetails.research_drugs`, `projectDetails.drug_groups`)。
        - 交互: 对话框添加/删除药物，对话框添加/删除分组。
        - 特殊逻辑: 新建项目时使用 `localStorage` 暂存数据。
      - **研究人员:** @`ProjectPersonnel.svelte`
        - 功能: 管理人员角色分配 (`projectDetails.personnel`)。
        - 交互: 单人/批量添加模式，员工搜索，角色选择。
        - 数据获取: `staffService` 获取员工，`sqliteDictionaryService` 获取角色字典。
      - **补贴信息:** @`ProjectSubsidies.svelte`
        - 功能: 管理补贴方案和具体补贴项 (`projectDetails.subsidy_schemes`, `projectDetails.subsidies`)。
        - 交互: 对话框添加/编辑/删除补贴项和方案，方案可关联补贴项。
        - 数据获取: `sqliteDictionaryService` 获取补贴类型和单位字典。

### 前端服务

- **项目管理服务:** @`src/lib/services/projectManagementService.ts`
  - **核心方法:**
    - `getProjectDetails(projectId)`: 调用后端 `get_project_details` 命令获取单个项目的完整信息（用于编辑页加载）。
    - `saveProjectWithDetails(projectDetails)`: 调用后端 `save_project_with_details` 命令，用于**新建** (无 `project_id`) 和**更新** (有 `project_id`) 项目。
    - `getProjectsList(...)`: 调用后端 `get_projects_list` 获取项目列表（用于列表页）。
    - `deleteProject(projectId)`: 调用后端 `pm_delete_project` 删除项目。
- **其他相关服务:**
  - @`src/lib/services/sqliteDictionaryService.ts`: 获取各种下拉菜单所需的字典数据。
  - @`src/lib/services/staffService.ts`: 搜索和获取员工信息（用于人员分配）。

## 后端交互 (Tauri Commands)

主要通过 @`src-tauri/src/commands/project_management_commands.rs` 定义的命令与前端交互：

- `get_projects_list`: 获取项目列表（支持分页、过滤）。
- `get_project_details`: 获取单个项目及其所有关联数据 (用于编辑页加载)。
- `save_project_with_details`: **核心命令**，接收 `ProjectWithDetails` 对象，根据 `project_id` 是否存在智能处理**新建或更新**操作。调用仓储层的同名方法。
- `pm_delete_project`: 删除项目。
- `init_project_management_tables`, `check_database_tables`, `reset_database_tables`: 数据库管理相关命令。

(注意: @`src-tauri/src/commands/unified/project_commands.rs` 中的命令似乎是另一套实现或旧实现，当前分析主要基于 `project_management_commands.rs`)

## 后端核心逻辑 (仓储层)

- @`src-tauri/src/repositories/project_management_repository.rs`: 包含与数据库交互的具体实现。
  - `get_project_details(projectId)`: 查询 `projects` 表及所有关联表，组装 `ProjectWithDetails` 返回。
  - `save_project_with_details(projectDetails)`: 
    - **判断操作**: 检查 `projectDetails.project.project_id`。
    - **更新逻辑 (ID 存在)**: 启动事务 -> 删除所有旧关联记录 -> 更新 `projects` 表 -> 插入所有新关联记录 -> 提交事务。
    - **新建逻辑 (ID 不存在)**: 启动事务 -> 插入 `projects` 表 (生成新 ID) -> 插入所有关联记录 (使用新 ID) -> 提交事务。

## 数据库结构

> **参考**: 完整的数据库结构文档请查看工作区规则中的数据库文档。

项目管理功能涉及以下主要数据库表：

### 核心表
- **`projects`**: 项目主表，存储项目基本信息
- **`project_sponsors`**: 项目申办方关联表
- **`research_drugs`**: 研究药物信息
- **`drug_groups`**: 药物分组信息
- **`project_personnel_roles`**: 项目人员角色分配
- **`subsidies`**: 补贴项目明细
- **`subsidy_schemes`**: 补贴方案
- **`scheme_included_subsidies`**: 补贴方案与补贴项关联表

### 关联表
- **`dictionary_items`**: 字典条目表，提供下拉选项数据
- **`staff`**: 人员信息表
- **`project_criteria`**: 项目入排标准规则（详见入排标准开发文档）

### 数据库位置
- SQLite 数据库: `/Users/<USER>/我的文档/sqlite/peckbyte.db`

## 相关文档

- [功能使用手册 - 项目管理](../../FUNCTIONAL_MANUAL.md#1-项目管理)
- [入排标准开发文档](./inclusion-exclusion-rules.md)
- [仪表盘开发文档](./dashboard.md)
- [数据库结构文档](../database/database-schema.md)

---

**最后更新**: 2024年12月  
**维护者**: 开发团队  
**文档版本**: v2.0
