# 项目结构说明

本文档详细说明临床研究项目管理系统的完整文件结构和组织方式。

## 📂 完整项目结构

```
临床研究项目管理系统/
├── 📁 src/                          # 前端源码 (SvelteKit)
│   ├── 📄 app.html                  # 应用主模板
│   ├── 📄 app.css                   # 全局样式 (Tailwind CSS)
│   ├── 📁 lib/                      # 共享代码库
│   │   ├── 📁 components/           # UI 组件库
│   │   │   ├── 📁 project/          # 项目管理组件
│   │   │   │   ├── 📄 ProjectForm.svelte
│   │   │   │   ├── 📄 ProjectBasicInfo.svelte
│   │   │   │   ├── 📄 ProjectDrugs.svelte
│   │   │   │   ├── 📄 ProjectPersonnel.svelte
│   │   │   │   └── 📄 ProjectSubsidies.svelte
│   │   │   ├── 📁 rule-designer/    # 规则设计器组件
│   │   │   │   ├── 📄 RuleDefinitionList.svelte
│   │   │   │   ├── 📄 RuleDefinitionForm.svelte
│   │   │   │   ├── 📄 ProjectCriteriaConfig.svelte
│   │   │   │   └── 📄 ProjectCriterionForm.svelte
│   │   │   ├── 📁 dashboard/        # 仪表盘组件
│   │   │   │   ├── 📄 ProjectStatsCard.svelte
│   │   │   │   ├── 📄 ChartContainer.svelte
│   │   │   │   └── 📄 StatisticsGrid.svelte
│   │   │   ├── 📁 csv-import/       # CSV导入组件
│   │   │   │   ├── 📄 CsvImportDialog.svelte
│   │   │   │   ├── 📄 ImportPreview.svelte
│   │   │   │   └── 📄 ValidationResults.svelte
│   │   │   ├── 📁 recruitment/      # 招募管理组件
│   │   │   │   ├── 📄 RecruitmentPolicyForm.svelte
│   │   │   │   └── 📄 RecruitmentOverview.svelte
│   │   │   └── 📁 ui/               # 基础 UI 组件
│   │   │       ├── 📄 Button.svelte
│   │   │       ├── 📄 Modal.svelte
│   │   │       ├── 📄 Table.svelte
│   │   │       ├── 📄 Form.svelte
│   │   │       └── 📄 SettingsDialog.svelte
│   │   ├── 📁 services/             # 前端服务层
│   │   │   ├── 📄 projectManagementService.ts    # 项目管理服务
│   │   │   ├── 📄 ruleDesignerService.ts         # 规则设计器服务
│   │   │   ├── 📄 staffService.ts                # 人员管理服务
│   │   │   ├── 📄 csvImportService.ts            # CSV导入服务
│   │   │   ├── 📄 recruitmentPolicyService.ts    # 招募政策服务
│   │   │   ├── 📄 dashboardService.ts            # 仪表盘服务
│   │   │   ├── 📄 sqliteDictionaryService.ts     # 字典管理服务
│   │   │   ├── 📄 criteriaGeneratorService.ts    # AI生成服务
│   │   │   ├── 📄 lighthouseApiService.ts        # Lighthouse API服务
│   │   │   ├── 📄 notionService.ts               # Notion集成服务
│   │   │   └── 📄 configService.ts               # 配置管理服务
│   │   ├── 📁 stores/               # 状态管理
│   │   │   ├── 📄 settings.ts                    # 通用应用设置
│   │   │   ├── 📄 lighthouseSettings.ts          # Lighthouse配置
│   │   │   ├── 📄 referrerSettings.ts            # 转诊API配置
│   │   │   └── 📄 dashboardStores.ts             # 仪表盘状态
│   │   ├── 📁 utils/                # 工具函数
│   │   │   ├── 📄 noteClassifier.ts              # AI笔记分类
│   │   │   ├── 📄 notion.ts                      # Notion工具函数
│   │   │   ├── 📄 validation.ts                  # 数据验证
│   │   │   └── 📄 formatting.ts                  # 格式化工具
│   │   ├── 📁 types/                # TypeScript 类型定义
│   │   │   ├── 📄 project.ts                     # 项目相关类型
│   │   │   ├── 📄 staff.ts                       # 人员相关类型
│   │   │   ├── 📄 rule.ts                        # 规则相关类型
│   │   │   └── 📄 api.ts                         # API响应类型
│   │   ├── 📁 models/               # 前端数据模型
│   │   │   ├── 📄 Project.ts                     # 项目模型
│   │   │   ├── 📄 Staff.ts                       # 人员模型
│   │   │   └── 📄 Rule.ts                        # 规则模型
│   │   └── 📁 examples/             # 示例数据
│   │       ├── 📄 criteria_import_template.json
│   │       ├── 📄 criteria_import_with_rules.json
│   │       └── 📄 test_criteria_import.json
│   └── 📁 routes/                   # 页面路由
│       ├── 📄 +layout.svelte                     # 根布局
│       ├── 📄 +page.svelte                       # 主页
│       ├── 📁 projects/             # 项目管理页面
│       │   ├── 📄 +page.svelte                   # 项目列表页
│       │   ├── 📁 new/                           # 新建项目
│       │   │   └── 📄 +page.svelte
│       │   ├── 📁 [projectId]/                   # 项目详情
│       │   │   ├── 📄 +page.svelte
│       │   │   ├── 📁 edit/
│       │   │   │   └── 📄 +page.svelte
│       │   │   └── 📁 criteria/                  # 入排标准配置
│       │   │       └── 📄 +page.svelte
│       │   └── 📁 batch-import/                  # 批量导入
│       │       └── 📄 +page.svelte
│       ├── 📁 staff/                # 人员管理页面
│       │   ├── 📄 +page.svelte                   # 人员列表页
│       │   ├── 📁 new/                           # 新增人员
│       │   │   └── 📄 +page.svelte
│       │   └── 📁 [id]/                          # 人员详情
│       │       └── 📄 +page.svelte
│       ├── 📁 notes/                # 智能笔记页面
│       │   └── 📄 +page.svelte
│       ├── 📁 dashboard/            # 数据分析页面
│       │   ├── 📄 +page.svelte                   # 主仪表盘
│       │   └── 📁 project-management/            # 项目管理仪表盘
│       │       └── 📄 +page.svelte
│       ├── 📁 sqlite-dictionaries/  # 字典管理页面
│       │   ├── 📄 +page.svelte                   # 字典列表
│       │   ├── 📁 new/                           # 新建字典
│       │   │   └── 📄 +page.svelte
│       │   ├── 📁 [id]/                          # 字典详情
│       │   │   └── 📄 +page.svelte
│       │   └── 📁 migrate/                       # 数据迁移
│       │       └── 📄 +page.svelte
│       ├── 📁 rules/                # 规则管理页面
│       │   └── 📁 definitions/
│       │       └── 📄 +page.svelte
│       ├── 📁 recruitment-overview/ # 招募概览页面
│       │   └── 📄 +page.svelte
│       ├── 📁 referrer-management/  # 转诊管理页面
│       │   └── 📄 +page.svelte
│       ├── 📁 lighthouse-users/     # Lighthouse用户管理
│       │   └── 📄 +page.svelte
│       └── 📁 settings/             # 系统设置页面
│           └── 📄 +page.svelte
├── 📁 src-tauri/                    # 后端源码 (Rust)
│   ├── 📁 src/
│   │   ├── 📁 commands/             # Tauri 命令层
│   │   │   ├── 📄 project_management_commands.rs  # 项目管理命令
│   │   │   ├── 📄 rule_designer_commands.rs       # 规则设计器命令
│   │   │   ├── 📄 dashboard_commands.rs           # 仪表盘命令
│   │   │   ├── 📄 csv_import_commands.rs          # CSV导入命令
│   │   │   ├── 📄 recruitment_policy_commands.rs  # 招募政策命令
│   │   │   ├── 📄 sqlite_dictionary_commands.rs   # 字典管理命令
│   │   │   ├── 📄 lighthouse_commands.rs          # Lighthouse API命令
│   │   │   ├── 📄 notion_commands.rs              # Notion集成命令
│   │   │   ├── 📄 config_commands.rs              # 配置管理命令
│   │   │   ├── 📄 file_system_commands.rs         # 文件系统命令
│   │   │   └── 📄 staff.rs                        # 人员管理命令
│   │   ├── 📁 services/             # 业务逻辑层
│   │   │   ├── 📄 project_service.rs              # 项目服务
│   │   │   ├── 📄 rule_designer_service.rs        # 规则设计器服务
│   │   │   ├── 📄 dashboard_service.rs            # 仪表盘服务
│   │   │   ├── 📄 csv_import_service.rs           # CSV导入服务
│   │   │   ├── 📄 sqlite_dictionary_service.rs    # 字典服务
│   │   │   ├── 📄 notion_service.rs               # Notion服务
│   │   │   └── 📄 config_service.rs               # 配置服务
│   │   ├── 📁 repositories/         # 数据访问层
│   │   │   ├── 📄 project_management_repository.rs # 项目管理仓储
│   │   │   ├── 📄 rule_designer_repository.rs     # 规则设计器仓储
│   │   │   ├── 📄 dashboard_repository.rs         # 仪表盘仓储
│   │   │   ├── 📄 recruitment_policy_repository.rs # 招募政策仓储
│   │   │   ├── 📁 sqlite/           # SQLite仓储实现
│   │   │   │   ├── 📄 project_repository.rs
│   │   │   │   ├── 📄 staff_repository.rs
│   │   │   │   └── 📄 dictionary_repository.rs
│   │   │   └── 📁 mongodb/          # MongoDB仓储实现
│   │   │       ├── 📄 config_repository.rs
│   │   │       └── 📄 notion_repository.rs
│   │   ├── 📁 models/               # 数据模型
│   │   │   ├── 📄 project.rs                      # 项目模型
│   │   │   ├── 📄 project_management.rs           # 项目管理模型
│   │   │   ├── 📄 rule_designer.rs                # 规则设计器模型
│   │   │   ├── 📄 dashboard.rs                    # 仪表盘模型
│   │   │   ├── 📄 recruitment_policy.rs           # 招募政策模型
│   │   │   ├── 📄 staff.rs                        # 人员模型
│   │   │   ├── 📄 dictionary.rs                   # 字典模型
│   │   │   └── 📄 config.rs                       # 配置模型
│   │   ├── 📁 config/               # 配置管理
│   │   │   ├── 📄 app_config.rs                   # 应用配置
│   │   │   └── 📄 database.rs                     # 数据库配置
│   │   ├── 📁 utils/                # 工具模块
│   │   │   ├── 📄 sqlite_service.rs               # SQLite服务
│   │   │   ├── 📄 mongodb_service.rs              # MongoDB服务
│   │   │   └── 📄 file_utils.rs                   # 文件工具
│   │   ├── 📄 error.rs              # 错误处理
│   │   ├── 📄 db.rs                 # 数据库连接
│   │   ├── 📄 response.rs           # 统一响应格式
│   │   ├── 📄 app.rs                # 应用初始化
│   │   └── 📄 lib.rs                # 库入口
│   ├── 📄 Cargo.toml                # Rust 依赖配置
│   ├── 📄 tauri.conf.json           # Tauri 应用配置
│   ├── 📄 build.rs                  # 构建脚本
│   └── 📁 capabilities/             # Tauri 权限配置
│       ├── 📄 migrated.json
│       └── 📄 default.json
├── 📁 docs/                         # 项目文档
│   ├── 📄 USER_MANUAL.md            # 用户使用手册
│   ├── 📄 ARCHITECTURE.md           # 系统架构文档
│   ├── 📄 PROJECT_STRUCTURE.md      # 项目结构说明 (本文档)
│   ├── 📄 dashboard-business-logic.md # 仪表盘业务逻辑
│   └── 📄 mermaid-diagrams.md       # 系统图表
├── 📁 开发文档/                      # 开发文档目录
│   ├── 📁 core-features/            # 核心功能开发文档
│   │   ├── 📄 project-management.md          # 项目管理模块技术文档
│   │   ├── 📄 inclusion-exclusion-rules.md   # 入排标准规则引擎技术文档
│   │   ├── 📄 ai-automation.md               # AI自动化生成技术文档
│   │   └── 📄 dashboard.md                   # 仪表盘技术文档
│   ├── 📁 database/                 # 数据库文档
│   │   ├── 📄 database-schema.md             # 数据库结构文档
│   │   └── 📄 peckbyte_database_doc.md       # 数据库详细文档
│   ├── 📁 api-integration/          # API集成文档
│   │   ├── 📄 lighthouse-api.md              # Lighthouse API规范
│   │   ├── 📄 gcpm-api-examples.md           # GCPM API示例
│   │   ├── 📄 referral-management-api.md     # 转诊管理API
│   │   ├── 📄 lighthouse-user-management.md  # Lighthouse用户管理
│   │   └── 📁 openapi-specs/        # OpenAPI规范文件
│   │       ├── 📄 lighthouse-api.json
│   │       └── 📄 referral-api.json
│   ├── 📁 system-features/          # 系统功能文档
│   │   ├── 📄 csv-import-system.md           # CSV导入系统
│   │   ├── 📄 csv-import-workflow.md         # CSV导入工作流
│   │   └── 📄 overview-filtering-improvements.md # 概览页面筛选功能
│   ├── 📁 examples/                 # 示例和测试数据
│   │   ├── 📄 test_personnel_import.csv      # 人员导入测试文件
│   │   └── 📁 sample-data/
│   │       ├── 📄 projects.json
│   │       ├── 📄 staff.json
│   │       └── 📄 rules.json
│   └── 📁 archive/                  # 历史文档存档
│       ├── 📄 legacy-docs.md
│       └── 📄 migration-notes.md
├── 📁 public/                       # 静态资源
│   └── 📄 subsidy_import_example.csv # 补贴导入示例文件
├── 📁 static/                       # 静态文件
│   ├── 📄 favicon.png               # 应用图标
│   ├── 📄 logo.svg                  # 应用Logo
│   ├── 📄 app-icon.png              # 应用图标
│   └── 📄 *.svg                     # 各种图标文件
├── 📁 tests/                        # 测试文件
│   ├── 📁 unit/                     # 单元测试
│   ├── 📁 integration/              # 集成测试
│   └── 📁 e2e/                      # 端到端测试
├── 📄 README.md                     # 项目说明文档
├── 📄 CLAUDE.md                     # Claude 开发指南
├── 📄 GEMINI.md                     # Gemini 相关文档
├── 📄 package.json                  # Node.js 依赖配置
├── 📄 package-lock.json             # 依赖版本锁定
├── 📄 tailwind.config.ts            # Tailwind CSS 配置
├── 📄 tsconfig.json                 # TypeScript 配置
├── 📄 svelte.config.js              # Svelte 配置
├── 📄 vite.config.js                # Vite 构建配置
├── 📄 postcss.config.js             # PostCSS 配置
├── 📄 components.json               # UI 组件配置
├── 📄 .gitignore                    # Git 忽略文件
├── 📄 .env.example                  # 环境变量示例
└── 📄 LICENSE                       # 许可证文件
```

## 📋 目录说明

### 前端代码结构 (`src/`)

#### 核心目录
- **`lib/`**: 共享代码库，包含组件、服务、工具等
- **`routes/`**: 基于文件系统的路由结构
- **`app.html`**: 应用的HTML模板
- **`app.css`**: 全局样式定义

#### 组件组织 (`lib/components/`)
- **按功能模块分组**: 每个业务模块有独立的组件目录
- **基础组件**: `ui/` 目录包含可复用的基础UI组件
- **业务组件**: 特定功能的组件按模块组织

#### 服务层 (`lib/services/`)
- **API封装**: 每个模块对应一个服务文件
- **统一接口**: 所有服务都通过统一的错误处理和响应格式
- **类型安全**: 使用TypeScript确保类型安全

### 后端代码结构 (`src-tauri/`)

#### 分层架构
- **`commands/`**: Tauri命令层，前端API入口
- **`services/`**: 业务逻辑层
- **`repositories/`**: 数据访问层
- **`models/`**: 数据模型定义

#### 核心文件
- **`lib.rs`**: 主要的库入口，注册所有Tauri命令
- **`app.rs`**: 应用初始化逻辑
- **`db.rs`**: 数据库连接管理
- **`error.rs`**: 统一错误类型定义

### 文档结构

#### 用户文档 (`docs/`)
- **用户手册**: 功能使用指南
- **架构文档**: 技术架构说明
- **项目结构**: 文件组织说明

#### 开发文档 (`开发文档/`)
- **核心功能**: 各模块的技术实现
- **数据库**: 数据库设计和结构
- **API集成**: 外部系统集成
- **系统功能**: 特殊功能的实现

### 配置文件

#### 前端配置
- **`package.json`**: Node.js依赖和脚本
- **`vite.config.js`**: Vite构建配置
- **`svelte.config.js`**: Svelte框架配置
- **`tailwind.config.ts`**: Tailwind CSS配置
- **`tsconfig.json`**: TypeScript编译配置

#### 后端配置
- **`Cargo.toml`**: Rust项目配置和依赖
- **`tauri.conf.json`**: Tauri应用配置
- **`build.rs`**: 构建脚本

## 🗂️ 文件命名规范

### 前端文件命名
- **组件文件**: PascalCase (如 `ProjectForm.svelte`)
- **服务文件**: camelCase + Service (如 `projectManagementService.ts`)
- **工具文件**: camelCase (如 `noteClassifier.ts`)
- **类型文件**: camelCase (如 `project.ts`)

### 后端文件命名
- **模块文件**: snake_case (如 `project_management_commands.rs`)
- **结构体**: PascalCase
- **函数**: snake_case
- **常量**: UPPER_SNAKE_CASE

### 目录命名
- **小写短划线**: kebab-case (如 `rule-designer/`)
- **简短描述性**: 目录名应简洁明了

## 📦 依赖管理

### 前端依赖 (`package.json`)
- **核心框架**: Svelte, SvelteKit
- **UI库**: Tailwind CSS, Lucide Icons
- **工具库**: TypeScript, Vite
- **AI集成**: Langchain.js, OpenAI

### 后端依赖 (`Cargo.toml`)
- **核心框架**: Tauri, Tokio
- **数据库**: rusqlite, mongodb
- **序列化**: serde, serde_json
- **HTTP客户端**: reqwest

## 🔄 构建流程

### 开发模式
```bash
npm run tauri dev  # 启动前后端开发服务器
```

### 生产构建
```bash
npm run tauri build  # 构建应用程序包
```

### 文件生成位置
- **开发**: 内存中或临时目录
- **生产**: `src-tauri/target/release/bundle/`

---

这个项目结构设计确保了代码的组织性、可维护性和可扩展性，为临床研究项目管理系统提供了清晰的开发框架。