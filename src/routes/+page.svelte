<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    Users, Activity, CheckCircle, Calendar, 
    BarChart3, Building, Stethoscope, AlertCircle, Target, TrendingUp
  } from 'lucide-svelte';

  // 导入图表组件
  import PieChart from '$lib/components/dashboard/charts/PieChart.svelte';
  import Bar<PERSON>hart from '$lib/components/dashboard/charts/BarChart.svelte';
  import LineChart from '$lib/components/dashboard/charts/LineChart.svelte';
  import KpiCard from '$lib/components/dashboard/charts/KpiCard.svelte';

  // 导入服务和存储
  import { dashboardService } from '$lib/services/dashboardService';
  import type { ChartDataPoint, FlatDashboardFilterParams } from '$lib/services/dashboardService';
  import {
    rawDashboardData,
    isLoading,
    errors,
    projectStatusChartOptions,
    projectStageChartOptions,
    recruitmentStatusChartOptions,
    diseaseChartOptions,
    monthlyNewProjectsChartOptions
  } from '$lib/stores/dashboardStores';
  import { goto } from '$app/navigation';
  
  // 导入筛选组件
  import DashboardFilterPanel from '$lib/components/dashboard/filters/DashboardFilterPanel.svelte';

  // 筛选状态
  let currentFilters: FlatDashboardFilterParams = {};

  // 加载仪表盘概览数据
  async function loadDashboardOverview() {
    try {
      $isLoading.overview = true;
      $errors.overview = undefined;

      console.log('加载仪表盘概览数据，筛选条件:', currentFilters);
      
      const overview = await dashboardService.getDashboardOverview(currentFilters);
      console.log('概览数据加载成功:', overview);
      
      $rawDashboardData = { ...$rawDashboardData, overview };
    } catch (err: any) {
      $errors.overview = err.message || '加载仪表盘概览数据失败';
      console.error('加载仪表盘概览数据失败:', err);
    } finally {
      $isLoading.overview = false;
    }
  }

  // 加载项目状态分布数据
  async function loadProjectStatusDistribution() {
    try {
      $isLoading.projectStatus = true;
      $errors.projectStatus = undefined;

      console.log('加载项目状态分布数据，筛选条件:', currentFilters);

      const distribution = await dashboardService.getProjectStatusDistribution(currentFilters);
      const chartData = await dashboardService.getProjectStatusChartData(currentFilters);

      console.log('项目状态分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        projectStatusDistribution: distribution,
        projectStatusChartData: chartData
      };
    } catch (err: any) {
      $errors.projectStatus = err.message || '加载项目状态分布数据失败';
      console.error('加载项目状态分布数据失败:', err);
    } finally {
      $isLoading.projectStatus = false;
    }
  }

  // 加载项目阶段分布数据
  async function loadProjectStageDistribution() {
    try {
      $isLoading.projectStage = true;
      $errors.projectStage = undefined;

      console.log('加载项目阶段分布数据，筛选条件:', currentFilters);

      const distribution = await dashboardService.getProjectStageDistribution(currentFilters);
      const chartData = await dashboardService.getProjectStageChartData(currentFilters);

      console.log('项目阶段分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        projectStageDistribution: distribution,
        projectStageChartData: chartData
      };
    } catch (err: any) {
      $errors.projectStage = err.message || '加载项目阶段分布数据失败';
      console.error('加载项目阶段分布数据失败:', err);
    } finally {
      $isLoading.projectStage = false;
    }
  }

  // 加载招募状态分布数据
  async function loadRecruitmentStatusDistribution() {
    try {
      $isLoading.recruitmentStatus = true;
      $errors.recruitmentStatus = undefined;

      console.log('加载招募状态分布数据，筛选条件:', currentFilters);

      const distribution = await dashboardService.getRecruitmentStatusDistribution(currentFilters);
      const chartData = await dashboardService.getRecruitmentStatusChartData(currentFilters);

      console.log('招募状态分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        recruitmentStatusDistribution: distribution,
        recruitmentStatusChartData: chartData
      };
    } catch (err: any) {
      $errors.recruitmentStatus = err.message || '加载招募状态分布数据失败';
      console.error('加载招募状态分布数据失败:', err);
    } finally {
      $isLoading.recruitmentStatus = false;
    }
  }

  // 加载疾病分布数据
  async function loadDiseaseDistribution() {
    try {
      $isLoading.disease = true;
      $errors.disease = undefined;

      console.log('加载疾病分布数据，筛选条件:', currentFilters);

      const distribution = await dashboardService.getDiseaseDistribution(currentFilters);
      const chartData = await dashboardService.getDiseaseChartData(currentFilters);

      console.log('疾病分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        diseaseDistribution: distribution,
        diseaseChartData: chartData
      };
    } catch (err: any) {
      $errors.disease = err.message || '加载疾病分布数据失败';
      console.error('加载疾病分布数据失败:', err);
    } finally {
      $isLoading.disease = false;
    }
  }

  // 加载每月新增项目数据
  async function loadMonthlyNewProjects() {
    try {
      $isLoading.monthlyNewProjects = true;
      $errors.monthlyNewProjects = undefined;

      console.log('加载每月新增项目数据，筛选条件:', currentFilters);

      const monthlyData = await dashboardService.getMonthlyNewProjects(currentFilters);
      const chartData = await dashboardService.getMonthlyNewProjectsChartData(currentFilters);

      console.log('每月新增项目数据加载成功:', { monthlyData, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        monthlyNewProjects: monthlyData,
        monthlyNewProjectsChartData: chartData
      };
    } catch (err: any) {
      $errors.monthlyNewProjects = err.message || '加载每月新增项目数据失败';
      console.error('加载每月新增项目数据失败:', err);
    } finally {
      $isLoading.monthlyNewProjects = false;
    }
  }

  // 调试项目数据
  async function debugProjectData() {
    try {
      const result = await dashboardService.debugProjectDateStatistics();
      console.log('调试结果:', result);
      alert('调试信息已输出到控制台和日志，请检查开发者工具');
    } catch (err: any) {
      console.error('调试失败:', err);
      alert('调试失败: ' + err.message);
    }
  }

  // 导航到项目列表
  function navigateToProjects() {
    goto('/projects');
  }
  
  // 筛选处理
  function handleFilterChange(filters: FlatDashboardFilterParams) {
    console.log('=== 筛选事件触发 ===');
    console.log('应用筛选条件:', filters);
    console.log('筛选前 currentFilters:', currentFilters);
    currentFilters = filters;
    console.log('筛选后 currentFilters:', currentFilters);
    
    // 重新加载所有数据
    refreshAllData();
  }

  function handleClearAllFilters() {
    console.log('清除所有筛选条件');
    currentFilters = {};
    
    // 重新加载所有数据
    refreshAllData();
  }

  // 重新加载所有数据
  function refreshAllData() {
    loadDashboardOverview();
    loadProjectStatusDistribution();
    loadProjectStageDistribution();
    loadRecruitmentStatusDistribution();
    loadDiseaseDistribution();
    loadMonthlyNewProjects();
  }

  // 组件挂载时加载数据
  onMount(() => {
    refreshAllData();
  });
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4 py-8">
    <!-- 标题和操作区域 -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">项目管理仪表盘</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">专注于项目状态筛选的核心运营数据分析</p>
      </div>

      <div class="flex flex-wrap gap-2">
        <button
          type="button"
          onclick={() => navigateToProjects()}
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          查看项目列表
        </button>
      </div>
    </div>

    <!-- 筛选面板 -->
    <DashboardFilterPanel
      {currentFilters}
      on:filterChange={(e) => handleFilterChange(e.detail)}
      on:clearAll={handleClearAllFilters}
    />

    <!-- 主要 KPI 指标 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {#key `${JSON.stringify(currentFilters)}-kpi-${$rawDashboardData.overview?.active_project_count || 0}`}
        <KpiCard
          title="活动项目数"
          value={$rawDashboardData.overview?.active_project_count || 0}
          icon={Activity}
          color="blue"
          isLoading={$isLoading.overview}
          subtitle="正在进行的项目"
        />

        <KpiCard
          title="总项目数"
          value={$rawDashboardData.overview?.total_project_count || 0}
          icon={Target}
          color="indigo"
          isLoading={$isLoading.overview}
          subtitle="项目总数量"
        />

        <KpiCard
          title="招募中项目"
          value={$rawDashboardData.overview?.recruiting_project_count || 0}
          icon={Users}
          color="green"
          isLoading={$isLoading.overview}
          subtitle="正在招募患者"
        />

        <KpiCard
          title="本月新项目"
          value={$rawDashboardData.monthlyNewProjects && $rawDashboardData.monthlyNewProjects.length > 0
            ? $rawDashboardData.monthlyNewProjects[$rawDashboardData.monthlyNewProjects.length - 1].project_count
            : 0}
          icon={Calendar}
          color="purple"
          isLoading={$isLoading.monthlyNewProjects}
          subtitle="当月新启动"
        />
      {/key}
    </div>

    <!-- 核心图表 - 2x3 网格 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-8">
      {#key `${JSON.stringify(currentFilters)}-charts-${JSON.stringify($rawDashboardData.projectStatusChartData)}`}
        <!-- 项目状态分布 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">项目状态分布</h3>
            <BarChart3 class="w-5 h-5 text-gray-500" />
          </div>

          {#if $errors.projectStatus}
            <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
              <AlertCircle class="w-4 h-4 mr-2" />
              {$errors.projectStatus}
            </div>
          {/if}

          <PieChart
            options={$projectStatusChartOptions}
            height="300px"
          />
        </div>

        <!-- 项目阶段分布 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">项目阶段分布</h3>
            <Target class="w-5 h-5 text-gray-500" />
          </div>

          {#if $errors.projectStage}
            <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
              <AlertCircle class="w-4 h-4 mr-2" />
              {$errors.projectStage}
            </div>
          {/if}

          <PieChart
            options={$projectStageChartOptions}
            height="300px"
          />
        </div>

        <!-- 疾病领域分布 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">疾病领域分布</h3>
            <Stethoscope class="w-5 h-5 text-gray-500" />
          </div>

          {#if $errors.disease}
            <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
              <AlertCircle class="w-4 h-4 mr-2" />
              {$errors.disease}
            </div>
          {/if}

          <BarChart
            options={$diseaseChartOptions}
            height="300px"
          />
        </div>

        <!-- 招募状态分布 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">招募状态分布</h3>
            <CheckCircle class="w-5 h-5 text-gray-500" />
          </div>

          {#if $errors.recruitmentStatus}
            <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
              <AlertCircle class="w-4 h-4 mr-2" />
              {$errors.recruitmentStatus}
            </div>
          {/if}

          <PieChart
            options={$recruitmentStatusChartOptions}
            height="300px"
          />
        </div>

        <!-- 每月新项目趋势 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">新项目趋势</h3>
            <div class="flex items-center space-x-2">
              <button
                onclick={debugProjectData}
                class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                title="调试项目数据"
              >
                调试
              </button>
              <TrendingUp class="w-5 h-5 text-gray-500" />
            </div>
          </div>

          {#if $errors.monthlyNewProjects}
            <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
              <AlertCircle class="w-4 h-4 mr-2" />
              {$errors.monthlyNewProjects}
            </div>
          {/if}

          {#if $isLoading.monthlyNewProjects}
            <div class="flex items-center justify-center h-64">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span class="ml-2 text-gray-600 dark:text-gray-400">加载趋势数据中...</span>
            </div>
          {:else if $rawDashboardData.monthlyNewProjects && $rawDashboardData.monthlyNewProjects.length === 0}
            <div class="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <div class="text-center">
                <Calendar class="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p class="text-lg font-medium mb-2">暂无月度项目数据</p>
                <p class="text-sm">数据库中可能没有有效的项目启动日期</p>
                <p class="text-xs mt-2">点击"调试"按钮查看详细信息</p>
              </div>
            </div>
          {:else if $rawDashboardData.monthlyNewProjectsChartData && $rawDashboardData.monthlyNewProjectsChartData.length > 0}
            <LineChart
              options={$monthlyNewProjectsChartOptions}
              height="300px"
            />
          {:else}
            <div class="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <div class="text-center">
                <TrendingUp class="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p class="text-lg font-medium mb-2">暂无图表数据</p>
                <p class="text-sm">正在加载图表数据...</p>
              </div>
            </div>
          {/if}
        </div>

        <!-- 占位图表区域，保持网格布局平衡 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">项目综合指标</h3>
            <Building class="w-5 h-5 text-gray-500" />
          </div>

          <div class="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
            <div class="text-center">
              <Building class="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p class="text-lg font-medium mb-2">更多指标即将上线</p>
              <p class="text-sm">敬请期待更多的项目分析维度</p>
            </div>
          </div>
        </div>
      {/key}
    </div>

    <!-- 操作提示区域 -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
      <div class="flex items-start">
        <AlertCircle class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" />
        <div>
          <h4 class="text-blue-900 dark:text-blue-300 font-medium mb-2">筛选使用提示</h4>
          <div class="text-blue-800 dark:text-blue-300 text-sm space-y-1">
            <p>• 在筛选面板中选择项目状态（未启动、在研、已结束、暂停中）来过滤数据</p>
            <p>• 所有图表和KPI指标都会根据筛选条件实时更新</p>
            <p>• 项目状态分布图表本身也会响应其他维度的筛选条件</p>
            <p>• 点击"清除筛选"恢复显示所有项目数据</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>