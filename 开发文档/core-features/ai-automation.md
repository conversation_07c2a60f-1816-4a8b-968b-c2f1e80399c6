# 自动化生成入排标准开发文档

> **注意**: 本文档提供自动化生成入排标准功能的详细技术实现信息。用户功能说明请参考 [FUNCTIONAL_MANUAL.md](../FUNCTIONAL_MANUAL.md#2-入排标准规则引擎) 中的 AI 智能生成部分。

## 概述

本文档详细描述自动化生成入排标准功能的技术架构和实现细节。该功能基于 Langchain.js 和大型语言模型，能够将自然语言描述的入排标准文本自动转换为结构化的 JSON 配置文件。

## 功能需求

以下是自动生成入排标准模块的开发需求：
- 页面子菜单位于窗口顶部菜单的“项目管理”菜单下面，子菜单名称为“自动生成入排标准”
- 处理页面分为2个部分，左侧是待处理文档输入框；右侧是处理好的JSON文件，需要显示目前处理的进度；
- 页面需要有选择“大语言模型”的下列选项框； 页面需要有配置‘Prompt’的弹框，‘Prompt’供langchain使用；
- 用户在输入框粘贴一段入组排除标准的文字；
- 系统给文字分段，每一段可能是一条或者多条`待处理入组排除标准`；
- 使用`langchain`的prompt模板，将每一段文字转换为json文件；
    - 读取“rule_definitions”表单中的“rule_definition_id”、“rule_name”和“rule_description”；
    - 把“rule_description”传入`langchain`的prompt模板；
    - 匹配这条`待处理入组排除标准`是否符合系统里面已有的规则，如果符合，调取规则的JSON模板；
    - 使用`langchain`从`待处理入组排除标准`获取的参数，把参数生成规则的JSON；
    - 把所有的`待处理入组排除标准`生成入组排除标准导入要求的JSON文件；

## 处理流程图
```mermaid
graph TD
    A(Start) --> B[/User Input Text/];
    %% Simplified Node C text drastically
    B --> C[**1. Preprocess Text**];
    C --> D[(Load Rule Definitions)];

    D --> E{Process Segment List};
    E -- For Each Segment --> F[**2. Prepare Prompt**];
    F --> G[**3. Call Langchain/LLM**];
    G --> H{Get LLM Output};

    H -- Success --> I[Collect Valid Result];
    I --> J{More Segments?};

    H -- Failure / No Match --> K[Log Failure / Skip];
    K --> J;

    J -- Yes --> E;
    J -- No --> L[**4. Aggregate Results**];
    %% Node M text simplified further
    L --> M[**5. Postprocess Add order**];
    M --> N[/Generate Final JSON/];
    N --> O(End);
```

## 入组排除标准JSON文件示范
```json
{
  "criteria": [
    {
      "rule_definition_id": 4,
      "parameter_values": {
        "Min_age": 18,
        "Max_age": 65
      },
      "display_order": 1
    },
    {
      "rule_definition_id": 7,
      "parameter_values": {
        "diagnose_month": 6
      },
      "display_order": 2
    },
    {
      "rule_definition_id": 14,
      "parameter_values": {
        "Type": "LABA/LAMA/ICS三联",
        "Maintenance_duration": 3,
        "Stable_dosage_duration": 1
      },
      "display_order": 3
    },
    // --- 以下为新增的示例规则 ---
    {
      "rule_definition_id": 8, // 支气管舒张剂前（Pre-BD）FEV1
      "parameter_values": {
        "Pre_BD_FEV1_min": 30, // FEV1 预测值百分比最小值
        "Pre_BD_FEV1_max": 80  // FEV1 预测值百分比最大值
      },
      "display_order": 4
    },
    {
      "rule_definition_id": 11, // 血嗜酸粒细胞计数
      "parameter_values": {
        "Min_count": 150,        // 计数最小值 (个细胞/μL)
        "data_range": "筛选前12个月内" // 有效数据的时间范围
      },
      "display_order": 5
    },
    {
      "rule_definition_id": 13, // FEV1/FVC
      "parameter_values": {
        "Max_number": 0.7       // FEV1/FVC 比值需要小于0.7
      },
      "display_order": 6
    },
    {
      "rule_definition_id": 15, // 吸烟史
      "parameter_values": {
        "smoking_history": 10  // 吸烟史至少10包/年
      },
      "display_order": 7
    },
    {
        "rule_definition_id": 17, // 慢阻肺病急性加重病史
        "parameter_values": {
            "date_number": 12,       // 在过去12个月内
            "Min_count": 2,          // 至少发生2次
            "degree": "中度"         // 要求为中度急性加重
        },
        "display_order": 8
    }
  ],
  "or_groups": [ // or_groups 定义了哪些条件之间是“或”的关系
    {
      "criteria_indices": [ // 这个组表示索引为 0 和 1 的条件满足任意一个即可
        0, // 对应 display_order: 1 (年龄规则)
        1  // 对应 display_order: 2 (确诊史规则)
      ]
    },
    {
      "criteria_indices": [ // 这个组表示索引为 6 和 7 的条件满足任意一个即可
         6, // 对应 display_order: 7 (吸烟史规则)
         7  // 对应 display_order: 8 (慢阻肺病急性加重病史规则)
      ]
       // 注意: 如果or_groups为空或不存在，则默认所有criteria之间是"与"的关系
       // 如果有or_groups，则在组内的条件是"或"关系，组与组之间以及未分组的条件之间是"与"关系。
       // 例如在此例中，最终逻辑是：
       // (条件1 或 条件2) 与 条件3 与 条件4 与 条件5 与 条件6 与 (条件7 或 条件8)
    }
  ]
}
```

## 功能优化记录

### 2024年更新：OR关系处理与界面优化

1. **OR关系自动处理**
   - 优化了Prompt模板，增强了对"或"关系的检测能力
   - 实现了自动分割OR关系的功能，将包含"或"关系的段落自动分割为多个独立段落
   - 添加了`splitOrRelationship`方法，使用专门的提示模板指导LLM分割包含"或"关系的段落
   - 将分割后的子段落重新进行规则匹配，并自动组织成OR组
   - 更新了`GeneratedOrGroup`类型，使其包含`group_id`、`criteria_ids`和`operator`字段

2. **匹配结果显示优化**
   - 在匹配成功的段落中显示规则的category（入组标准或排除标准）
   - 使用不同的颜色标识不同类型的标准（入组标准为蓝色，排除标准为红色）
   - 添加了移除不符合要求的匹配结果功能，包括确认对话框
   - 实现了移除后自动更新JSON输出和OR组的逻辑

3. **未匹配段落处理优化**
   - 为未匹配段落添加了规则选择下拉框和追加描述功能
   - 实现了可编辑的段落文本，允许用户在追加前修改文本内容
   - 添加了显示当前规则描述和追加结果的功能
   - 优化了追加描述的用户体验，提供明确的成功反馈

4. **键盘快捷键修复**
   - 在`tauri.conf.json`中添加`withGlobalTauri: true`启用全局Tauri对象
   - 在`+layout.svelte`中添加代码阻止Backspace键被解释为导航操作
   - 在`app.html`中添加脚本确保复制/粘贴快捷键在整个应用中正常工作
   - 修复了`app`参数的类型注解，解决了编译错误

5. **处理流程优化**
   - 修改了处理逻辑，将所有规则定义一次性传给LLM，而不是逐个尝试
   - 减少了每个段落的LLM调用次数，提高了处理效率
   - 改进了错误处理和用户反馈机制，提供更详细的处理状态信息

## 相关文档

- [功能使用手册 - AI 功能集成](../../FUNCTIONAL_MANUAL.md#12-ai-功能集成)
- [入排标准规则开发文档](./inclusion-exclusion-rules.md)
- [项目管理开发文档](./project-management.md)

---

**最后更新**: 2024年12月  
**维护者**: 开发团队  
**文档版本**: v2.0