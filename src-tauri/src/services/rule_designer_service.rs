use serde_json::Value;
use std::error::Error;

use crate::models::rule_designer::{
    CreateProjectCriterionRequest, CreateRuleDefinitionRequest, ProjectCriterion,
    ProjectCriterionQuery, ProjectCriterionWithRule, RuleDefinition, RuleDefinitionQuery,
    UpdateProjectCriterionRequest, UpdateRuleDefinitionRequest,
};
use crate::repositories::rule_designer_repository::RuleDesignerRepository;

/// 规则设计器服务
pub struct RuleDesignerService {
    repository: RuleDesignerRepository,
}

impl RuleDesignerService {
    /// 创建新的规则设计器服务
    pub fn new(db_path: String) -> Self {
        let repository = RuleDesignerRepository::new(db_path);
        Self { repository }
    }

    /// 初始化表
    pub fn init_tables(&self) -> Result<(), Box<dyn Error>> {
        self.repository.init_tables()
    }

    /// 获取所有规则定义
    pub fn get_rule_definitions(
        &self,
        query: &RuleDefinitionQuery,
    ) -> Result<Vec<RuleDefinition>, Box<dyn Error>> {
        self.repository.get_rule_definitions(query)
    }

    /// 根据ID获取规则定义
    pub fn get_rule_definition_by_id(
        &self,
        rule_definition_id: i64,
    ) -> Result<Option<RuleDefinition>, Box<dyn Error>> {
        self.repository
            .get_rule_definition_by_id(rule_definition_id)
    }

    /// 创建规则定义
    pub fn create_rule_definition(
        &self,
        request: &CreateRuleDefinitionRequest,
    ) -> Result<RuleDefinition, Box<dyn Error>> {
        // 验证参数模式是否为有效的JSON
        self.validate_parameter_schema(&request.parameter_schema)?;

        let rule = RuleDefinition {
            rule_definition_id: None,
            rule_name: request.rule_name.clone(),
            rule_description: request.rule_description.clone(),
            category: request.category.clone(),
            parameter_schema: request.parameter_schema.clone(),
            label: request.label.clone(),
            created_at: None,
            updated_at: None,
        };

        let id = self.repository.create_rule_definition(&rule)?;

        let created_rule = self
            .repository
            .get_rule_definition_by_id(id)?
            .ok_or_else(|| "Failed to retrieve created rule definition".to_string())?;

        Ok(created_rule)
    }

    /// 更新规则定义
    pub fn update_rule_definition(
        &self,
        rule_definition_id: i64,
        request: &UpdateRuleDefinitionRequest,
    ) -> Result<RuleDefinition, Box<dyn Error>> {
        // 获取现有规则定义
        let existing_rule = self
            .repository
            .get_rule_definition_by_id(rule_definition_id)?
            .ok_or_else(|| format!("Rule definition with ID {} not found", rule_definition_id))?;

        // 如果提供了参数模式，验证其是否为有效的JSON
        if let Some(parameter_schema) = &request.parameter_schema {
            self.validate_parameter_schema(parameter_schema)?;
        }

        // 创建更新后的规则定义
        let updated_rule = RuleDefinition {
            rule_definition_id: Some(rule_definition_id),
            rule_name: request.rule_name.clone().unwrap_or(existing_rule.rule_name),
            rule_description: request
                .rule_description
                .clone()
                .or(existing_rule.rule_description),
            category: request.category.clone().or(existing_rule.category),
            parameter_schema: request
                .parameter_schema
                .clone()
                .unwrap_or(existing_rule.parameter_schema),
            label: request.label.clone().or(existing_rule.label),
            created_at: existing_rule.created_at,
            updated_at: None,
        };

        // 更新规则定义
        self.repository
            .update_rule_definition(rule_definition_id, &updated_rule)?;

        // 获取更新后的规则定义
        let updated_rule = self
            .repository
            .get_rule_definition_by_id(rule_definition_id)?
            .ok_or_else(|| {
                format!(
                    "Failed to retrieve updated rule definition with ID {}",
                    rule_definition_id
                )
            })?;

        Ok(updated_rule)
    }

    /// 查找使用特定规则定义的项目
    pub fn find_projects_using_rule(
        &self,
        rule_definition_id: i64,
    ) -> Result<Vec<(String, String, String)>, Box<dyn Error>> {
        self.repository.find_projects_using_rule(rule_definition_id)
    }

    /// 删除规则定义
    pub fn delete_rule_definition(&self, rule_definition_id: i64) -> Result<(), Box<dyn Error>> {
        // 检查规则定义是否存在
        let _existing_rule = self
            .repository
            .get_rule_definition_by_id(rule_definition_id)?
            .ok_or_else(|| format!("Rule definition with ID {} not found", rule_definition_id))?;

        // 删除规则定义 (会在事务中级联删除相关的项目标准)
        self.repository.delete_rule_definition(rule_definition_id)
    }

    /// 获取项目标准
    pub fn get_project_criteria(
        &self,
        query: &ProjectCriterionQuery,
    ) -> Result<Vec<ProjectCriterionWithRule>, Box<dyn Error>> {
        self.repository.get_project_criteria(query)
    }

    /// 根据ID获取项目标准
    pub fn get_project_criterion_by_id(
        &self,
        project_criterion_id: i64,
    ) -> Result<Option<ProjectCriterionWithRule>, Box<dyn Error>> {
        self.repository
            .get_project_criterion_by_id(project_criterion_id)
    }

    /// 创建项目标准
    pub fn create_project_criterion(
        &self,
        request: &CreateProjectCriterionRequest,
    ) -> Result<ProjectCriterionWithRule, Box<dyn Error>> {
        // 验证规则定义是否存在
        let rule_definition = self
            .repository
            .get_rule_definition_by_id(request.rule_definition_id)?
            .ok_or_else(|| {
                format!(
                    "Rule definition with ID {} not found",
                    request.rule_definition_id
                )
            })?;

        // 验证参数值是否为有效的JSON
        self.validate_parameter_values(&request.parameter_values)?;

        // 验证参数值是否符合参数模式
        self.validate_parameter_values_against_schema(
            &request.parameter_values,
            &rule_definition.parameter_schema,
        )?;

        // 验证标准类型
        if request.criterion_type != "inclusion" && request.criterion_type != "exclusion" {
            return Err(format!(
                "Invalid criterion type: {}. Must be 'inclusion' or 'exclusion'",
                request.criterion_type
            )
            .into());
        }

        let criterion = ProjectCriterion {
            project_criterion_id: None,
            project_id: request.project_id.clone(),
            rule_definition_id: request.rule_definition_id,
            criterion_type: request.criterion_type.clone(),
            parameter_values: request.parameter_values.clone(),
            is_active: request.is_active,
            display_order: request.display_order,
            created_at: None,
            updated_at: None,
            criteria_group_id: None,
            group_operator: None,
        };

        let id = self.repository.create_project_criterion(&criterion)?;

        let created_criterion = self
            .repository
            .get_project_criterion_by_id(id)?
            .ok_or_else(|| "Failed to retrieve created project criterion".to_string())?;

        Ok(created_criterion)
    }

    /// 更新项目标准
    pub fn update_project_criterion(
        &self,
        project_criterion_id: i64,
        request: &UpdateProjectCriterionRequest,
    ) -> Result<ProjectCriterionWithRule, Box<dyn Error>> {
        // 获取现有项目标准
        let existing_criterion = self
            .repository
            .get_project_criterion_by_id(project_criterion_id)?
            .ok_or_else(|| {
                format!(
                    "Project criterion with ID {} not found",
                    project_criterion_id
                )
            })?;

        // 如果提供了规则定义ID，验证规则定义是否存在
        let rule_definition_id = request
            .rule_definition_id
            .unwrap_or(existing_criterion.criterion.rule_definition_id);
        if request.rule_definition_id.is_some() {
            let _rule_definition = self
                .repository
                .get_rule_definition_by_id(rule_definition_id)?
                .ok_or_else(|| {
                    format!("Rule definition with ID {} not found", rule_definition_id)
                })?;
        }

        // 如果提供了参数值，验证其是否为有效的JSON
        let parameter_values = request
            .parameter_values
            .clone()
            .unwrap_or(existing_criterion.criterion.parameter_values.clone());
        if request.parameter_values.is_some() {
            self.validate_parameter_values(&parameter_values)?;

            // 获取规则定义
            let rule_definition = self
                .repository
                .get_rule_definition_by_id(rule_definition_id)?
                .ok_or_else(|| {
                    format!("Rule definition with ID {} not found", rule_definition_id)
                })?;

            // 验证参数值是否符合参数模式
            self.validate_parameter_values_against_schema(
                &parameter_values,
                &rule_definition.parameter_schema,
            )?;
        }

        // 验证标准类型
        let criterion_type = request
            .criterion_type
            .clone()
            .unwrap_or(existing_criterion.criterion.criterion_type.clone());
        if criterion_type != "inclusion" && criterion_type != "exclusion" {
            return Err(format!(
                "Invalid criterion type: {}. Must be 'inclusion' or 'exclusion'",
                criterion_type
            )
            .into());
        }

        // 创建更新后的项目标准
        let updated_criterion = ProjectCriterion {
            project_criterion_id: Some(project_criterion_id),
            project_id: existing_criterion.criterion.project_id.clone(),
            rule_definition_id,
            criterion_type,
            parameter_values,
            is_active: request.is_active.or(existing_criterion.criterion.is_active),
            display_order: request
                .display_order
                .or(existing_criterion.criterion.display_order),
            created_at: existing_criterion.criterion.created_at.clone(),
            updated_at: None,
            // 特殊处理 criteria_group_id 和 group_operator，允许将其设置为 NULL
            criteria_group_id: match &request.criteria_group_id {
                Some(id) if id.is_empty() => None, // 空字符串视为 NULL
                Some(id) => Some(id.clone()),
                None => existing_criterion.criterion.criteria_group_id.clone(),
            },
            group_operator: match &request.group_operator {
                Some(op) if op.is_empty() => None, // 空字符串视为 NULL
                Some(op) => Some(op.clone()),
                None => existing_criterion.criterion.group_operator.clone(),
            },
        };

        // 更新项目标准
        self.repository
            .update_project_criterion(project_criterion_id, &updated_criterion)?;

        // 获取更新后的项目标准
        let updated_criterion = self
            .repository
            .get_project_criterion_by_id(project_criterion_id)?
            .ok_or_else(|| {
                format!(
                    "Failed to retrieve updated project criterion with ID {}",
                    project_criterion_id
                )
            })?;

        Ok(updated_criterion)
    }

    /// 删除项目标准
    pub fn delete_project_criterion(
        &self,
        project_criterion_id: i64,
    ) -> Result<(), Box<dyn Error>> {
        // 检查项目标准是否存在
        let _existing_criterion = self
            .repository
            .get_project_criterion_by_id(project_criterion_id)?
            .ok_or_else(|| {
                format!(
                    "Project criterion with ID {} not found",
                    project_criterion_id
                )
            })?;

        // 删除项目标准
        self.repository
            .delete_project_criterion(project_criterion_id)
    }

    /// 验证参数模式是否为有效的JSON
    fn validate_parameter_schema(&self, parameter_schema: &str) -> Result<(), Box<dyn Error>> {
        match serde_json::from_str::<Value>(parameter_schema) {
            Ok(_) => Ok(()),
            Err(e) => Err(format!("Invalid parameter schema JSON: {}", e).into()),
        }
    }

    /// 验证参数值是否为有效的JSON
    fn validate_parameter_values(&self, parameter_values: &str) -> Result<(), Box<dyn Error>> {
        match serde_json::from_str::<Value>(parameter_values) {
            Ok(_) => Ok(()),
            Err(e) => Err(format!("Invalid parameter values JSON: {}", e).into()),
        }
    }

    /// 验证参数值是否符合参数模式
    fn validate_parameter_values_against_schema(
        &self,
        parameter_values: &str,
        parameter_schema: &str,
    ) -> Result<(), Box<dyn Error>> {
        // 解析参数模式和参数值
        let schema: Value = serde_json::from_str(parameter_schema)?;
        let values: Value = serde_json::from_str(parameter_values)?;

        // 检查参数值是否为对象
        if !values.is_object() {
            return Err("Parameter values must be a JSON object".into());
        }

        // 检查参数模式是否为对象
        if !schema.is_object() {
            return Err("Parameter schema must be a JSON object".into());
        }

        // 获取参数模式中的参数列表
        let schema_obj = schema.as_object().unwrap();
        let parameters = match schema_obj.get("parameters") {
            Some(params) if params.is_array() => params.as_array().unwrap(),
            _ => return Err("Parameter schema must contain a 'parameters' array".into()),
        };

        // 获取参数值对象
        let values_obj = values.as_object().unwrap();

        // 检查每个必需参数是否存在
        for param in parameters {
            if !param.is_object() {
                continue;
            }

            let param_obj = param.as_object().unwrap();

            // 获取参数名称
            let name = match param_obj.get("name") {
                Some(n) if n.is_string() => n.as_str().unwrap(),
                _ => continue,
            };

            // 检查参数是否必需
            let required = match param_obj.get("required") {
                Some(r) if r.is_boolean() => r.as_bool().unwrap(),
                _ => false,
            };

            // 如果参数是必需的，但在参数值中不存在，则返回错误
            if required && !values_obj.contains_key(name) {
                return Err(format!("Required parameter '{}' is missing", name).into());
            }

            // 如果参数在参数值中存在，检查其类型是否符合参数模式
            if values_obj.contains_key(name) {
                let value = values_obj.get(name).unwrap();

                // 获取参数类型
                let param_type = match param_obj.get("type") {
                    Some(t) if t.is_string() => t.as_str().unwrap(),
                    _ => continue,
                };

                // 检查参数类型
                match param_type {
                    "string" => {
                        if !value.is_string() {
                            return Err(format!("Parameter '{}' must be a string", name).into());
                        }
                    }
                    "number" | "integer" => {
                        if !value.is_number() {
                            return Err(format!("Parameter '{}' must be a number", name).into());
                        }

                        if param_type == "integer" && !value.is_i64() {
                            return Err(format!("Parameter '{}' must be an integer", name).into());
                        }
                    }
                    "boolean" => {
                        if !value.is_boolean() {
                            return Err(format!("Parameter '{}' must be a boolean", name).into());
                        }
                    }
                    "enum" => {
                        // 检查参数值是否在枚举选项中
                        let options = match param_obj.get("options") {
                            Some(o) if o.is_array() => o.as_array().unwrap(),
                            _ => continue,
                        };

                        let mut valid = false;
                        for option in options {
                            if option == value {
                                valid = true;
                                break;
                            }
                        }

                        if !valid {
                            return Err(format!(
                                "Parameter '{}' must be one of the allowed options",
                                name
                            )
                            .into());
                        }
                    }
                    _ => {}
                }
            }
        }

        Ok(())
    }
}
