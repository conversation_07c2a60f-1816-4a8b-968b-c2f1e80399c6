---
noteId: "b9481eb0447111f0ac19dba74f464ba5"
tags: []

---

# 概览页面筛选功能改进文档

## 改进概述

本次改进为项目管理仪表盘的概览页面增加了全面的筛选功能，提升了用户体验和数据分析能力。

## 重要修复：前后端通信问题

### 问题诊断
在实施筛选功能时发现数据无法正常加载，经过调试发现是前后端参数格式不匹配：
- **后端期望**：扁平化参数结构，如 `{status_ids: [37,38], stage_ids: [1,2]}`
- **前端发送**：嵌套参数结构，如 `{project_status: {status_ids: [37,38]}}`
- **根本原因**：后端使用了`#[serde(flatten)]`属性，期望扁平化JSON结构

### 解决方案

#### 1. 后端模型修复 (`src-tauri/src/models/dashboard.rs`)
```rust
// 修改前：使用嵌套结构和flatten属性
pub struct DashboardFilterParams {
    #[serde(flatten)]
    pub project_status: Option<ProjectStatusFilterParams>,
    // ...
}

// 修改后：使用扁平化结构
pub struct DashboardFilterParams {
    pub start_date: Option<String>,
    pub end_date: Option<String>,
    pub status_ids: Option<Vec<i64>>,
    pub stage_ids: Option<Vec<i64>>,
    pub recruitment_ids: Option<Vec<i64>>,
    pub sponsor_ids: Option<Vec<i64>>,
    pub disease_ids: Option<Vec<i64>>,
}
```

#### 2. 前端服务层修复 (`src/lib/services/dashboardService.ts`)
```typescript
// 新增扁平化转换函数
function flattenFilterParams(filters?: DashboardFilterParams): FlatDashboardFilterParams {
  if (!filters) return {};
  
  const flattened: FlatDashboardFilterParams = {};
  
  if (filters.project_status?.status_ids) {
    flattened.status_ids = filters.project_status.status_ids;
  }
  if (filters.project_stage?.stage_ids) {
    flattened.stage_ids = filters.project_stage.stage_ids;
  }
  // ... 其他字段转换
  
  return flattened;
}

// 更新所有服务方法使用扁平化参数
async getDashboardOverview(filterParams?: DashboardFilterParams) {
  const flatParams = flattenFilterParams(filterParams);
  return await invoke('get_dashboard_overview', {
    filterParams: flatParams,
    dbPath: DB_PATH
  });
}
```

#### 3. 页面数据加载修复
- 更新所有数据加载函数使用统一的扁平化参数转换
- 添加详细的调试日志便于问题排查
- 确保筛选器状态卡片仅在概览页面显示

### 修复验证
创建了测试脚本 `test_dashboard_communication.js` 用于验证前后端通信：
```javascript
const testFilters = {
  status_ids: [37, 38],
  stage_ids: null,
  // ... 其他扁平化参数
};

const overview = await invoke('get_dashboard_overview', {
  filterParams: testFilters,
  dbPath: '/Users/<USER>/我的文档/sqlite/peckbyte.db'
});
```

## 主要改进内容

### 1. 新增筛选器组件

#### 1.1 项目阶段筛选器 (`ProjectStageFilter.svelte`)
- **功能**: 基于研究分期字典（字典ID: 7）进行筛选
- **特性**: 
  - 默认全选所有阶段
  - 支持多选
  - 显示选择状态统计
  - 提供全选/清除快捷操作

#### 1.2 招募状态筛选器 (`RecruitmentStatusFilter.svelte`)
- **功能**: 基于招募状态字典（字典ID: 10）进行筛选
- **特性**: 
  - 默认全选所有状态
  - 支持多选
  - 显示选择状态统计
  - 提供全选/清除快捷操作

#### 1.3 改进的项目状态筛选器
- **改进**: 使用新的数据服务接口
- **增强**: 添加全选/清除功能和状态统计

#### 1.4 改进的疾病领域筛选器
- **改进**: 简化界面，移除搜索功能
- **增强**: 添加全选/清除功能和状态统计

### 2. 筛选器快速操作组件 (`FilterQuickActions.svelte`)

#### 2.1 功能特性
- **筛选器状态显示**: 显示已激活筛选器数量
- **快速切换**: 一键显示/隐藏筛选器面板
- **一键重置**: 快速清除所有筛选条件
- **预设筛选方案**: 提供常用筛选组合

#### 2.2 预设筛选方案
- **活动项目**: 在研 + 招募中状态
- **招募项目**: 仅招募中状态
- **已完成项目**: 已结束状态
- **肿瘤项目**: 肿瘤学领域

### 3. 改进的筛选器面板布局

#### 3.1 响应式网格布局
- **桌面端**: 6列网格，日期范围占2列
- **平板端**: 3列网格
- **移动端**: 单列布局

#### 3.2 申办方筛选独立区域
- **原因**: 申办方选项较多，需要更多空间
- **布局**: 单独一行，与筛选状态总览并列

#### 3.3 筛选状态总览面板
- **功能**: 实时显示各筛选器的选择状态
- **视觉**: 使用不同颜色区分各类筛选器
- **信息**: 显示已选择数量和设置状态

### 4. 概览页面显示改进

#### 4.1 图表布局优化
- **原布局**: 2x2网格
- **新布局**: 2x3网格（桌面端3列）
- **新增**: 项目阶段分布图表

#### 4.2 筛选结果统计面板
- **位置**: 筛选器面板下方
- **内容**: 基于当前筛选条件的关键指标
- **指标**: 
  - 匹配项目总数
  - 活动项目数
  - 招募中项目
  - 状态类型数
  - 疾病领域数
  - 申办方数量

### 5. 数据服务层改进

#### 5.1 新增筛选参数类型
```typescript
// 项目阶段筛选参数
export interface ProjectStageFilterParams {
  stage_ids?: number[];
}

// 招募状态筛选参数
export interface RecruitmentStatusFilterParams {
  recruitment_ids?: number[];
}
```

#### 5.2 扩展仪表盘筛选参数
```typescript
export interface DashboardFilterParams {
  date_range?: DateRangeParams;
  project_status?: ProjectStatusFilterParams;
  project_stage?: ProjectStageFilterParams;        // 新增
  recruitment_status?: RecruitmentStatusFilterParams; // 新增
  sponsor?: SponsorFilterParams;
  disease?: DiseaseFilterParams;
}
```

#### 5.3 SQLite Explorer服务
- **文件**: `src/lib/services/sqliteExplorerService.ts`
- **功能**: 封装数据库查询操作
- **特性**: 提供模拟数据用于开发测试

## 用户体验改进

### 1. 筛选器默认行为
- **原则**: 默认全选，用户看到完整数据
- **优势**: 避免初次进入页面时数据为空的困惑

### 2. 视觉反馈
- **选择状态**: 实时显示已选择数量
- **颜色编码**: 不同筛选器使用不同颜色
- **加载状态**: 显示加载动画和错误信息

### 3. 操作便利性
- **快捷按钮**: 全选、清除、重置操作
- **预设方案**: 常用筛选组合一键应用
- **状态总览**: 一目了然的筛选状态

## 技术实现要点

### 1. 响应式设计
- **Tailwind CSS**: 使用响应式类名
- **网格布局**: 适配不同屏幕尺寸
- **组件化**: 可复用的筛选器组件

### 2. 状态管理
- **Svelte Stores**: 统一的筛选状态管理
- **响应式更新**: 筛选条件变化时自动重新加载数据
- **事件驱动**: 组件间通过事件通信

### 3. 类型安全
- **TypeScript**: 完整的类型定义
- **接口规范**: 统一的事件和数据接口
- **错误处理**: 完善的错误处理机制

## 使用指南

### 1. 基本筛选操作
1. 点击筛选器快速操作面板中的"显示"按钮
2. 在筛选器面板中选择所需的筛选条件
3. 查看筛选结果统计面板了解匹配的数据量
4. 观察概览图表的实时更新

### 2. 快速筛选
1. 使用预设筛选方案快速应用常用组合
2. 使用"重置"按钮清除所有筛选条件
3. 查看筛选状态总览了解当前设置

### 3. 高级筛选
1. 组合多个筛选器实现精确筛选
2. 使用日期范围限定时间窗口
3. 利用申办方筛选分析特定合作伙伴

## 后续扩展建议

### 1. 筛选器增强
- 添加更多预设筛选方案
- 支持筛选条件的保存和加载
- 增加筛选历史记录

### 2. 数据导出
- 支持筛选结果的导出功能
- 提供多种导出格式（Excel、PDF等）
- 添加自定义报告生成

### 3. 高级分析
- 增加筛选条件的组合分析
- 提供筛选效果的可视化展示
- 支持筛选条件的分享功能

## 相关文件

### 新增文件
- `src/lib/components/dashboard/filters/ProjectStageFilter.svelte`
- `src/lib/components/dashboard/filters/RecruitmentStatusFilter.svelte`
- `src/lib/components/dashboard/filters/FilterQuickActions.svelte`
- `src/lib/services/sqliteExplorerService.ts`

### 修改文件
- `src/routes/dashboard/project-management/+page.svelte`
- `src/lib/components/dashboard/filters/ProjectStatusFilter.svelte`
- `src/lib/components/dashboard/filters/DiseaseFilter.svelte`
- `src/lib/services/dashboardService.ts`

## 总结

本次改进大幅提升了项目管理仪表盘概览页面的筛选功能，通过增加多维度筛选器、快速操作面板和筛选结果统计，为用户提供了更加灵活和直观的数据分析体验。改进遵循了用户友好的设计原则，确保了良好的默认行为和清晰的视觉反馈。 
noteId: "7fa7f330442411f0ab6f679a54a69364"
tags: []

---

 