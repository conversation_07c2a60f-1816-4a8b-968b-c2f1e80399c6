{"permissions": {"allow": ["Bash(ls:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(npm run tauri:*)", "Bash(npm install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git commit:*)", "Bash(npm run check:*)", "<PERSON><PERSON>(claude config list)", "<PERSON><PERSON>(uv:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__sqlite-explorer__list_tables", "mcp__sqlite-explorer__describe_table", "Bash(rg:*)", "mcp__sqlite-explorer__read_query", "<PERSON><PERSON>(curl:*)", "Bash(cargo clean:*)", "Bash(cargo:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(npm run dev:*)", "Bash(sqlite3 \"/Users/<USER>/我的文档/sqlite/peckbyte.db\" \"SELECT rule_definition_id, rule_name, rule_description, category FROM rule_definitions WHERE rule_name LIKE ''%mMRC%'' OR rule_name LIKE ''%COPD%'' OR rule_name LIKE ''%治疗%'' OR category LIKE ''%COPD%'';\")", "Bash(sqlite3 \"/Users/<USER>/我的文档/sqlite/peckbyte.db\" \"SELECT rule_definition_id, rule_name, rule_description, category, parameter_schema FROM rule_definitions WHERE rule_definition_id IN (12, 14);\")", "Bash(node analyze_copd_mmrc_issue.js)", "Bash(sqlite3 \"/Users/<USER>/我的文档/sqlite/peckbyte.db\" \"SELECT rule_definition_id, rule_name, rule_description, category FROM rule_definitions WHERE rule_name LIKE ''%EOS%'' OR rule_name LIKE ''%ACQ%'' OR rule_name LIKE ''%嗜酸%'' OR category LIKE ''%EOS%'' OR category LIKE ''%ACQ%'';\")", "Bash(sqlite3 \"/Users/<USER>/我的文档/sqlite/peckbyte.db\" \"SELECT rule_definition_id, rule_name, rule_description, category, parameter_schema FROM rule_definitions WHERE rule_definition_id IN (11, 21);\")", "Bash(node analyze_eos_acq_issue.js)", "Bash(node test_rules_format.js)", "Bash(node verify_fix.js)"], "deny": []}}