# 删除招募政策概览页面平均费用统计卡片设计文档

## 1. 概述

### 1.1 需求背景
在招募政策概览页面（通过"工具->招募政策概览"菜单进入）中，需要删除"平均知情费用"和"平均随机费用"两个统计卡片，以简化页面展示和优化用户体验。

### 1.2 技术栈
- **前端**: SvelteKit + TypeScript + Tailwind CSS
- **后端**: Rust + Tauri
- **数据库**: SQLite
- **架构模式**: 前后端分离，命令-服务-仓库分层架构

## 2. 当前系统架构分析

### 2.1 页面入口路径
```mermaid
flowchart TD
    A[应用主界面] --> B[顶部菜单栏]
    B --> C[工具菜单]
    C --> D[招募政策概览]
    D --> E[/recruitment-overview页面]
```

### 2.2 现有页面结构
基于代码分析，当前招募政策概览页面位于：
- **路由**: `/recruitment-overview`
- **文件**: `src/routes/recruitment-overview/+page.svelte`
- **功能**: 展示招募政策列表、筛选、编辑、删除操作

### 2.3 菜单配置
在`src-tauri/src/lib.rs`中的菜单定义：
```rust
let tools_submenu = SubmenuBuilder::new(app, "工具")
    .text("recruitment_overview", "招募政策概览")
    // ... 其他菜单项
```

## 3. 统计卡片功能设计

### 3.1 需要删除的卡片
根据用户需求，需要删除以下两个统计卡片：

1. **平均知情费用卡片**
   - 显示所有招募政策的知情同意费用平均值
   - 数据来源: `recruitment_company_policies.informed_consent_fee`

2. **平均随机费用卡片**
   - 显示所有招募政策的随机化费用平均值
   - 数据来源: `recruitment_company_policies.randomization_fee`

### 3.2 卡片组件设计
```mermaid
classDiagram
    class StatisticsCard {
        +title: string
        +value: number
        +format: 'currency'
        +icon: Component
        +color: string
        +loading: boolean
    }
    
    class AverageFeeStats {
        +averageInformedFee: number
        +averageRandomizationFee: number
        +calculateAverages()
        +formatCurrency()
    }
```

### 3.3 数据流架构
```mermaid
sequenceDiagram
    participant UI as 招募政策概览页面
    participant Service as recruitmentPolicyService
    participant Backend as Tauri Commands
    participant DB as SQLite Database
    
    UI->>Service: 请求统计数据
    Service->>Backend: get_recruitment_fee_statistics()
    Backend->>DB: 查询平均费用
    DB-->>Backend: 返回统计结果
    Backend-->>Service: 返回统计数据
    Service-->>UI: 更新统计卡片
```

## 4. 删除实现方案

### 4.1 前端修改

#### 4.1.1 页面组件调整
在`src/routes/recruitment-overview/+page.svelte`中：

1. **移除统计卡片区域**
```typescript
// 删除统计数据相关的状态
// let feeStatistics = $state<{
//   averageInformedFee: number;
//   averageRandomizationFee: number;
// } | null>(null);

// 删除加载统计数据的函数
// async function loadFeeStatistics() { ... }
```

2. **移除UI组件**
```svelte
<!-- 删除统计卡片展示区域 -->
<!-- 
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
  <StatisticsCard 
    title="平均知情费用" 
    value={feeStatistics?.averageInformedFee || 0}
    format="currency"
    icon={DollarSign}
    color="blue"
  />
  <StatisticsCard 
    title="平均随机费用" 
    value={feeStatistics?.averageRandomizationFee || 0}
    format="currency" 
    icon={CreditCard}
    color="green"
  />
</div>
-->
```

#### 4.1.2 服务层调整
在`src/lib/services/recruitmentPolicyService.ts`中：

1. **移除统计接口定义**
```typescript
// 删除费用统计相关的接口
// export interface RecruitmentFeeStatistics {
//   averageInformedFee: number;
//   averageRandomizationFee: number;
//   totalPolicies: number;
// }
```

2. **移除统计方法**
```typescript
// 删除获取统计数据的方法
// async getFeeStatistics(): Promise<RecruitmentFeeStatistics>
```

### 4.2 后端修改

#### 4.2.1 命令层调整
在`src-tauri/src/commands/recruitment_policy_commands.rs`中：

```rust
// 删除或注释掉统计命令
// #[command]
// pub fn get_recruitment_fee_statistics(
//     db_path: String,
// ) -> Result<RecruitmentFeeStatistics, String>
```

#### 4.2.2 服务层调整
在相关服务文件中移除统计计算逻辑：

```rust
// 删除费用统计计算方法
// impl RecruitmentPolicyService {
//     pub fn calculate_fee_statistics(&self) -> Result<RecruitmentFeeStatistics, Error>
// }
```

#### 4.2.3 数据模型调整
移除统计相关的数据结构定义：

```rust
// 在models中删除
// #[derive(Debug, Serialize, Deserialize)]
// pub struct RecruitmentFeeStatistics {
//     pub average_informed_fee: f64,
//     pub average_randomization_fee: f64,
//     pub total_policies: i64,
// }
```

### 4.3 Tauri命令注册调整
在`src-tauri/src/lib.rs`中：

```rust
// 从invoke_handler中移除统计命令
.invoke_handler(tauri::generate_handler![
    // ... 其他命令
    // get_recruitment_fee_statistics, // 删除这行
])
```

## 5. 数据库影响分析

### 5.1 表结构保持不变
删除统计卡片不影响现有数据库结构，以下表格保持原样：
- `recruitment_company_policies` - 招募政策主表
- `projects` - 项目信息表
- `dictionary_items` - 字典数据表

### 5.2 数据完整性
移除统计功能不会影响：
- 现有招募政策数据的完整性
- 项目与招募政策的关联关系
- 其他业务功能的正常运行

## 6. 用户体验优化

### 6.1 页面布局调整
移除统计卡片后的页面布局：

```mermaid
graph TD
    A[页面标题栏] --> B[筛选器面板]
    B --> C[操作按钮区域]
    C --> D[招募政策数据表格]
    D --> E[分页控件]
```

### 6.2 界面优化建议
1. **空间利用**: 移除卡片后释放的空间可用于扩展筛选选项
2. **视觉焦点**: 将用户注意力集中在招募政策列表的核心功能上
3. **操作流程**: 简化页面层次，提升数据查看和管理效率

## 7. 测试策略

### 7.1 功能测试
- ✅ 验证统计卡片已完全移除
- ✅ 确认其他功能正常运行（筛选、新增、编辑、删除）
- ✅ 检查页面加载性能优化效果

### 7.2 界面测试
- ✅ 验证页面布局在移除卡片后的视觉效果
- ✅ 确认响应式设计在不同屏幕尺寸下的表现
- ✅ 检查无统计数据时的页面状态

### 7.3 集成测试
- ✅ 验证菜单导航功能正常
- ✅ 确认与其他页面的数据关联不受影响
- ✅ 测试权限控制和错误处理机制

## 8. 实施步骤

### 8.1 开发阶段
1. **前端修改** (预计1小时)
























































































































































































































































