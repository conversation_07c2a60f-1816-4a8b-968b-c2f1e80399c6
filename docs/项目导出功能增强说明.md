# 项目管理模块导出功能增强说明

## 概述

本次增强为项目管理模块的导出功能添加了更全面的数据导出能力，支持批量选择、完整数据导出和灵活的过滤选项。

## 主要增强功能

### 1. 扩展导出数据范围

#### 后端增强
- **新增命令**: `get_projects_complete_export_data`
  - 支持导出包含入排标准的完整项目数据
  - 自动包含所有关联数据（申办方、研究药物、人员角色、补贴方案等）
  - 添加导出元数据（导出时间、版本信息等）

#### 数据完整性
- 所有外键关联数据都被正确解析为显示值
- 包含完整的时间戳信息
- 确保数据导出时的一致性和准确性

### 2. 导出格式优化

#### CSV格式增强
- **核心字段**: 包含基础字段 + 动态角色列
  - 基本信息：项目全称、项目简称、疾病、研究分期、项目状态、招募状态
  - **时间信息增强**：启动日期（格式：YYYY年MM月DD日（已启动X天））
  - 药物信息：药物作用机制、药物介绍
  - 关联数据：申办方、研究药物、药物分组、补贴方案
  - **人员角色分列**: 根据项目中实际存在的角色类型动态生成列（如：PI、CRC、CRA、统计师等）
  - **入组标准字段**: 6个特定的入组标准字段独立成列（年龄、慢阻肺/哮喘吸入药物维持治疗、慢阻肺病或哮喘急性发作病史、支气管舒张剂前FEV1、确诊史、血嗜酸粒细胞计数）
  - 统计信息：入排标准数量
- **数据格式化**:
  - 多个关联项用分号分隔，便于Excel分析
  - 同一角色的多个人员用逗号分隔
  - 缺少特定角色的项目在对应列显示为空
  - **启动日期智能格式化**：
    - 已启动项目：`2025年08月30日（已启动1天）`
    - 未启动项目：`2025年09月01日（未启动）`
    - 今日启动：`2025年08月30日（今日启动）`
    - 未设置日期：`未设置启动日期`
  - **入组标准智能提取和格式化**：
    - 仅提取入组标准（inclusion criteria），忽略排除标准
    - 按字段名称精确匹配6个特定标准
    - 缺少的标准字段显示为空
    - **字段特定格式化**：
      - 年龄：`最大年龄: 75 岁, 最小年龄: 18 岁`
      - 维持治疗：`维持治疗药物类型: 中至高剂量ICS治疗, 至少维持使用: 3 月, 剂量稳定至少: 1 月`
      - 确诊史：`12个月` (自动添加单位)
      - 急性发作史：`12个月内1次重度发作`
      - FEV1：`FEV1 30-80% (支气管舒张剂前)`
      - 血嗜酸粒细胞：`≥150 cells/μL`
- **简化设计**: 移除了项目ID、项目路径、最后更新时间、导出时间等技术性字段，专注于核心业务信息
- **动态列生成**: 根据所有项目中存在的角色类型自动生成对应的列，确保数据结构一致

#### JSON格式增强
- **完整数据结构**: 保持所有嵌套关联数据的完整性
- **元数据信息**: 包含导出配置、过滤条件、选择模式、角色类型列表等
- **版本控制**: 导出版本从1.0.0升级到2.0.0
- **数据清理**: 自动移除技术性字段（项目ID、项目路径、最后更新时间、导出时间），保留核心业务数据
- **人员角色结构化**: 使用 `personnel_by_role` 对象，按角色类型组织人员数据，便于程序化处理
- **启动日期双格式**: 同时提供格式化的启动日期（含天数）和原始日期数据，满足显示和程序处理需求
- **入组标准结构化**: 使用 `inclusion_criteria` 对象，按字段名称组织入组标准数据，便于程序化分析

### 3. 用户体验改进

#### 项目选择功能
- **选择模式**: 支持"按条件过滤"和"手动选择项目"两种模式
- **批量操作**: 支持全选/取消全选功能
- **可视化选择**: 项目列表显示项目名称、简称和疾病信息

#### 导出选项配置
- **内容选择**: 可选择是否包含入排标准、人员信息、补贴信息、药物信息、申办方信息
- **灵活过滤**: 支持按研究分期、项目状态、疾病类型进行过滤
- **智能提示**: 实时显示将要导出的项目数量和内容

#### 进度指示和反馈
- **导出进度**: 显示"导出中..."状态
- **成功反馈**: 显示文件保存路径和打开文件夹选项
- **错误处理**: 详细的错误信息和重试选项

## 技术实现

### 后端实现

#### 新增查询参数结构
```rust
pub struct ProjectExportQuery {
    pub project_status_item_id: Option<i64>,
    pub recruitment_status_item_id: Option<i64>,
    pub disease_item_id: Option<i64>,
    pub project_stage_item_id: Option<i64>,
    pub project_ids: Option<Vec<String>>,
    pub include_criteria: Option<bool>,
}
```

#### 核心命令
- `get_projects_export_data`: 基础导出功能，支持过滤和批量选择
- `get_projects_complete_export_data`: 完整导出功能，包含入排标准

### 前端实现

#### 服务层增强
- `getProjectsCompleteExportData`: 获取完整导出数据
- `exportSelectedProjects`: 批量导出指定项目

#### UI组件增强
- 项目选择界面
- 导出选项配置
- 增强的CSV和JSON内容生成

## 使用方法

### 1. 访问导出功能
- 在项目列表页面点击"导出项目数据"按钮
- 选择导出模式和配置选项

### 2. 选择导出模式
- **按条件过滤**: 使用研究分期、项目状态、疾病类型进行过滤
- **手动选择**: 从项目列表中选择特定项目

### 3. 配置导出选项
- 选择要包含的数据类型（入排标准、人员信息等）
- 选择导出格式（CSV或JSON）

### 4. 执行导出
- 点击"导出数据"按钮
- 文件将保存到下载文件夹
- 可选择打开文件夹查看导出结果

## 文件命名规则

- **基本格式**: `项目完整数据_[过滤条件]_[选择信息]_时间戳.格式`
- **示例**: 
  - `项目完整数据_研究分期=I期_2024-01-15T10-30-00.csv`
  - `项目完整数据_选中5项_2024-01-15T10-30-00.json`

## 注意事项

1. **性能考虑**: 大量项目导出时可能需要较长时间
2. **数据完整性**: 确保在导出前项目数据已完整录入
3. **文件大小**: JSON格式文件通常比CSV格式更大
4. **兼容性**: CSV文件可在Excel等软件中直接打开

## 📊 导出数据示例

### CSV格式示例
**动态角色分列 + 增强启动日期 + 入组标准字段**，根据项目中实际存在的角色类型生成列：
```csv
项目全称,项目简称,疾病,研究分期,项目状态,招募状态,启动日期,药物作用机制,药物介绍,申办方,研究药物,药物分组,PI,CRC,CRA,统计师,年龄,慢阻肺、哮喘吸入药物维持治疗,慢阻肺病或哮喘急性发作病史,支气管舒张剂前（Pre-BD）FEV1,确诊史,血嗜酸粒细胞计数,补贴方案,入排标准数量
"某肿瘤药物临床试验","TUMOR-001","肺癌","III期","进行中","招募中","2024年01月15日（已启动229天）","靶向治疗","新型靶向药物","某制药公司","试验药物A","对照组(50%);试验组(50%)","张医生","李护士,王护士","陈专员","赵统计师","最大年龄: 75 岁, 最小年龄: 18 岁","维持治疗药物类型: 中至高剂量ICS治疗, 至少维持使用: 3 月, 剂量稳定至少: 1 月","12个月内1次重度发作","FEV1 30-80% (支气管舒张剂前)","12个月","≥150 cells/μL","标准方案(50000元)","15"
"未来项目试验","FUTURE-001","胃癌","I期","筹备中","未开始","2025年09月01日（未启动）","免疫治疗","新型免疫药物","另一制药公司","试验药物B","单组试验(100%)","王医生","","","","","","","","","","筹备方案(30000元)","8"
```

### JSON格式示例
**结构化人员角色数据 + 双格式启动日期 + 入组标准字段**：
```json
{
  "metadata": {
    "export_version": "2.0.0",
    "total_projects": 1,
    "role_types": ["PI", "CRC", "CRA", "统计师"],
    "inclusion_criteria_fields": ["年龄", "慢阻肺、哮喘吸入药物维持治疗", "慢阻肺病或哮喘急性发作病史", "支气管舒张剂前（Pre-BD）FEV1", "确诊史", "血嗜酸粒细胞计数"],
    "filters": {...}
  },
  "projects": [
    {
      "project_name": "某肿瘤药物临床试验",
      "project_short_name": "TUMOR-001",
      "disease": "肺癌",
      "project_stage": "III期",
      "project_status": "进行中",
      "recruitment_status": "招募中",
      "project_start_date": "2024年01月15日（已启动229天）",
      "project_start_date_raw": "2024-01-15",
      "drug_mechanism": "靶向治疗",
      "drug_introduction": "新型靶向药物",
      "personnel_by_role": {
        "PI": ["张医生"],
        "CRC": ["李护士", "王护士"],
        "CRA": ["陈专员"],
        "统计师": ["赵统计师"]
      },
      "inclusion_criteria": {
        "年龄": "最大年龄: 75 岁, 最小年龄: 18 岁",
        "慢阻肺、哮喘吸入药物维持治疗": "维持治疗药物类型: 中至高剂量ICS治疗, 至少维持使用: 3 月, 剂量稳定至少: 1 月",
        "慢阻肺病或哮喘急性发作病史": "12个月内1次重度发作",
        "支气管舒张剂前（Pre-BD）FEV1": "FEV1 30-80% (支气管舒张剂前)",
        "确诊史": "12个月",
        "血嗜酸粒细胞计数": "≥150 cells/μL"
      }
    }
  ]
}
```

## 后续优化建议

1. 添加导出进度条显示
2. 支持自定义导出字段选择
3. 添加导出历史记录功能
4. 支持定时自动导出
5. 添加导出数据验证功能
