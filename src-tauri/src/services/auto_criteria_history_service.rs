use std::error::Error;

use crate::models::auto_criteria_history::{
    AutoCriteriaHistory, AutoCriteriaHistoryQuery, CreateAutoCriteriaHistoryRequest,
};
use crate::repositories::auto_criteria_history_repository::AutoCriteriaHistoryRepository;

pub struct AutoCriteriaHistoryService {
    repo: AutoCriteriaHistoryRepository,
}

impl AutoCriteriaHistoryService {
    pub fn new(db_path: String) -> Self {
        Self {
            repo: AutoCriteriaHistoryRepository::new(db_path),
        }
    }

    pub fn init_tables(&self) -> Result<(), Box<dyn Error>> {
        self.repo.init_tables()
    }

    pub fn create(&self, req: &CreateAutoCriteriaHistoryRequest) -> Result<i64, Box<dyn Error>> {
        self.repo.create(req)
    }

    pub fn list(
        &self,
        q: &AutoCriteriaHistoryQuery,
    ) -> Result<Vec<AutoCriteriaHistory>, Box<dyn Error>> {
        self.repo.list(q)
    }

    pub fn get(&self, history_id: i64) -> Result<Option<AutoCriteriaHistory>, Box<dyn Error>> {
        self.repo.get(history_id)
    }

    pub fn delete(&self, history_id: i64) -> Result<(), Box<dyn Error>> {
        self.repo.delete(history_id)
    }
}
