use chrono::Utc;
use rusqlite::{params, Connection, OptionalExtension};
use std::error::Error;

use crate::models::rule_designer::{
    ProjectCriterion, ProjectCriterionQuery, ProjectCriterionWithRule, RuleDefinition,
    RuleDefinitionQuery,
};

/// 规则设计器仓库
pub struct RuleDesignerRepository {
    db_path: String,
}

impl RuleDesignerRepository {
    /// 创建新的规则设计器仓库
    pub fn new(db_path: String) -> Self {
        Self { db_path }
    }

    /// 获取数据库连接
    fn get_connection(&self) -> Result<Connection, Box<dyn Error>> {
        let conn = Connection::open(&self.db_path)?;
        // 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON", [])?;
        Ok(conn)
    }

    /// 初始化所有相关表
    pub fn init_tables(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 创建规则定义表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS rule_definitions (
                rule_definition_id INTEGER PRIMARY KEY AUTOINCREMENT,
                rule_name TEXT NOT NULL,
                rule_description TEXT,
                category TEXT,
                parameter_schema TEXT NOT NULL,
                label TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )",
            [],
        )?;

        // 添加 label 字段（如果不存在）
        let _ = conn.execute("ALTER TABLE rule_definitions ADD COLUMN label TEXT", []);

        // 创建项目标准表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS project_criteria (
                project_criterion_id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id TEXT NOT NULL,
                rule_definition_id INTEGER NOT NULL,
                criterion_type TEXT NOT NULL,
                parameter_values TEXT NOT NULL,
                is_active INTEGER NOT NULL DEFAULT 1,
                display_order INTEGER,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                criteria_group_id TEXT,
                group_operator TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (project_id),
                FOREIGN KEY (rule_definition_id) REFERENCES rule_definitions (rule_definition_id)
            )",
            [],
        )?;

        // 创建索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_project_criteria_project_id ON project_criteria(project_id)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_project_criteria_rule_definition_id ON project_criteria(rule_definition_id)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_rule_definitions_category ON rule_definitions(category)", [])?;

        Ok(())
    }

    /// 获取所有规则定义
    pub fn get_rule_definitions(
        &self,
        query: &RuleDefinitionQuery,
    ) -> Result<Vec<RuleDefinition>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut sql = String::from("SELECT rule_definition_id, rule_name, rule_description, category, parameter_schema, label, created_at, updated_at FROM rule_definitions");

        let mut params = Vec::new();

        if let Some(category) = &query.category {
            sql.push_str(" WHERE category = ?");
            params.push(category.clone());
        }

        sql.push_str(" ORDER BY rule_name");

        let mut stmt = conn.prepare(&sql)?;

        let mut rules = Vec::new();

        // 使用函数来处理查询结果，避免闭包类型不匹配的问题
        let map_fn = |row: &rusqlite::Row| -> rusqlite::Result<RuleDefinition> {
            Ok(RuleDefinition {
                rule_definition_id: row.get(0)?,
                rule_name: row.get(1)?,
                rule_description: row.get(2)?,
                category: row.get(3)?,
                parameter_schema: row.get(4)?,
                label: row.get(5)?,
                created_at: row.get(6)?,
                updated_at: row.get(7)?,
            })
        };

        if params.is_empty() {
            let mut rows = stmt.query([])?;
            while let Some(row) = rows.next()? {
                rules.push(map_fn(row)?);
            }
        } else {
            let param_ref = &params[0];
            let mut rows = stmt.query([param_ref])?;
            while let Some(row) = rows.next()? {
                rules.push(map_fn(row)?);
            }
        }

        Ok(rules)
    }

    /// 根据ID获取规则定义
    pub fn get_rule_definition_by_id(
        &self,
        rule_definition_id: i64,
    ) -> Result<Option<RuleDefinition>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT rule_definition_id, rule_name, rule_description, category, parameter_schema, label, created_at, updated_at
             FROM rule_definitions
             WHERE rule_definition_id = ?"
        )?;

        let mut rows = stmt.query([rule_definition_id])?;

        if let Some(row) = rows.next()? {
            Ok(Some(RuleDefinition {
                rule_definition_id: row.get(0)?,
                rule_name: row.get(1)?,
                rule_description: row.get(2)?,
                category: row.get(3)?,
                parameter_schema: row.get(4)?,
                label: row.get(5)?,
                created_at: row.get(6)?,
                updated_at: row.get(7)?,
            }))
        } else {
            Ok(None)
        }
    }

    /// 创建规则定义
    pub fn create_rule_definition(&self, rule: &RuleDefinition) -> Result<i64, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let now = Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

        conn.execute(
            "INSERT INTO rule_definitions (rule_name, rule_description, category, parameter_schema, label, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            params![
                rule.rule_name,
                rule.rule_description,
                rule.category,
                rule.parameter_schema,
                rule.label,
                now,
                now
            ],
        )?;

        let id = conn.last_insert_rowid();

        Ok(id)
    }

    /// 更新规则定义
    pub fn update_rule_definition(
        &self,
        rule_definition_id: i64,
        rule: &RuleDefinition,
    ) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let now = Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

        conn.execute(
            "UPDATE rule_definitions
             SET rule_name = ?, rule_description = ?, category = ?, parameter_schema = ?, label = ?, updated_at = ?
             WHERE rule_definition_id = ?",
            params![
                rule.rule_name,
                rule.rule_description,
                rule.category,
                rule.parameter_schema,
                rule.label,
                now,
                rule_definition_id
            ],
        )?;

        Ok(())
    }

    /// 查找使用特定规则定义的项目
    pub fn find_projects_using_rule(
        &self,
        rule_definition_id: i64,
    ) -> Result<Vec<(String, String, String)>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT DISTINCT pc.project_id, p.project_name, pc.criterion_type
             FROM project_criteria pc
             JOIN projects p ON pc.project_id = p.project_id
             WHERE pc.rule_definition_id = ?
             ORDER BY p.project_name",
        )?;

        let rows = stmt.query_map([rule_definition_id], |row| {
            Ok((
                row.get::<_, String>(0)?,
                row.get::<_, String>(1)?,
                row.get::<_, String>(2)?,
            ))
        })?;

        let mut projects = Vec::new();
        for row in rows {
            projects.push(row?);
        }

        Ok(projects)
    }

    /// 删除规则定义
    pub fn delete_rule_definition(&self, rule_definition_id: i64) -> Result<(), Box<dyn Error>> {
        let mut conn = self.get_connection()?;

        // 开始事务
        let tx = conn.transaction()?;

        // 先删除关联的项目标准
        tx.execute(
            "DELETE FROM project_criteria WHERE rule_definition_id = ?",
            params![rule_definition_id],
        )?;

        // 然后删除规则定义
        tx.execute(
            "DELETE FROM rule_definitions WHERE rule_definition_id = ?",
            params![rule_definition_id],
        )?;

        // 提交事务
        tx.commit()?;

        Ok(())
    }

    /// 获取项目标准
    pub fn get_project_criteria(
        &self,
        query: &ProjectCriterionQuery,
    ) -> Result<Vec<ProjectCriterionWithRule>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut sql = String::from(
            "SELECT
                pc.project_criterion_id, pc.project_id, pc.rule_definition_id, pc.criterion_type,
                pc.parameter_values, pc.is_active, pc.display_order, pc.created_at, pc.updated_at,
                pc.criteria_group_id, pc.group_operator,
                rd.rule_definition_id, rd.rule_name, rd.rule_description, rd.category,
                rd.parameter_schema, rd.label, rd.created_at, rd.updated_at
             FROM project_criteria pc
             JOIN rule_definitions rd ON pc.rule_definition_id = rd.rule_definition_id
             WHERE pc.project_id = ?",
        );

        let mut params = vec![query.project_id.clone()];

        if let Some(criterion_type) = &query.criterion_type {
            sql.push_str(" AND pc.criterion_type = ?");
            params.push(criterion_type.clone());
        }

        sql.push_str(" ORDER BY pc.display_order, pc.created_at");

        let mut stmt = conn.prepare(&sql)?;

        let mut criteria = Vec::new();

        // 使用函数来处理查询结果，避免闭包类型不匹配的问题
        let map_fn = |row: &rusqlite::Row| -> rusqlite::Result<ProjectCriterionWithRule> {
            Ok(ProjectCriterionWithRule {
                criterion: ProjectCriterion {
                    project_criterion_id: row.get(0)?,
                    project_id: row.get(1)?,
                    rule_definition_id: row.get(2)?,
                    criterion_type: row.get(3)?,
                    parameter_values: row.get(4)?,
                    is_active: row.get(5)?,
                    display_order: row.get(6)?,
                    created_at: row.get(7)?,
                    updated_at: row.get(8)?,
                    criteria_group_id: row.get(9)?,
                    group_operator: row.get(10)?,
                },
                rule_definition: RuleDefinition {
                    rule_definition_id: row.get(11)?,
                    rule_name: row.get(12)?,
                    rule_description: row.get(13)?,
                    category: row.get(14)?,
                    parameter_schema: row.get(15)?,
                    label: row.get(16)?,
                    created_at: row.get(17)?,
                    updated_at: row.get(18)?,
                },
            })
        };

        if params.len() == 1 {
            let param_ref = &params[0];
            let mut rows = stmt.query([param_ref])?;
            while let Some(row) = rows.next()? {
                criteria.push(map_fn(row)?);
            }
        } else {
            let param_ref1 = &params[0];
            let param_ref2 = &params[1];
            let mut rows = stmt.query([param_ref1, param_ref2])?;
            while let Some(row) = rows.next()? {
                criteria.push(map_fn(row)?);
            }
        }

        Ok(criteria)
    }

    /// 根据ID获取项目标准
    pub fn get_project_criterion_by_id(
        &self,
        project_criterion_id: i64,
    ) -> Result<Option<ProjectCriterionWithRule>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT
                pc.project_criterion_id, pc.project_id, pc.rule_definition_id, pc.criterion_type,
                pc.parameter_values, pc.is_active, pc.display_order, pc.created_at, pc.updated_at,
                pc.criteria_group_id, pc.group_operator,
                rd.rule_definition_id, rd.rule_name, rd.rule_description, rd.category,
                rd.parameter_schema, rd.label, rd.created_at, rd.updated_at
             FROM project_criteria pc
             JOIN rule_definitions rd ON pc.rule_definition_id = rd.rule_definition_id
             WHERE pc.project_criterion_id = ?",
        )?;

        let mut rows = stmt.query([project_criterion_id])?;

        if let Some(row) = rows.next()? {
            Ok(Some(ProjectCriterionWithRule {
                criterion: ProjectCriterion {
                    project_criterion_id: row.get(0)?,
                    project_id: row.get(1)?,
                    rule_definition_id: row.get(2)?,
                    criterion_type: row.get(3)?,
                    parameter_values: row.get(4)?,
                    is_active: row.get(5)?,
                    display_order: row.get(6)?,
                    created_at: row.get(7)?,
                    updated_at: row.get(8)?,
                    criteria_group_id: row.get(9)?,
                    group_operator: row.get(10)?,
                },
                rule_definition: RuleDefinition {
                    rule_definition_id: row.get(11)?,
                    rule_name: row.get(12)?,
                    rule_description: row.get(13)?,
                    category: row.get(14)?,
                    parameter_schema: row.get(15)?,
                    label: row.get(16)?,
                    created_at: row.get(17)?,
                    updated_at: row.get(18)?,
                },
            }))
        } else {
            Ok(None)
        }
    }

    /// 创建项目标准
    pub fn create_project_criterion(
        &self,
        criterion: &ProjectCriterion,
    ) -> Result<i64, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let now = Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

        // 如果没有指定显示顺序，获取当前最大显示顺序并加1
        let display_order = if let Some(order) = criterion.display_order {
            order
        } else {
            let mut stmt = conn.prepare(
                "SELECT COALESCE(MAX(display_order), 0) + 1
                 FROM project_criteria
                 WHERE project_id = ? AND criterion_type = ?",
            )?;

            stmt.query_row(
                params![criterion.project_id, criterion.criterion_type],
                |row| row.get(0),
            )?
        };

        conn.execute(
            "INSERT INTO project_criteria (
                project_id, rule_definition_id, criterion_type, parameter_values,
                is_active, display_order, created_at, updated_at, criteria_group_id, group_operator
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            params![
                criterion.project_id,
                criterion.rule_definition_id,
                criterion.criterion_type,
                criterion.parameter_values,
                criterion.is_active.unwrap_or(true),
                display_order,
                now,
                now,
                criterion.criteria_group_id,
                criterion.group_operator
            ],
        )?;

        let id = conn.last_insert_rowid();

        Ok(id)
    }

    /// 更新项目标准
    pub fn update_project_criterion(
        &self,
        project_criterion_id: i64,
        criterion: &ProjectCriterion,
    ) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let now = Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

        conn.execute(
            "UPDATE project_criteria
             SET rule_definition_id = ?, criterion_type = ?, parameter_values = ?,
                 is_active = ?, display_order = ?, updated_at = ?, criteria_group_id = ?, group_operator = ?
             WHERE project_criterion_id = ?",
            params![
                criterion.rule_definition_id,
                criterion.criterion_type,
                criterion.parameter_values,
                criterion.is_active,
                criterion.display_order,
                now,
                criterion.criteria_group_id,
                criterion.group_operator,
                project_criterion_id
            ],
        )?;

        Ok(())
    }

    /// 删除项目标准
    pub fn delete_project_criterion(
        &self,
        project_criterion_id: i64,
    ) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        conn.execute(
            "DELETE FROM project_criteria WHERE project_criterion_id = ?",
            params![project_criterion_id],
        )?;

        Ok(())
    }
}
