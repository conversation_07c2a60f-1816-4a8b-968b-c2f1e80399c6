<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import settings, { loadSettings } from '$lib/stores/settings';
  import { CriteriaGenerator, type RuleDefinition } from '$lib/services/criteriaGeneratorService';
  import { ruleDesignerService } from '$lib/services/ruleDesignerService';
  import { projectManagementService, type ProjectWithDetails } from '$lib/services/projectManagementService';
  import RuleDefinitionForm from '$lib/components/rule-designer/RuleDefinitionForm.svelte';
  import { Check, AlertCircle, Plus, Edit, Search, Loader2 } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import { segmentText as smartSegmentText } from '$lib/utils/criteriaTextProcessor';
  import { autoCriteriaHistoryService, type AutoCriteriaHistory } from '$lib/services/autoCriteriaHistoryService';

  // State variables
  let inputText = $state('');
  let segmentedText = $state('');
  let outputJson = $state('');
  let isProcessing = $state(false);
  let progress = $state({ current: 0, total: 0, success: 0, failed: 0 });
  let selectedModelId = $state('');
  let promptTemplate = $state('');
  let showPromptDialog = $state(false);
  let showProjectImportDialog = $state(false);
  let errorMessage = $state('');
  let criteriaGenerator = $state<CriteriaGenerator | null>(null);

  // Project import related state
  let projectSearchQuery = $state('');
  let isSearchingProjects = $state(false);
  let projectSearchResults = $state<ProjectWithDetails[]>([]);
  let selectedProject = $state<ProjectWithDetails | null>(null);
  let importingToProject = $state(false);
  let importToProjectSuccess = $state('');
  let importToProjectError = $state('');

  // 搜索下拉相关状态
  let showSearchDropdown = $state(false);
  let selectedResultIndex = $state(-1);
  let searchTimeout = $state<NodeJS.Timeout | null>(null);

  // 处理结果详情
  let processingDetails = $state<{
    matchedSegments: Array<{
      segment: string;
      rule: RuleDefinition;
      criterion: any;
      remark?: string;
    }>;
    failedSegments: Array<{
      segment: string;
      errors: Array<{
        rule: RuleDefinition;
        error: string;
      }>;
    }>;
  }>({ matchedSegments: [], failedSegments: [] });

  // 规则编辑状态
  let showEditRuleDialog = $state(false);
  let showCreateRuleDialog = $state(false);
  let suggestedRule: RuleDefinition | null = $state(null);
  let showSuggestDialog = $state(false);
  let suggestedCandidates = $state<RuleDefinition[]>([]);
  let suggestionError = $state('');
  let showRemoveConfirmDialog = $state(false);
  let showEditParamsDialog = $state(false);
  let selectedRule = $state<RuleDefinition | null>(null);
  let selectedSegment = $state('');
  let selectedMatchToRemove = $state<{segment: string; rule: RuleDefinition; criterion: any} | null>(null);
  let selectedMatchToEditParams = $state<{segment: string; rule: RuleDefinition; criterion: any} | null>(null);
  let editedParamValues = $state<Record<string, any>>({});
  let isUpdatingRule = $state(false);
  let isUpdatingParams = $state(false);
  let updateSuccess = $state('');
  let updateError = $state('');
  let activeTab = $state('json');
  let showHistoryDialog = $state(false);
  let histories = $state<AutoCriteriaHistory[]>([]);
  let historyError = $state('');
  let loadingHistories = $state(false);
  let historySearch = $state('');
  let selectedHistoryIds = $state<Record<number, boolean>>({});
  let saveNote = $state('');

  // 智能化分段
  function intelligentSegment() {
    updateError = '';
    if (!inputText || !inputText.trim()) {
      errorMessage = '请输入待分段的文本';
      return;
    }
    try {
      const segments = smartSegmentText(inputText);
      // 将分段结果回填到输入框：每段一行，以中文分号结尾
      segmentedText = segments.map(s => s.replace(/[；;]+$/,'') + '；').join('\n\n');
      updateSuccess = `已智能分段，共 ${segments.length} 段；点击“开始处理”将使用分段结果。`;
    } catch (e) {
      console.error('智能分段失败:', e);
      updateError = e instanceof Error ? e.message : '智能分段失败';
    }
  }

  // 不再使用effect监听，改为在openEditParamsDialog函数中直接设置

  // 规则列表
  let ruleDefinitions = $state<RuleDefinition[]>([]);
  let showRulesListDialog = $state(false);
  let rulesListText = $state('');

  // 未匹配段落的规则选择
  let selectedRuleForFailure = $state<Record<number, number | string>>({});

  // 可编辑的段落文本
  let editableSegments = $state<Record<number, string>>({});

  // 追加结果显示
  let appendResults = $state<Record<number, string>>({});

  // Initialize the generator on mount
  onMount(async () => {
    try {
      // First load settings if not already loaded
      if (!$settings.initialized) {
        await loadSettings();
      }

      // Create and initialize the generator
      criteriaGenerator = new CriteriaGenerator();
      await criteriaGenerator.initialize();

      // Set default model from settings
      if ($settings.models && $settings.models.length > 0) {
        selectedModelId = $settings.models[0].id;
        console.log('Selected model from settings:', selectedModelId);
      } else {
        console.log('No models found in settings');
      }

      // Load default prompt template
      promptTemplate = criteriaGenerator.getDefaultPromptTemplate();

      // 加载规则列表
      await loadRuleDefinitions();
    } catch (error) {
      console.error('Failed to initialize criteria generator:', error);
      errorMessage = error instanceof Error ? error.message : 'Failed to initialize';
    }
  });

  async function openHistory() {
    historyError = '';
    loadingHistories = true;
    try {
      histories = await autoCriteriaHistoryService.list({ limit: 100 });
      showHistoryDialog = true;
    } catch (e) {
      console.error('加载历史失败', e);
      historyError = e instanceof Error ? e.message : '加载历史失败';
    } finally {
      loadingHistories = false;
    }
  }

  async function saveToHistory(note: string = '') {
    try {
      const payload: AutoCriteriaHistory = {
        input_text: inputText,
        segmented_text: segmentedText,
        model_id: selectedModelId,
        prompt_template: promptTemplate,
        generated_json: outputJson,
        processing_details: JSON.stringify(processingDetails),
        success_count: progress.success,
        failed_count: progress.failed,
        notes: note
      };
      await autoCriteriaHistoryService.save(payload);
      updateSuccess = '已保存到历史';
      saveNote = '';
    } catch (e) {
      console.error('保存历史失败', e);
      updateError = e instanceof Error ? e.message : '保存历史失败';
    }
  }

  async function deleteHistoryItem(id: number) {
    try {
      await autoCriteriaHistoryService.remove(id);
      histories = histories.filter(h => h.history_id !== id);
    } catch (e) {
      console.error('删除历史失败', e);
      historyError = e instanceof Error ? e.message : '删除历史失败';
    }
  }

  async function batchDeleteHistory() {
    const ids = histories.filter(h => h.history_id && selectedHistoryIds[h.history_id]).map(h => h.history_id!)
    if (ids.length === 0) return;
    for (const id of ids) {
      try { await autoCriteriaHistoryService.remove(id); } catch {}
    }
    histories = histories.filter(h => !(h.history_id && selectedHistoryIds[h.history_id]));
    selectedHistoryIds = {};
  }

  async function searchHistories() {
    loadingHistories = true;
    try {
      histories = await autoCriteriaHistoryService.list({ limit: 200, keyword: historySearch.trim() || undefined });
    } catch (e) {
      console.error('搜索历史失败', e);
      historyError = e instanceof Error ? e.message : '搜索历史失败';
    } finally {
      loadingHistories = false;
    }
  }

  function restoreFromHistory(h: AutoCriteriaHistory) {
    inputText = h.input_text || '';
    segmentedText = h.segmented_text || '';
    outputJson = h.generated_json || '';
    promptTemplate = h.prompt_template || promptTemplate;
    selectedModelId = h.model_id || selectedModelId;
    showHistoryDialog = false;
    updateSuccess = '已从历史恢复到编辑器';
  }

  // 加载规则定义列表
  async function loadRuleDefinitions() {
    try {
      ruleDefinitions = await ruleDesignerService.getRuleDefinitions({});
      console.log(`已加载 ${ruleDefinitions.length} 条规则定义`);
    } catch (error) {
      console.error('加载规则定义失败:', error);
      errorMessage = error instanceof Error ? error.message : '加载规则定义失败';
    }
  }

  // 显示规则列表对话框
  function showRulesList() {
    if (!criteriaGenerator) {
      errorMessage = '生成器未初始化，无法显示规则列表';
      return;
    }

    try {
      // 使用 CriteriaGenerator 的 prepareRulesList 方法获取格式化的规则列表
      rulesListText = criteriaGenerator['prepareRulesList']();
      showRulesListDialog = true;
    } catch (error) {
      console.error('获取规则列表失败:', error);
      errorMessage = error instanceof Error ? error.message : '获取规则列表失败';
    }
  }

  // 获取选中的规则
  function getSelectedRule(ruleId: number | string): RuleDefinition | undefined {
    if (!ruleId) return undefined;
    return ruleDefinitions.find(rule => rule.rule_definition_id === Number(ruleId));
  }

  // Process the input text
  async function processText() {
    const textToProcess = segmentedText.trim() ? segmentedText : inputText;
    if (!textToProcess.trim()) {
      errorMessage = '请输入待处理的入排标准文本，或先进行智能分段';
      return;
    }

    if (!criteriaGenerator) {
      errorMessage = '生成器未初始化，请刷新页面重试';
      return;
    }

    try {
      isProcessing = true;
      errorMessage = '';
      progress = { current: 0, total: 0, success: 0, failed: 0 };
      processingDetails = { matchedSegments: [], failedSegments: [] };

      // 重置追加结果和可编辑段落
      appendResults = {};
      editableSegments = {};

      // Update progress callback
      const updateProgress = (current: number, total: number, success: number, failed: number) => {
        progress = { current, total, success, failed };
      };

      // Process the text
      const result = await criteriaGenerator.processText(
        textToProcess,
        selectedModelId,
        promptTemplate,
        updateProgress
      );

      // Format the JSON output
      outputJson = JSON.stringify(result.generatedJson, null, 2);

      // Store processing details
      processingDetails = result.processingDetails;

      // 初始化可编辑段落文本
      processingDetails.failedSegments.forEach((failure, index) => {
        editableSegments[index] = failure.segment;
      });

      // Switch to details tab if there are matches or failures
      if (processingDetails.matchedSegments.length > 0 || processingDetails.failedSegments.length > 0) {
        activeTab = 'details';
      }
    } catch (error) {
      console.error('Error processing text:', error);
      errorMessage = error instanceof Error ? error.message : '处理文本时出错';
    } finally {
      isProcessing = false;
    }
  }

  // Save the prompt template
  function savePromptTemplate() {
    if (!criteriaGenerator) {
      errorMessage = '生成器未初始化，请刷新页面重试';
      return;
    }
    criteriaGenerator.setPromptTemplate(promptTemplate);
    showPromptDialog = false;
  }

  // Import clipboard service
  import { setClipboardText } from '$lib/services/clipboardService';

  // Copy JSON to clipboard
  async function copyToClipboard() {
    try {
      await setClipboardText(outputJson);
      // Show temporary success message
      const tempMsg = errorMessage;
      errorMessage = '已复制到剪贴板';
      setTimeout(() => {
        errorMessage = tempMsg;
      }, 2000);
    } catch (error) {
      errorMessage = '复制到剪贴板失败';
      console.error('复制到剪贴板失败:', error);
    }
  }

  // 打开编辑规则对话框
  function openEditRuleDialog(rule: RuleDefinition, segment: string) {
    selectedRule = { ...rule };
    selectedSegment = segment;
    showEditRuleDialog = true;
  }

  // 打开创建规则对话框
  function openCreateRuleDialog(segment: string) {
    selectedRule = null;
    selectedSegment = segment;
    suggestedRule = null;
    showCreateRuleDialog = true;
  }

  // 基于未匹配段落生成“规则设置建议”，并预填创建对话框
  async function suggestRuleForSegment(segment: string) {
    try {
      if (!criteriaGenerator) return;
      const suggestion = await criteriaGenerator.suggestRuleDefinition(segment);
      // 预填 RuleDefinitionForm
      suggestedRule = {
        rule_name: suggestion.rule_name,
        rule_description: suggestion.rule_description || segment,
        category: suggestion.category || '入组标准',
        parameter_schema: suggestion.parameter_schema
      } as any;
      selectedRule = suggestedRule;
      selectedSegment = segment;
      showCreateRuleDialog = true;
    } catch (e) {
      console.error('生成规则建议失败:', e);
      errorMessage = e instanceof Error ? e.message : '生成规则建议失败';
    }
  }

  // 多候选建议 + 相似度提示
  function jaccardSimilarity(a: string, b: string): number {
    const norm = (s: string) => (s || '').toLowerCase().replace(/\s+/g, '');
    const ta = new Set(norm(a).split(/[^a-z0-9\u4e00-\u9fa5]+/).filter(Boolean));
    const tb = new Set(norm(b).split(/[^a-z0-9\u4e00-\u9fa5]+/).filter(Boolean));
    const inter = new Set([...ta].filter(x => tb.has(x)));
    const union = new Set([...ta, ...tb]);
    return union.size ? inter.size / union.size : 0;
  }

  function bestSimilar(rule: RuleDefinition): { rule: RuleDefinition; score: number } | null {
    if (!ruleDefinitions || ruleDefinitions.length === 0) return null;
    const textCand = `${rule.rule_name} ${rule.rule_description || ''}`;
    let best: { rule: RuleDefinition; score: number } | null = null;
    for (const r of ruleDefinitions) {
      const textRef = `${r.rule_name} ${r.rule_description || ''} ${(r as any).label || ''}`;
      const s = jaccardSimilarity(textCand, textRef);
      if (!best || s > best.score) best = { rule: r, score: s };
    }
    return best;
  }

  async function suggestRulesForSegment(segment: string) {
    suggestionError = '';
    suggestedCandidates = [];
    try {
      if (!criteriaGenerator) return;
      const arr = await criteriaGenerator.suggestMultipleRuleDefinitions(segment, 3);
      suggestedCandidates = arr.map(s => ({
        rule_name: s.rule_name,
        rule_description: s.rule_description || segment,
        category: s.category || '入组标准',
        parameter_schema: s.parameter_schema
      } as any));
      selectedSegment = segment;
      showSuggestDialog = true;
    } catch (e) {
      console.error('生成多候选规则建议失败:', e);
      suggestionError = e instanceof Error ? e.message : '生成多候选规则建议失败';
    }
  }

  // 更新参数值
  function updateParameterValue(name: string, value: any) {
    console.log(`Updating parameter ${name} to:`, value);
    editedParamValues = { ...editedParamValues, [name]: value };
  }

  // 根据规则名称生成默认参数
  function generateDefaultParametersFromRuleName(ruleName: string): { parameters: Array<{name: string, label: string, type: string, required: boolean, options?: string[]}> } {
    console.log('根据规则名称生成默认参数:', ruleName);

    // 默认参数模式
    const defaultSchema: { parameters: Array<{name: string, label: string, type: string, required: boolean, options?: string[]}> } = {
      parameters: []
    };

    // 根据规则名称中的关键词生成参数
    const lowerRuleName = ruleName.toLowerCase();

    // 年龄相关参数
    if (lowerRuleName.includes('年龄') || lowerRuleName.includes('age')) {
      if (lowerRuleName.includes('≥') || lowerRuleName.includes('大于等于') || lowerRuleName.includes('不小于')) {
        defaultSchema.parameters.push({
          name: 'minAge',
          label: '最小年龄',
          type: 'integer',
          required: true
        });
      }

      if (lowerRuleName.includes('≤') || lowerRuleName.includes('小于等于') || lowerRuleName.includes('不大于')) {
        defaultSchema.parameters.push({
          name: 'maxAge',
          label: '最大年龄',
          type: 'integer',
          required: true
        });
      }

      if (lowerRuleName.includes('介于') || lowerRuleName.includes('between')) {
        if (!defaultSchema.parameters.some(p => p.name === 'minAge')) {
          defaultSchema.parameters.push({
            name: 'minAge',
            label: '最小年龄',
            type: 'integer',
            required: true
          });
        }

        if (!defaultSchema.parameters.some(p => p.name === 'maxAge')) {
          defaultSchema.parameters.push({
            name: 'maxAge',
            label: '最大年龄',
            type: 'integer',
            required: true
          });
        }
      }
    }

    // 性别相关参数
    if (lowerRuleName.includes('性别') || lowerRuleName.includes('gender') || lowerRuleName.includes('sex')) {
      defaultSchema.parameters.push({
        name: 'gender',
        label: '性别',
        type: 'enum',
        options: ['男', '女', '不限'],
        required: true
      });
    }

    // 时间/周期相关参数
    if (lowerRuleName.includes('月') || lowerRuleName.includes('周') || lowerRuleName.includes('天') ||
        lowerRuleName.includes('month') || lowerRuleName.includes('week') || lowerRuleName.includes('day')) {

      if (lowerRuleName.includes('≥') || lowerRuleName.includes('大于等于') || lowerRuleName.includes('不小于')) {
        defaultSchema.parameters.push({
          name: 'minPeriod',
          label: '最小周期',
          type: 'integer',
          required: true
        });
      }

      if (lowerRuleName.includes('≤') || lowerRuleName.includes('小于等于') || lowerRuleName.includes('不大于')) {
        defaultSchema.parameters.push({
          name: 'maxPeriod',
          label: '最大周期',
          type: 'integer',
          required: true
        });
      }
    }

    // 数值相关参数
    if (lowerRuleName.includes('数值') || lowerRuleName.includes('值') || lowerRuleName.includes('指标') ||
        lowerRuleName.includes('value') || lowerRuleName.includes('level')) {

      if (lowerRuleName.includes('≥') || lowerRuleName.includes('大于等于') || lowerRuleName.includes('不小于')) {
        defaultSchema.parameters.push({
          name: 'minValue',
          label: '最小值',
          type: 'number',
          required: true
        });
      }

      if (lowerRuleName.includes('≤') || lowerRuleName.includes('小于等于') || lowerRuleName.includes('不大于')) {
        defaultSchema.parameters.push({
          name: 'maxValue',
          label: '最大值',
          type: 'number',
          required: true
        });
      }
    }

    // 如果没有匹配到任何参数，添加一个通用参数
    if (defaultSchema.parameters.length === 0) {
      defaultSchema.parameters.push({
        name: 'value',
        label: '参数值',
        type: 'string',
        required: true
      });
    }

    console.log('生成的默认参数模式:', defaultSchema);
    return defaultSchema;
  }

  // 打开参数编辑对话框 - 完全重写
  function openEditParamsDialog(match: {segment: string; rule: RuleDefinition; criterion: any}) {
    try {
      console.log('Opening parameter edit dialog for:', match.rule.rule_name);

      // 清除之前的错误和成功消息
      updateError = '';
      updateSuccess = '';

      // 设置当前匹配项
      selectedMatchToEditParams = {
        segment: match.segment,
        rule: { ...match.rule },
        criterion: { ...match.criterion }
      };

      // 初始化编辑参数值为空对象
      editedParamValues = {};

      // 处理参数值
      if (!match.criterion.parameter_values) {
        console.warn('参数值不存在，使用空对象');
      } else {
        console.log('参数值类型:', typeof match.criterion.parameter_values);
        console.log('参数值内容:', match.criterion.parameter_values);

        // 如果是对象类型，直接使用
        if (typeof match.criterion.parameter_values === 'object' && match.criterion.parameter_values !== null) {
          editedParamValues = { ...match.criterion.parameter_values };
          console.log('直接使用对象参数值:', editedParamValues);
        }
        // 如果是字符串类型，尝试解析
        else if (typeof match.criterion.parameter_values === 'string') {
          const paramStr = match.criterion.parameter_values.trim();

          // 尝试直接解析JSON
          try {
            editedParamValues = JSON.parse(paramStr);
            console.log('成功解析参数值字符串:', editedParamValues);
          } catch (parseError) {
            console.error('解析参数值字符串失败:', parseError);

            // 特殊处理 - 尝试提取 Max_age 和 Min_age
            if (paramStr.includes('Max_age') || paramStr.includes('Min_age')) {
              const simpleObj: Record<string, any> = {};

              // 尝试匹配 "Max_age": 78 或 Max_age": 78 格式
              const maxAgeMatch = paramStr.match(/(?:")?Max_age(?:")?:\s*(\d+)/);
              const minAgeMatch = paramStr.match(/(?:")?Min_age(?:")?:\s*(\d+)/);

              if (maxAgeMatch && maxAgeMatch[1]) {
                simpleObj['Max_age'] = parseInt(maxAgeMatch[1], 10);
              }

              if (minAgeMatch && minAgeMatch[1]) {
                simpleObj['Min_age'] = parseInt(minAgeMatch[1], 10);
              }

              if (Object.keys(simpleObj).length > 0) {
                editedParamValues = simpleObj;
                console.log('成功提取年龄参数:', editedParamValues);
                updateError = '参数值格式有问题，但已自动修复';
              } else {
                // 尝试提取数字
                const numbers = paramStr.match(/\d+/g);
                if (numbers && numbers.length >= 2) {
                  const num1 = parseInt(numbers[0], 10);
                  const num2 = parseInt(numbers[1], 10);

                  if (!isNaN(num1) && !isNaN(num2)) {
                    if (num1 >= num2) {
                      simpleObj['Max_age'] = num1;
                      simpleObj['Min_age'] = num2;
                    } else {
                      simpleObj['Min_age'] = num1;
                      simpleObj['Max_age'] = num2;
                    }

                    editedParamValues = simpleObj;
                    console.log('从数字提取年龄参数:', editedParamValues);
                    updateError = '参数值格式有问题，但已自动修复';
                  }
                }
              }
            }

            // 如果上述方法都失败，尝试清理字符串并解析
            if (Object.keys(editedParamValues).length === 0) {
              try {
                const cleanedStr = paramStr
                  .replace(/'/g, '"')  // 替换单引号为双引号
                  .replace(/(\w+):/g, '"$1":')  // 给键名添加双引号
                  .replace(/,\s*}/g, '}');  // 移除尾部逗号

                editedParamValues = JSON.parse(cleanedStr);
                console.log('成功通过清理后解析参数值:', editedParamValues);
                updateError = '参数值格式有问题，但已自动修复';
              } catch (cleanError) {
                console.error('清理后仍然无法解析:', cleanError);
                // 保持空对象
              }
            }
          }
        } else {
          console.warn('参数值类型不支持:', typeof match.criterion.parameter_values);
        }
      }

      // 如果匹配规则定义的参数模式存在且非空，确保所有需要的参数都有初始值
      if (match.rule.parameter_schema && match.rule.parameter_schema.trim() !== '') {
        try {
          // 解析参数模式
          const schema = ruleDesignerService.parseParameterSchema(match.rule.parameter_schema);

          // 验证schema结构和parameters数组
          if (schema && schema.parameters && Array.isArray(schema.parameters)) {
            // 为每个定义的参数设置默认值（如果尚未设置）
            for (const param of schema.parameters) {
              // 确保param是有效的参数定义
              if (param && param.name) {
                if (!(param.name in editedParamValues)) {
                  // 根据参数类型设置默认值
                  if (param.type === 'boolean') {
                    editedParamValues[param.name] = param.default !== undefined ? param.default : false;
                  } else if (param.type === 'number' || param.type === 'integer') {
                    editedParamValues[param.name] = param.default !== undefined ? param.default : null;
                  } else if (param.type === 'enum' && param.options && param.options.length > 0) {
                    editedParamValues[param.name] = param.default !== undefined ? param.default : param.options[0];
                  } else {
                    editedParamValues[param.name] = param.default !== undefined ? param.default : '';
                  }
                }
              } else {
                console.warn('参数定义缺少name属性:', param);
              }
            }
          } else {
            console.warn('参数模式结构无效或parameters数组不存在:', schema);
          }
        } catch (e) {
          console.error('解析参数模式失败:', e);
          updateError = '解析参数模式失败: ' + (e instanceof Error ? e.message : String(e));
        }
      } else {
        console.log('参数模式为空或不存在，将根据规则名称生成默认参数');

        // 根据规则名称生成默认参数
        try {
          const generatedSchema = generateDefaultParametersFromRuleName(match.rule.rule_name);

          // 为生成的参数设置默认值
          for (const param of generatedSchema.parameters) {
            if (!(param.name in editedParamValues)) {
              // 根据参数类型设置默认值
              if (param.type === 'boolean') {
                editedParamValues[param.name] = false;
              } else if (param.type === 'integer' || param.type === 'number') {
                editedParamValues[param.name] = null;
              } else if (param.type === 'enum' && param.options && param.options.length > 0) {
                editedParamValues[param.name] = param.options[0];
              } else {
                editedParamValues[param.name] = '';
              }
            }
          }

          console.log('已根据规则名称生成默认参数:', editedParamValues);
        } catch (genError) {
          console.error('根据规则名称生成默认参数失败:', genError);
        }
      }

      // 打开对话框
      showEditParamsDialog = true;
      console.log('Dialog opened with parameters:', editedParamValues);
    } catch (error) {
      console.error('Error opening parameter edit dialog:', error);
      updateError = error instanceof Error ? error.message : '打开参数编辑对话框失败';
    }
  }

  // 更新规则描述
  async function updateRuleDescription() {
    if (!selectedRule || !selectedRule.rule_definition_id) {
      updateError = '无法更新规则：规则ID不存在';
      return;
    }

    try {
      isUpdatingRule = true;
      updateError = '';
      updateSuccess = '';

      // 获取最新的规则定义
      const currentRule = await ruleDesignerService.getRuleDefinitionById(selectedRule.rule_definition_id);

      if (!currentRule) {
        updateError = '无法获取规则：规则不存在';
        return;
      }

      // 准备新的描述（追加段落）
      let newDescription = currentRule.rule_description || '';

      // 如果描述不为空，添加换行符
      if (newDescription && newDescription.trim() !== '') {
        newDescription += '\n\n';
      }

      // 追加段落
      newDescription += selectedSegment;

      // 更新规则
      const updateRequest = {
        rule_description: newDescription
      };

      await ruleDesignerService.updateRuleDefinition(selectedRule.rule_definition_id, updateRequest);

      updateSuccess = '规则描述已更新';

      // 重新加载生成器的规则定义
      if (criteriaGenerator) {
        await criteriaGenerator.initialize();
      }

    } catch (error) {
      console.error('更新规则描述失败:', error);
      updateError = error instanceof Error ? error.message : '更新规则描述失败';
    } finally {
      isUpdatingRule = false;
    }
  }

  // 从未匹配段落追加描述到规则
  async function appendToRuleDescription(segment: string, ruleId: number | string) {
    if (!ruleId) {
      errorMessage = '请先选择一个规则';
      return;
    }

    try {
      // 显示加载状态
      updateError = '';
      updateSuccess = '';
      isUpdatingRule = true;

      // 获取规则定义
      const rule = await ruleDesignerService.getRuleDefinitionById(Number(ruleId));

      if (!rule) {
        updateError = '无法获取规则：规则不存在';
        return;
      }

      // 准备新的描述（追加段落）
      let newDescription = rule.rule_description || '';

      // 如果描述不为空，添加换行符
      if (newDescription && newDescription.trim() !== '') {
        newDescription += '\n\n';
      }

      // 追加段落
      newDescription += segment;

      // 更新规则
      const updateRequest = {
        rule_description: newDescription
      };

      await ruleDesignerService.updateRuleDefinition(Number(ruleId), updateRequest);

      // 获取当前段落索引
      const index = processingDetails.failedSegments.findIndex(f => f.segment === segment);
      if (index !== -1) {
        // 更新追加结果显示
        appendResults[index] = `已成功追加到规则"${rule.rule_name}"的描述中`;
      }

      // 显示全局成功消息
      updateSuccess = `已将段落追加到规则"${rule.rule_name}"的描述中`;

      // 重新加载规则定义
      await loadRuleDefinitions();

      // 重新加载生成器的规则定义
      if (criteriaGenerator) {
        await criteriaGenerator.initialize();
      }

    } catch (error) {
      console.error('追加描述失败:', error);
      updateError = error instanceof Error ? error.message : '追加描述失败';
    } finally {
      isUpdatingRule = false;
    }
  }

  // 处理规则保存事件
  async function handleRuleSave(event: CustomEvent) {
    const { action } = event.detail;

    if (action === 'create' || action === 'update') {
      // 关闭对话框
      showCreateRuleDialog = false;
      showEditRuleDialog = false;

      // 显示成功消息
      updateSuccess = action === 'create' ? '规则创建成功' : '规则更新成功';

      // 重新加载生成器的规则定义
      if (criteriaGenerator) {
        await criteriaGenerator.initialize();
      }

      // 如果有输入文本，建议重新处理
      if (inputText.trim()) {
        setTimeout(() => {
          updateSuccess = '规则已更新，建议重新处理文本以应用新规则';
        }, 2000);
      }
    }
  }

  // 保存参数值 - 完全重写
  async function saveParameterValues() {
    if (!selectedMatchToEditParams) {
      console.error('No selected match to edit params');
      updateError = '没有选中的匹配项';
      return;
    }

    try {
      isUpdatingParams = true;
      updateError = '';
      updateSuccess = '';

      // 验证参数值
      let parametersToValidate: Array<{name: string, label: string, type: string, required: boolean}> = [];

      // 首先尝试从规则的参数模式获取参数定义
      if (selectedMatchToEditParams.rule.parameter_schema && selectedMatchToEditParams.rule.parameter_schema.trim() !== '') {
        try {
          const schema = ruleDesignerService.parseParameterSchema(selectedMatchToEditParams.rule.parameter_schema);

          // 验证schema结构和parameters数组
          if (schema && schema.parameters && Array.isArray(schema.parameters)) {
            parametersToValidate = schema.parameters;
          } else {
            console.warn('参数模式结构无效或parameters数组不存在:', schema);
          }
        } catch (schemaError) {
          console.error('解析参数模式失败:', schemaError);
          updateError = '解析参数模式失败: ' + (schemaError instanceof Error ? schemaError.message : String(schemaError));
          isUpdatingParams = false;
          return;
        }
      }

      // 如果没有从参数模式获取到参数定义，则尝试根据规则名称生成
      if (parametersToValidate.length === 0) {
        console.log('从参数模式未获取到参数定义，尝试根据规则名称生成');
        const generatedSchema = generateDefaultParametersFromRuleName(selectedMatchToEditParams.rule.rule_name);
        parametersToValidate = generatedSchema.parameters;
      }

      // 验证参数
      for (const param of parametersToValidate) {
        // 确保param是有效的参数定义
        if (param && param.name) {
          if (param.required && (
            editedParamValues[param.name] === undefined ||
            editedParamValues[param.name] === null ||
            editedParamValues[param.name] === ''
          )) {
            updateError = `参数 "${param.label || param.name}" 是必填项`;
            isUpdatingParams = false;
            return;
          }
        }
      }

      console.log('Saving parameters with values:', editedParamValues);

      // 确保editedParamValues是一个有效的对象
      if (typeof editedParamValues !== 'object' || Array.isArray(editedParamValues) || editedParamValues === null) {
        console.error('参数值不是有效的对象:', editedParamValues);
        updateError = '参数值必须是有效的对象';
        isUpdatingParams = false;
        return;
      }

      // 将参数值序列化为JSON字符串
      let parameterValuesJson;
      try {
        // 直接使用JSON.stringify而不是ruleDesignerService.stringifyParameterValues
        // 这样可以避免可能的格式化问题
        parameterValuesJson = JSON.stringify(editedParamValues);
        console.log('序列化后的参数值:', parameterValuesJson);
      } catch (stringifyError) {
        console.error('序列化参数值失败:', stringifyError);
        updateError = '序列化参数值失败: ' + (stringifyError instanceof Error ? stringifyError.message : String(stringifyError));
        isUpdatingParams = false;
        return;
      }

      // 保存当前选中的匹配项信息
      const currentMatch = {
        segment: selectedMatchToEditParams.segment,
        ruleId: selectedMatchToEditParams.rule.rule_definition_id,
        displayOrder: selectedMatchToEditParams.criterion.display_order
      };

      // 找到要更新的匹配项
      const matchIndex = processingDetails.matchedSegments.findIndex(
        m => m.segment === currentMatch.segment &&
             m.rule.rule_definition_id === currentMatch.ruleId
      );

      if (matchIndex === -1) {
        console.error('Match not found in processingDetails.matchedSegments');
        updateError = '找不到要更新的匹配项';
        isUpdatingParams = false;
        return;
      }

      // 1. 更新匹配项的参数值 - 直接使用对象而不是JSON字符串
      processingDetails.matchedSegments[matchIndex].criterion.parameter_values = editedParamValues;
      console.log('Updated matched segment at index', matchIndex);

      // 2. 更新JSON输出
      if (outputJson) {
        try {
          const jsonObj = JSON.parse(outputJson);
          const criterionIndex = jsonObj.criteria.findIndex(
            (c: any) => c.display_order === currentMatch.displayOrder
          );

          if (criterionIndex !== -1) {
            // 在JSON输出中使用对象而不是字符串
            jsonObj.criteria[criterionIndex].parameter_values = editedParamValues;
            outputJson = JSON.stringify(jsonObj, null, 2);
            console.log('Updated JSON output for criterion at index', criterionIndex);
          } else {
            console.warn('Criterion not found in JSON output with display_order:', currentMatch.displayOrder);
          }
        } catch (jsonError) {
          console.error('Error updating JSON output:', jsonError);
          updateError = '更新JSON输出时出错';
          isUpdatingParams = false;
          return;
        }
      }

      // 显示成功消息
      updateSuccess = '参数值已成功保存';

      // 关闭对话框
      showEditParamsDialog = false;

      // 3秒后清除成功消息
      setTimeout(() => {
        updateSuccess = '';
      }, 3000);
    } catch (error) {
      console.error('保存参数值失败:', error);
      updateError = error instanceof Error ? error.message : '保存参数值失败';
    } finally {
      isUpdatingParams = false;
    }
  }

  // 重新处理文本
  function reprocessText() {
    if (inputText.trim()) {
      processText();
    }
  }

  // 处理搜索输入
  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    projectSearchQuery = target.value;

    // 重置选中的结果索引
    selectedResultIndex = -1;

    // 清除之前的定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // 如果输入为空，清空结果并隐藏下拉框
    if (!projectSearchQuery.trim()) {
      projectSearchResults = [];
      showSearchDropdown = false;
      return;
    }

    // 设置定时器进行搜索（防抖）
    searchTimeout = setTimeout(async () => {
      await performFuzzySearch();
    }, 300);
  }

  // 处理键盘导航
  function handleSearchKeydown(event: KeyboardEvent) {
    // 如果没有搜索结果或下拉框未显示，不处理键盘事件
    if (!showSearchDropdown || projectSearchResults.length === 0) {
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
        // 向下移动选择
        event.preventDefault();
        selectedResultIndex = Math.min(selectedResultIndex + 1, projectSearchResults.length - 1);
        break;

      case 'ArrowUp':
        // 向上移动选择
        event.preventDefault();
        selectedResultIndex = Math.max(selectedResultIndex - 1, 0);
        break;

      case 'Enter':
        // 选择当前高亮的项目
        event.preventDefault();
        if (selectedResultIndex >= 0 && selectedResultIndex < projectSearchResults.length) {
          selectProject(projectSearchResults[selectedResultIndex]);
          showSearchDropdown = false;
        }
        break;

      case 'Escape':
        // 关闭下拉框
        event.preventDefault();
        showSearchDropdown = false;
        break;
    }
  }

  // 执行模糊搜索
  async function performFuzzySearch() {
    try {
      isSearchingProjects = true;
      importToProjectError = '';

      // 调用项目管理服务搜索项目
      const result = await projectManagementService.getProjects({
        name: projectSearchQuery,
        page: 1,
        page_size: 10
      });

      projectSearchResults = result.items;

      // 显示下拉框（如果有结果）
      showSearchDropdown = projectSearchResults.length > 0;

      if (projectSearchResults.length === 0) {
        importToProjectError = "未找到匹配的项目";
      }
    } catch (error) {
      console.error('搜索项目失败:', error);
      importToProjectError = error instanceof Error ? error.message : '搜索项目失败';
      showSearchDropdown = false;
    } finally {
      isSearchingProjects = false;
    }
  }

  // 项目搜索功能（点击搜索按钮时）
  async function searchProjects() {
    if (!projectSearchQuery.trim()) {
      importToProjectError = "请输入项目名称进行搜索";
      return;
    }

    try {
      isSearchingProjects = true;
      importToProjectError = '';
      showSearchDropdown = false; // 隐藏下拉框，显示完整结果列表

      // 调用项目管理服务搜索项目
      const result = await projectManagementService.getProjects({
        name: projectSearchQuery,
        page: 1,
        page_size: 10
      });

      projectSearchResults = result.items;

      if (projectSearchResults.length === 0) {
        importToProjectError = "未找到匹配的项目";
      }
    } catch (error) {
      console.error('搜索项目失败:', error);
      importToProjectError = error instanceof Error ? error.message : '搜索项目失败';
    } finally {
      isSearchingProjects = false;
    }
  }

  // 选择项目
  function selectProject(project: ProjectWithDetails) {
    selectedProject = project;
    importToProjectError = '';
    showSearchDropdown = false;
  }

  // 导入到项目
  async function importToProject() {
    if (!selectedProject || !selectedProject.project.project_id) {
      importToProjectError = "请先选择一个项目";
      return;
    }

    if (!outputJson) {
      importToProjectError = "没有可导入的JSON数据";
      return;
    }

    try {
      importingToProject = true;
      importToProjectError = '';
      importToProjectSuccess = '';

      // 解析JSON数据
      const jsonData = JSON.parse(outputJson);

      // 验证JSON结构
      if (!jsonData.criteria || !Array.isArray(jsonData.criteria)) {
        throw new Error("JSON数据格式无效，缺少criteria数组");
      }

      // 处理每个标准
      let successCount = 0;
      let failCount = 0;
      const projectId = selectedProject.project.project_id;

      // 创建所有标准
      for (const criterion of jsonData.criteria) {
        try {
          // 获取规则定义
          const ruleDefinition = await ruleDesignerService.getRuleDefinitionById(criterion.rule_definition_id);

          if (!ruleDefinition) {
            throw new Error(`找不到规则定义 (ID: ${criterion.rule_definition_id})`);
          }

          // 根据规则定义的category字段确定标准类型
          const criterionType = ruleDefinition.category === '排除标准' ? 'exclusion' : 'inclusion';

          // 准备参数值
          const parameterValues = JSON.stringify(criterion.parameter_values);

          // 创建请求对象
          const request = {
            project_id: projectId,
            rule_definition_id: criterion.rule_definition_id,
            criterion_type: criterionType,
            parameter_values: parameterValues,
            is_active: criterion.is_active !== false, // 默认为true
            display_order: criterion.display_order
          };

          // 调用服务创建标准
          await ruleDesignerService.createProjectCriterion(request);
          successCount++;
        } catch (err) {
          console.error('导入标准失败:', err);
          failCount++;
        }
      }

      // 等待所有标准创建完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 处理OR组关系
      if (jsonData.or_groups && Array.isArray(jsonData.or_groups)) {
        for (const group of jsonData.or_groups) {
          try {
            if (!Array.isArray(group.criteria_ids) || group.criteria_ids.length < 2) {
              continue;
            }

            // 生成一个唯一的组ID
            const groupId = crypto.randomUUID();

            // 获取所有标准
            const allCriteria = await ruleDesignerService.getProjectCriteria({ project_id: projectId });

            // 找到需要分组的标准
            const criteriaToGroup = [];
            for (const criteriaIndex of group.criteria_ids) {
              // 找到对应索引的criteria项
              const criteriaItem = jsonData.criteria[criteriaIndex - 1]; // 转换为0-based索引

              if (!criteriaItem) {
                console.warn(`找不到索引为${criteriaIndex}的criteria项，跳过`);
                continue;
              }

              // 获取该criteria项的display_order和rule_definition_id
              const displayOrder = criteriaItem.display_order;
              const ruleDefinitionId = criteriaItem.rule_definition_id;

              // 在已创建的标准中查找匹配的标准
              const matchingCriteria = allCriteria.filter(c =>
                c.criterion.display_order === displayOrder &&
                c.rule_definition.rule_definition_id === ruleDefinitionId
              );

              if (matchingCriteria.length > 0) {
                criteriaToGroup.push(matchingCriteria[0]);
              }
            }

            // 如果找到了至少两个标准，则创建一个组
            if (criteriaToGroup.length >= 2) {
              // 更新所有标准，设置组ID和操作符
              for (const criterion of criteriaToGroup) {
                if (!criterion.criterion.project_criterion_id) continue;

                await ruleDesignerService.updateProjectCriterion(
                  criterion.criterion.project_criterion_id,
                  {
                    criteria_group_id: groupId,
                    group_operator: 'OR'
                  }
                );
              }
            }
          } catch (err) {
            console.error('创建"或"关系组失败:', err);
          }
        }
      }

      // 显示成功消息
      importToProjectSuccess = `成功导入 ${successCount} 个标准${failCount > 0 ? `，失败 ${failCount} 个` : ''}`;

      // 不自动关闭对话框，让用户可以点击查看项目详情

    } catch (error) {
      console.error('导入到项目失败:', error);
      importToProjectError = error instanceof Error ? error.message : '导入到项目失败';
    } finally {
      importingToProject = false;
    }
  }

  // 重新匹配单个段落
  async function rematchSegment(segment: string, index: number): Promise<boolean> {
    if (!criteriaGenerator) {
      errorMessage = '生成器未初始化';
      return false;
    }

    try {
      // 显示处理中状态
      const tempMsg = errorMessage;
      errorMessage = `正在重新匹配段落 #${index + 1}...`;

      // 准备规则列表
      const rulesListText = criteriaGenerator['prepareRulesList']();

      // 调用处理方法
      const processResult = await criteriaGenerator['processSegmentWithAllRules'](segment, rulesListText);

      if (processResult.success && processResult.criterion) {
        // 找到匹配的规则
        const matchedRule = ruleDefinitions.find(
          rule => rule.rule_definition_id === processResult.criterion?.rule_definition_id
        );

        if (matchedRule) {
          // 创建新的标准
          const criterionWithOrder = {
            ...processResult.criterion,
            display_order: processingDetails.matchedSegments.length +
                          (outputJson ? JSON.parse(outputJson).criteria.length : 0) + 1
          };

          // 从失败列表中移除
          processingDetails.failedSegments = processingDetails.failedSegments.filter((_, i) => i !== index);

          // 添加到成功列表
          processingDetails.matchedSegments.push({
            segment,
            rule: matchedRule,
            criterion: criterionWithOrder
          });

          // 更新JSON输出
          if (outputJson) {
            const jsonObj = JSON.parse(outputJson);
            jsonObj.criteria.push(criterionWithOrder);
            outputJson = JSON.stringify(jsonObj, null, 2);
          }

          // 显示成功消息
          errorMessage = `成功匹配段落 #${index + 1} 到规则: ${matchedRule.rule_name}`;
          setTimeout(() => {
            errorMessage = tempMsg;
          }, 3000);

          return true; // 返回成功标志
        } else {
          errorMessage = `段落 #${index + 1} 匹配了不存在的规则ID: ${processResult.criterion.rule_definition_id}`;
          setTimeout(() => {
            errorMessage = tempMsg;
          }, 3000);
        }
      } else {
        // 匹配失败，显示错误
        errorMessage = `段落 #${index + 1} 重新匹配失败: ${processResult.error || '未找到匹配的规则'}`;
        setTimeout(() => {
          errorMessage = tempMsg;
        }, 3000);
      }

      return false; // 返回失败标志
    } catch (error) {
      console.error('重新匹配段落失败:', error);
      errorMessage = error instanceof Error ? error.message : '重新匹配段落失败';
      return false;
    }
  }

  // 重新匹配所有未匹配段落
  async function rematchAllSegments() {
    if (!criteriaGenerator || processingDetails.failedSegments.length === 0) {
      return;
    }

    try {
      // 设置处理状态
      isProcessing = true;
      const originalErrorMessage = errorMessage;
      errorMessage = '正在重新匹配所有未匹配段落...';

      // 保存当前未匹配段落的副本
      const failedSegmentsCopy = [...processingDetails.failedSegments];
      let successCount = 0;

      // 逐个处理未匹配段落
      for (let i = 0; i < failedSegmentsCopy.length; i++) {
        // 计算当前索引（考虑到成功匹配后会从数组中移除）
        const currentIndex = processingDetails.failedSegments.findIndex(
          segment => segment.segment === failedSegmentsCopy[i].segment
        );

        if (currentIndex !== -1) {
          errorMessage = `正在重新匹配段落 ${i + 1}/${failedSegmentsCopy.length}...`;
          const success = await rematchSegment(failedSegmentsCopy[i].segment, currentIndex);
          if (success) {
            successCount++;
          }

          // 短暂暂停，避免API限流
          if (i < failedSegmentsCopy.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      // 显示结果
      if (successCount > 0) {
        errorMessage = `成功重新匹配 ${successCount}/${failedSegmentsCopy.length} 个段落`;
      } else {
        errorMessage = '没有段落成功匹配';
      }

      // 3秒后恢复原始消息
      setTimeout(() => {
        errorMessage = originalErrorMessage;
      }, 3000);
    } catch (error) {
      console.error('重新匹配所有段落失败:', error);
      errorMessage = error instanceof Error ? error.message : '重新匹配所有段落失败';
    } finally {
      isProcessing = false;
    }
  }

  // 打开移除确认对话框
  function removeMatchedSegment(match: {
    segment: string;
    rule: RuleDefinition;
    criterion: any;
  }) {
    selectedMatchToRemove = match;
    showRemoveConfirmDialog = true;
  }

  // 确认移除匹配成功的段落
  function confirmRemoveMatchedSegment() {
    if (!selectedMatchToRemove) return;

    const match = selectedMatchToRemove;

    // 从匹配成功列表中移除
    processingDetails.matchedSegments = processingDetails.matchedSegments.filter(
      m => m.segment !== match.segment || m.rule.rule_definition_id !== match.rule.rule_definition_id
    );

    // 从生成的JSON中移除
    const criterionIndex = outputJson ? JSON.parse(outputJson).criteria.findIndex(
      (c: any) => c.display_order === match.criterion.display_order
    ) : -1;

    if (criterionIndex !== -1) {
      const jsonObj = JSON.parse(outputJson);

      // 获取要删除标准的display_order
      const removedDisplayOrder = match.criterion.display_order;

      // 从criteria数组中移除
      jsonObj.criteria.splice(criterionIndex, 1);

      // 更新后续标准的display_order
      jsonObj.criteria.forEach((criterion: any) => {
        if (criterion.display_order > removedDisplayOrder) {
          criterion.display_order -= 1;
        }
      });

      // 更新OR组中的criteria_ids
      jsonObj.or_groups.forEach((group: any) => {
        // 从组中移除被删除标准
        group.criteria_ids = group.criteria_ids.filter((id: number) => id !== removedDisplayOrder);

        // 更新大于被删除标准的ID
        group.criteria_ids = group.criteria_ids.map((id: number) =>
          id > removedDisplayOrder ? id - 1 : id
        );

        // 如果组中只剩一个或没有标准，移除该组
        if (group.criteria_ids.length <= 1) {
          jsonObj.or_groups = jsonObj.or_groups.filter((g: any) => g !== group);
        }
      });

      // 更新输出JSON
      outputJson = JSON.stringify(jsonObj, null, 2);
    }

    // 显示成功消息
    updateSuccess = '已成功移除匹配段落';

    // 如果所有匹配段落都被移除，切换到JSON标签
    if (processingDetails.matchedSegments.length === 0) {
      activeTab = 'json';
    }

    // 关闭确认对话框
    showRemoveConfirmDialog = false;
    selectedMatchToRemove = null;
  }
</script>

<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-4">自动生成入排标准</h1>

  <!-- Error message display -->
  {#if errorMessage}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
      <span class="block sm:inline">{errorMessage}</span>
    </div>
  {/if}

  <!-- Controls -->
  <div class="flex justify-between mb-4">
    <div class="flex gap-2">
      <select
        bind:value={selectedModelId}
        class="border rounded px-3 py-2 text-sm"
        disabled={isProcessing}
      >
        {#if $settings.models}
          {#each $settings.models as model}
            <option value={model.id}>{model.name}</option>
          {/each}
        {:else}
          <option value="">加载模型中...</option>
        {/if}
      </select>

      <Button onclick={()=>showPromptDialog = true} variant="outline" disabled={isProcessing}>
        配置Prompt
      </Button>

      <Button onclick={showRulesList} variant="outline" disabled={isProcessing || !criteriaGenerator} class="flex items-center gap-1">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
        <span>查看规则列表</span>
      </Button>
    </div>

    <div class="flex gap-2">
      <Button onclick={intelligentSegment} variant="outline" disabled={isProcessing || !inputText.trim()}>
        智能化分段
      </Button>
      <input class="border rounded px-2 py-1 text-sm" placeholder="备注（可选）" bind:value={saveNote} />
      <Button onclick={()=>saveToHistory(saveNote)} variant="outline" disabled={isProcessing || (!inputText.trim() && !segmentedText.trim() && !outputJson)}>
        保存到历史
      </Button>
      <Button onclick={openHistory} variant="outline" disabled={isProcessing}>
        历史
      </Button>
      <Button onclick={processText} disabled={isProcessing || (!inputText.trim() && !segmentedText.trim())}>
        {#if isProcessing}
          <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
          处理中...
        {:else}
          开始处理
        {/if}
      </Button>

      <Button onclick={copyToClipboard} variant="outline" disabled={!outputJson}>
        复制JSON
      </Button>

      <Button onclick={()=>showProjectImportDialog = true} variant="outline" disabled={!outputJson} class="flex items-center gap-1">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
        <span>导入到项目</span>
      </Button>
    </div>
  </div>

  <!-- Progress bar (visible when processing) -->
  {#if isProcessing}
    <div class="mb-4">
      <div class="w-full bg-gray-200 rounded-full h-2.5 mb-1">
        <div class="bg-blue-600 h-2.5 rounded-full" style="width: {progress.total > 0 ? (progress.current / progress.total * 100) : 0}%"></div>
      </div>
      <div class="text-sm text-gray-600">
        处理进度: {progress.current}/{progress.total}
        (成功: {progress.success}, 失败: {progress.failed})
      </div>
    </div>
  {/if}

  <!-- Main content area with split layout -->
  <div class="flex flex-col md:flex-row gap-4 h-[calc(100vh-220px)]">
    <!-- Left side: Input text area -->
    <div class="w-full md:w-1/2 flex flex-col">
      <h2 class="text-lg font-semibold mb-2">待处理文本</h2>
      <textarea
        bind:value={inputText}
        class="flex-1 p-3 border rounded resize-none font-mono text-sm"
        placeholder="请在此粘贴入排标准文本..."
        disabled={isProcessing}
      ></textarea>

      <!-- 智能化分段结果 -->
      <div class="mt-4">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-semibold text-gray-700">智能化分段（可编辑）</h3>
          {#if segmentedText.trim()}
            <span class="text-xs text-gray-500">共 {segmentedText.split(/\n{2,}/).filter(Boolean).length} 段</span>
          {/if}
        </div>
        <textarea
          bind:value={segmentedText}
          class="h-40 w-full p-3 border rounded resize-none font-mono text-sm bg-gray-50"
          placeholder="点击上方“智能化分段”后，结果将显示在这里；开始处理将优先使用这里的内容。"
        ></textarea>
      </div>
    </div>

    <!-- Right side: Results area with tabs -->
    <div class="w-full md:w-1/2 flex flex-col">
      <!-- Tabs for JSON and Details -->
      <div class="border-b border-gray-200 mb-2">
        <div class="flex">
          <button
            class="py-2 px-4 font-medium text-sm {activeTab === 'json' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}"
            onclick={() => activeTab = 'json'}
          >
            生成的JSON
          </button>
          <button
            class="py-2 px-4 font-medium text-sm {activeTab === 'details' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}"
            onclick={() => activeTab = 'details'}
          >
            处理详情
          </button>
        </div>
      </div>

      <!-- Success message -->
      {#if updateSuccess}
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <span class="block sm:inline">{updateSuccess}</span>
          {#if updateSuccess.includes('建议重新处理')}
            <button
              class="ml-2 px-2 py-1 bg-green-200 hover:bg-green-300 rounded text-xs"
              onclick={reprocessText}
            >
              重新处理
            </button>
          {/if}
        </div>
      {/if}

      <!-- Tab content -->
      {#if activeTab === 'json'}
        <pre class="flex-1 p-3 border rounded overflow-auto bg-gray-50 font-mono text-sm">{outputJson || '处理结果将显示在这里...'}</pre>
      {:else}
        <div class="flex-1 overflow-y-auto border rounded p-3 bg-gray-50">
          {#if processingDetails.matchedSegments.length === 0 && processingDetails.failedSegments.length === 0}
            <div class="text-center text-gray-500 py-10">
              处理结果详情将显示在这里...
            </div>
          {:else}
            <!-- Matched segments -->
            {#if processingDetails.matchedSegments.length > 0}
              <div class="mb-6">
                <h3 class="text-md font-semibold mb-2 flex items-center text-green-700">
                  <Check class="h-4 w-4 mr-1" /> 匹配成功的段落 ({processingDetails.matchedSegments.length})
                </h3>
                <div class="space-y-4">
                  {#each processingDetails.matchedSegments as match}
                    <div class="border border-green-200 rounded-md bg-green-50 overflow-hidden">
                      <div class="bg-green-100 px-3 py-2 flex justify-between items-center">
                        <div class="font-medium text-sm">
                          <div class="flex items-center">
                            匹配规则: <span class="text-green-800 ml-1">{match.rule.rule_name}</span>
                            {#if match.rule.category}
                              <span class="ml-2 px-2 py-0.5 text-xs rounded-full {match.rule.category === '入组标准' ? 'bg-blue-100 text-blue-800' : match.rule.category === '排除标准' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}">
                                {match.rule.category}
                              </span>
                            {/if}
                          </div>
                        </div>
                        <div class="flex space-x-2">
                          <button
                            class="text-xs px-2 py-1 bg-white border border-green-300 rounded hover:bg-green-50 flex items-center"
                            onclick={() => openEditRuleDialog(match.rule, match.segment)}
                          >
                            <Edit class="h-3 w-3 mr-1" /> 编辑规则
                          </button>
                          <button
                            class="text-xs px-2 py-1 bg-white border border-green-300 rounded hover:bg-green-50 flex items-center"
                            onclick={() => updateRuleDescription()}
                          >
                            <Plus class="h-3 w-3 mr-1" /> 追加描述
                          </button>
                          <button
                            class="text-xs px-2 py-1 bg-blue-500 text-white border border-blue-600 rounded hover:bg-blue-600 flex items-center"
                            onclick={() => openEditParamsDialog(match)}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path></svg>
                            编辑参数值
                          </button>
                          <button
                            class="text-xs px-2 py-1 bg-white border border-red-300 rounded hover:bg-red-50 flex items-center"
                            onclick={() => removeMatchedSegment(match)}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                            移除
                          </button>
                        </div>
                      </div>
                      <div class="p-3 text-sm">
                        <div class="mb-2">
                          <div class="font-medium mb-1">段落文本:</div>
                          <div class="bg-white p-2 rounded border border-gray-200">{match.segment}</div>
                        </div>
                        <div class="mb-2">
                          <div class="font-medium mb-1">规则描述:</div>
                          <div class="bg-white p-2 rounded border border-gray-200 whitespace-pre-line">{match.rule.rule_description || '(无描述)'}</div>
                        </div>
                        {#if match.remark}
                          <div class="mb-2">
                            <div class="font-medium mb-1">备注:</div>
                            <div class="bg-yellow-50 p-2 rounded border border-yellow-200 text-yellow-800 whitespace-pre-line">{match.remark}</div>
                          </div>
                        {/if}
                        <!-- 参数情况 -->
                        <div>
                          <div class="font-medium mb-1 flex items-center">
                            <span>提取的参数:</span>
                            <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800">
                              display_order: {match.criterion.display_order}
                            </span>
                          </div>
                          <div class="bg-white p-2 rounded border border-gray-200">
                            {#if match.criterion && match.criterion.parameter_values}
                              <div class="grid grid-cols-2 gap-2">
                                {#each Object.entries(match.criterion.parameter_values) as [key, value]}
                                  <div class="flex items-center">
                                    <span class="font-medium text-gray-700">{key}:</span>
                                    <span class="ml-1 px-2 py-0.5 bg-yellow-50 border border-yellow-200 rounded text-yellow-800">
                                      {#if typeof value === 'boolean'}
                                        {value ? '是' : '否'}
                                      {:else if value === null || value === undefined}
                                        <em class="text-gray-500">空</em>
                                      {:else}
                                        {value}
                                      {/if}
                                    </span>
                                  </div>
                                {/each}
                              </div>
                            {:else}
                              <div class="text-gray-500 italic">无参数</div>
                            {/if}
                          </div>
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}

            <!-- Failed segments -->
            {#if processingDetails.failedSegments.length > 0}
              <div>
                <div class="flex justify-between items-center mb-2">
                  <h3 class="text-md font-semibold flex items-center text-red-700">
                    <AlertCircle class="h-4 w-4 mr-1" /> 未匹配的段落 ({processingDetails.failedSegments.length})
                  </h3>
                  {#if processingDetails.failedSegments.length > 0}
                    <button
                      class="text-xs px-3 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center"
                      onclick={() => rematchAllSegments()}
                      disabled={isProcessing}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path></svg>
                      全部重新匹配
                    </button>
                  {/if}
                </div>
                <div class="space-y-4">
                  {#each processingDetails.failedSegments as failure, index}
                    <div class="border border-red-200 rounded-md bg-red-50 overflow-hidden">
                      <div class="bg-red-100 px-3 py-2 flex justify-between items-center">
                        <div class="font-medium text-sm">
                          段落 #{index + 1}
                        </div>
                        <div class="flex space-x-2">
                          <button
                            class="text-xs px-2 py-1 bg-white border border-blue-300 rounded hover:bg-blue-50 flex items-center"
                            onclick={() => rematchSegment(failure.segment, index)}
                            disabled={isProcessing}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path></svg>
                            重新匹配
                          </button>
                          <button
                            class="text-xs px-2 py-1 bg-white border border-red-300 rounded hover:bg-red-50 flex items-center"
                            onclick={() => openCreateRuleDialog(failure.segment)}
                          >
                            <Plus class="h-3 w-3 mr-1" /> 创建新规则
                          </button>
                          <button
                            class="text-xs px-2 py-1 bg-white border border-blue-300 rounded hover:bg-blue-50 flex items-center"
                            onclick={() => suggestRuleForSegment(failure.segment)}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M12 2v4"></path><path d="M12 18v4"></path><path d="M4.93 4.93l2.83 2.83"></path><path d="M16.24 16.24l2.83 2.83"></path><path d="M2 12h4"></path><path d="M18 12h4"></path><path d="M4.93 19.07l2.83-2.83"></path><path d="M16.24 7.76l2.83-2.83"></path></svg>
                            生成规则建议
                          </button>
                          <button
                            class="text-xs px-2 py-1 bg-white border border-purple-300 rounded hover:bg-purple-50 flex items-center"
                            onclick={() => suggestRulesForSegment(failure.segment)}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 1 1-2.83 2.83l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 1 1-4 0v-.09A1.65 1.65 0 0 0 8 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 1 1-2.83-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 1 1 0-4h.09A1.65 1.65 0 0 0 4.6 8a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 1 1 2.83-2.83l.06.06A1.65 1.65 0 0 0 8 4.6a1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 1 1 2.83 2.83l-.06.06A1.65 1.65 0 0 0 16 8.6a1.65 1.65 0 0 0 1.51 1H18a2 2 0 1 1 0 4h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
                            多候选建议
                          </button>
                        </div>
                      </div>
                      <div class="p-3 text-sm">
                        <div class="mb-2">
                          <div class="font-medium mb-1">段落文本:</div>
                          <div class="bg-white p-2 rounded border border-gray-200">{failure.segment}</div>
                        </div>
                        {#if failure.errors.length > 0}
                          <div class="mb-4">
                            <div class="font-medium mb-1">失败原因:</div>
                            <div class="space-y-2">
                              {#each failure.errors as error}
                                <div class="bg-white p-2 rounded border border-gray-200">
                                  <div class="text-xs text-gray-500 mb-1">规则: {error.rule.rule_name}</div>
                                  <div class="text-red-600">{error.error}</div>
                                </div>
                              {/each}
                            </div>
                          </div>
                        {/if}

                        <!-- 追加描述功能 -->
                        <div class="mt-3 pt-3 border-t border-red-200">
                          <div class="font-medium mb-2">追加描述到现有规则:</div>

                          <!-- 规则选择 -->
                          <div class="flex items-center space-x-2 mb-3">
                            <select
                              id="rule-select-{index}"
                              class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                              bind:value={selectedRuleForFailure[index]}
                            >
                              <option value="">-- 选择规则 --</option>
                              {#each ruleDefinitions as rule}
                                <option value={rule.rule_definition_id}>{rule.rule_name}</option>
                              {/each}
                            </select>
                          </div>

                          <!-- 显示选中的规则描述 -->
                          {#if selectedRuleForFailure[index]}
                            {#if getSelectedRule(selectedRuleForFailure[index])}
                              <div class="mb-3">
                                <div class="text-xs font-medium mb-1">当前规则描述:</div>
                                <div class="bg-white p-2 rounded border border-gray-200 text-xs max-h-24 overflow-y-auto whitespace-pre-line">
                                  {getSelectedRule(selectedRuleForFailure[index])?.rule_description || '(无描述)'}
                                </div>
                              </div>
                            {/if}
                          {/if}

                          <!-- 可编辑的段落文本 -->
                          <div class="mb-3">
                            <div class="text-xs font-medium mb-1">要追加的文本:</div>
                            <textarea
                              bind:value={editableSegments[index]}
                              class="w-full p-2 text-xs border border-gray-300 rounded min-h-[60px]"
                            ></textarea>
                          </div>

                          <!-- 追加按钮 -->
                          <div class="flex justify-end">
                            <button
                              class="text-xs px-3 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center"
                              onclick={() => appendToRuleDescription(editableSegments[index], selectedRuleForFailure[index])}
                              disabled={!selectedRuleForFailure[index]}
                            >
                              <Plus class="h-3 w-3 mr-1" /> 追加描述
                            </button>
                          </div>

                          <!-- 追加结果显示 -->
                          {#if appendResults[index]}
                            <div class="mt-2 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
                              {appendResults[index]}
                            </div>
                          {/if}
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/if}
          {/if}
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Prompt configuration dialog -->
<Dialog.Root bind:open={showPromptDialog}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>配置Prompt模板</Dialog.Title>
      <Dialog.Description>
        自定义用于处理入排标准文本的Prompt模板
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      <textarea
        bind:value={promptTemplate}
        class="w-full h-64 p-3 border rounded font-mono text-sm"
        placeholder="输入Prompt模板..."
      ></textarea>

      <p class="text-sm text-gray-600 mt-2">
        提示: 模板中可以使用 {'{rule_description}'}, {'{parameter_schema}'}, {'{text_segment}'} 作为占位符。
      </p>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={()=>showPromptDialog = false}>
        取消
      </Button>
      <Button onclick={savePromptTemplate}>
        保存
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 历史记录对话框 -->
<Dialog.Root bind:open={showHistoryDialog}>
  <Dialog.Content class="w-full sm:max-w-[900px] max-h-[85vh] overflow-hidden flex flex-col">
    <Dialog.Header>
      <Dialog.Title>历史记录</Dialog.Title>
      <Dialog.Description>查看、复制或删除历史生成结果</Dialog.Description>
    </Dialog.Header>
    {#if historyError}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-2">{historyError}</div>
    {/if}
    <div class="px-2 mb-2 flex items-center gap-2">
      <input class="border rounded px-2 py-1 text-sm flex-1" placeholder="搜索文本..." bind:value={historySearch} onkeydown={(e)=>{ if(e.key==='Enter') searchHistories() }} />
      <Button variant="outline" size="sm" onclick={searchHistories}>搜索</Button>
      <Button variant="destructive" size="sm" onclick={batchDeleteHistory} disabled={!histories.some(h=>h.history_id && selectedHistoryIds[h.history_id])}>删除选中</Button>
    </div>
    <div class="flex-1 overflow-y-auto">
      {#if loadingHistories}
        <div class="p-4 text-sm text-gray-500">加载中...</div>
      {:else if histories.length === 0}
        <div class="p-4 text-sm text-gray-500">暂无历史</div>
      {:else}
        <div class="divide-y">
          {#each histories as h}
            <div class="p-3">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  {#if h.history_id}
                    <input type="checkbox" bind:checked={selectedHistoryIds[h.history_id]} />
                  {/if}
                  <div class="text-sm text-gray-600">{h.created_at} · {h.model_id || '未指定模型'}</div>
                </div>
                <div class="flex gap-2">
                  <Button variant="outline" size="sm" onclick={() => setClipboardText(h.generated_json || '')}>复制JSON</Button>
                  <Button variant="outline" size="sm" onclick={() => setClipboardText(h.segmented_text || h.input_text || '')}>复制文本</Button>
                  <Button variant="outline" size="sm" onclick={() => restoreFromHistory(h)}>恢复到编辑器</Button>
                  {#if h.history_id}
                    <Button variant="destructive" size="sm" onclick={() => deleteHistoryItem(h.history_id!)}>删除</Button>
                  {/if}
                </div>
              </div>
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <div class="text-xs text-gray-500 mb-1">文本</div>
                  <pre class="text-xs bg-gray-50 p-2 border rounded max-h-40 overflow-auto whitespace-pre-wrap">{h.segmented_text || h.input_text}</pre>
                </div>
                <div>
                  <div class="text-xs text-gray-500 mb-1">JSON</div>
                  <pre class="text-xs bg-gray-50 p-2 border rounded max-h-40 overflow-auto whitespace-pre-wrap">{h.generated_json}</pre>
                </div>
              </div>
              {#if h.notes}
                <div class="mt-2 text-xs text-gray-500">备注：{h.notes}</div>
              {/if}
            </div>
          {/each}
        </div>
      {/if}
    </div>
    <Dialog.Footer>
      <Button variant="outline" onclick={()=>showHistoryDialog=false}>关闭</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- Suggest multiple rules dialog -->
<Dialog.Root bind:open={showSuggestDialog}>
  <Dialog.Content class="w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
    <Dialog.Header class="shrink-0">
      <Dialog.Title>规则设置建议（多候选）</Dialog.Title>
      <Dialog.Description>
        基于未匹配段落生成了多个规则建议，请选择其中一个创建，或对比相似的现有规则。
      </Dialog.Description>
    </Dialog.Header>

    {#if suggestionError}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <span class="block sm:inline">{suggestionError}</span>
      </div>
    {/if}

    <div class="mb-4 p-4 bg-gray-100 rounded">
      <h3 class="text-sm font-medium mb-2">未匹配段落:</h3>
      <div class="p-3 bg-white border rounded text-sm">{selectedSegment}</div>
    </div>

    <div class="flex-1 overflow-y-auto space-y-4 px-1">
      {#each suggestedCandidates as cand, idx}
        <div class="border rounded bg-white">
          <div class="px-3 py-2 border-b flex justify-between items-center">
            <div class="font-medium text-sm">候选 {idx + 1}: {cand.rule_name}
              {#if cand.category}
                <span class="ml-2 px-2 py-0.5 text-xs rounded-full {cand.category === '入组标准' ? 'bg-blue-100 text-blue-800' : cand.category === '排除标准' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}">{cand.category}</span>
              {/if}
            </div>
            <div class="flex gap-2">
              <button class="text-xs px-2 py-1 bg-blue-600 text-white rounded" onclick={() => { suggestedRule = cand; selectedRule = cand; showCreateRuleDialog = true; }}>用此创建</button>
              {#if bestSimilar(cand)}
                <button class="text-xs px-2 py-1 bg-white border border-gray-300 rounded" onclick={() => openEditRuleDialog(bestSimilar(cand)!.rule, selectedSegment)}>
                  查看相似规则（{Math.round(bestSimilar(cand)!.score * 100)}%）
                </button>
              {/if}
            </div>
          </div>
          <div class="p-3 text-sm space-y-2">
            <div>
              <div class="font-medium mb-1">规则描述:</div>
              <div class="bg-gray-50 p-2 rounded border text-gray-800 whitespace-pre-line">{cand.rule_description || '(无)'}</div>
            </div>
            <div>
              <div class="font-medium mb-1">参数模式:</div>
              <pre class="bg-gray-50 p-2 rounded border overflow-x-auto text-xs">{cand.parameter_schema}</pre>
            </div>
          </div>
        </div>
      {/each}
      {#if suggestedCandidates.length === 0}
        <div class="text-center text-gray-500 py-10">暂无候选建议</div>
      {/if}
    </div>

    <Dialog.Footer class="shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4">
      <Button variant="outline" onclick={()=>{ showSuggestDialog = false; }}>关闭</Button>
    </Dialog.Footer>
  </Dialog.Content>
  
</Dialog.Root>

<!-- Edit rule dialog -->
<Dialog.Root bind:open={showEditRuleDialog}>
  <Dialog.Content class="w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
    <Dialog.Header class="shrink-0">
      <Dialog.Title>编辑规则定义</Dialog.Title>
      <Dialog.Description>
        编辑规则定义的信息和参数。
      </Dialog.Description>
    </Dialog.Header>

    <div class="flex-1 overflow-y-auto py-4 px-1">
      {#if selectedRule}
        <RuleDefinitionForm
          ruleDefinition={selectedRule}
          onsave={handleRuleSave}
        />
      {/if}
    </div>

    <Dialog.Footer class="shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4">
      <Button variant="outline" onclick={()=>showEditRuleDialog = false}>
        关闭
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- Create rule dialog -->
<Dialog.Root bind:open={showCreateRuleDialog}>
  <Dialog.Content class="w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
    <Dialog.Header class="shrink-0">
      <Dialog.Title>创建新规则定义</Dialog.Title>
      <Dialog.Description>
        基于未匹配的段落创建新的规则定义。
      </Dialog.Description>
    </Dialog.Header>

    <div class="mb-4 p-4 bg-gray-100 rounded">
      <h3 class="text-sm font-medium mb-2">段落文本:</h3>
      <div class="p-3 bg-white border rounded text-sm">{selectedSegment}</div>
    </div>

    <div class="flex-1 overflow-y-auto py-4 px-1">
      <RuleDefinitionForm
        ruleDefinition={suggestedRule}
        onsave={handleRuleSave}
      />
    </div>

    <Dialog.Footer class="shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4">
      <Button variant="outline" onclick={()=>showCreateRuleDialog = false}>
        关闭
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- Update rule description dialog -->
<Dialog.Root bind:open={isUpdatingRule}>
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>更新规则描述</Dialog.Title>
      <Dialog.Description>
        将当前段落追加到规则描述中。
      </Dialog.Description>
    </Dialog.Header>

    {#if updateError}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <span class="block sm:inline">{updateError}</span>
      </div>
    {/if}

    <div class="py-4">
      <div class="mb-4">
        <h3 class="text-sm font-medium mb-2">规则名称:</h3>
        <div class="p-2 bg-gray-100 rounded">{selectedRule?.rule_name || ''}</div>
      </div>

      <div class="mb-4">
        <h3 class="text-sm font-medium mb-2">段落文本:</h3>
        <div class="p-2 bg-gray-100 rounded">{selectedSegment}</div>
      </div>

      <p class="text-sm text-gray-600">
        此操作将把上述段落文本追加到规则描述中，以帮助LLM更好地理解规则。
      </p>
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={()=>isUpdatingRule = false}>
        取消
      </Button>
      <Button onclick={updateRuleDescription}>
        更新描述
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- Remove matched segment confirmation dialog -->
<Dialog.Root bind:open={showRemoveConfirmDialog}>
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>确认移除</Dialog.Title>
      <Dialog.Description>
        您确定要移除此匹配成功的段落吗？
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      {#if selectedMatchToRemove}
        <div class="mb-4">
          <h3 class="text-sm font-medium mb-2">规则名称:</h3>
          <div class="p-2 bg-gray-100 rounded">{selectedMatchToRemove.rule.rule_name}</div>
        </div>

        <div class="mb-4">
          <h3 class="text-sm font-medium mb-2">段落文本:</h3>
          <div class="p-2 bg-gray-100 rounded">{selectedMatchToRemove.segment}</div>
        </div>

        <p class="text-sm text-red-600">
          此操作将从结果中移除此段落，并更新生成的JSON。此操作不可撤销。
        </p>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={()=>showRemoveConfirmDialog = false}>
        取消
      </Button>
      <Button variant="destructive" onclick={confirmRemoveMatchedSegment}>
        确认移除
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 参数编辑对话框 - 参考 ProjectCriterionForm.svelte 重新设计 -->
<Dialog.Root bind:open={showEditParamsDialog}>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>编辑参数值</Dialog.Title>
      <Dialog.Description>
        修改匹配规则的参数值
      </Dialog.Description>
    </Dialog.Header>

    {#if updateError}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <span class="block sm:inline">{updateError}</span>
      </div>
    {/if}

    {#if updateSuccess}
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <span class="block sm:inline">{updateSuccess}</span>
      </div>
    {/if}

    <div class="py-4">
      {#if selectedMatchToEditParams}
        <!-- 规则信息 -->
        <div class="mb-4 border-b pb-3">
          <div class="flex items-center mb-2">
            <h3 class="text-sm font-medium mr-2">规则:</h3>
            <span class="font-semibold">{selectedMatchToEditParams.rule.rule_name}</span>
            {#if selectedMatchToEditParams.rule.category}
              <span class="ml-2 px-2 py-0.5 text-xs rounded-full {selectedMatchToEditParams.rule.category === '入组标准' ? 'bg-blue-100 text-blue-800' : selectedMatchToEditParams.rule.category === '排除标准' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}">
                {selectedMatchToEditParams.rule.category}
              </span>
            {/if}
          </div>
          <div class="text-xs text-gray-500">
            <div class="mb-1"><span class="font-medium">段落文本:</span> {selectedMatchToEditParams.segment}</div>
            <div><span class="font-medium">显示顺序:</span> {selectedMatchToEditParams.criterion.display_order}</div>
          </div>
        </div>

        <!-- 参数编辑表单 -->
        {#if selectedMatchToEditParams.rule.parameter_schema}
          {#key selectedMatchToEditParams.rule.rule_definition_id}
            <div class="space-y-4">
              <h3 class="text-sm font-medium">参数设置:</h3>

              <form id="param-edit-form" class="space-y-4 border rounded-lg p-4 bg-blue-50">
                {#if true}
                  {@const schema = ruleDesignerService.parseParameterSchema(selectedMatchToEditParams.rule.parameter_schema)}
                  {#if schema.parameters.length === 0}
                    <p class="text-sm text-slate-500">此规则没有可配置的参数</p>
                  {:else}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {#each schema.parameters as param}
                        <div>
                          <label for={param.name} class="block text-sm font-semibold text-blue-700 mb-2">
                            {param.label}
                            {#if param.required}
                              <span class="text-red-500 ml-0.5">*</span>
                            {/if}
                            {#if param.unit}
                              <span class="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full text-xs ml-1">
                                {param.unit}
                              </span>
                            {/if}
                          </label>

                          <!-- 根据参数类型渲染不同的输入组件 -->
                          {#if param.type === 'string'}
                            <input
                              type="text"
                              id={param.name}
                              value={editedParamValues[param.name] || ''}
                              oninput={(e) => updateParameterValue(param.name, e.currentTarget.value)}
                              class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                              placeholder={`请输入${param.label}`}
                              readonly={param.readonly}
                            />
                          {:else if param.type === 'integer'}
                            <input
                              type="number"
                              id={param.name}
                              value={editedParamValues[param.name] || ''}
                              oninput={(e) => updateParameterValue(param.name, parseInt(e.currentTarget.value) || null)}
                              class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                              placeholder={`请输入${param.label}`}
                              step="1"
                              readonly={param.readonly}
                            />
                          {:else if param.type === 'number'}
                            <input
                              type="number"
                              id={param.name}
                              value={editedParamValues[param.name] || ''}
                              oninput={(e) => updateParameterValue(param.name, parseFloat(e.currentTarget.value) || null)}
                              class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                              placeholder={`请输入${param.label}`}
                              step="any"
                              readonly={param.readonly}
                            />
                          {:else if param.type === 'boolean'}
                            <div class="flex items-center">
                              <input
                                type="checkbox"
                                id={param.name}
                                checked={editedParamValues[param.name] || false}
                                onchange={(e) => updateParameterValue(param.name, e.currentTarget.checked)}
                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                disabled={param.readonly}
                              />
                              <label for={param.name} class="ml-2 text-sm text-gray-700">
                                {param.label}
                              </label>
                            </div>
                          {:else if param.type === 'enum' && param.options}
                            <select
                              id={param.name}
                              value={editedParamValues[param.name] || ''}
                              onchange={(e) => updateParameterValue(param.name, e.currentTarget.value)}
                              class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                              disabled={param.readonly}
                            >
                              <option value="">请选择{param.label}</option>
                              {#each param.options as option}
                                <option value={option}>{option}</option>
                              {/each}
                            </select>
                          {/if}

                          <!-- 当前值显示 -->
                          <div class="mt-1 text-xs text-gray-500">
                            当前值:
                            <span class="font-mono bg-yellow-50 px-1 py-0.5 rounded border border-yellow-200">
                              {#if typeof editedParamValues[param.name] === 'boolean'}
                                {editedParamValues[param.name] ? '是' : '否'}
                              {:else if editedParamValues[param.name] === null || editedParamValues[param.name] === undefined}
                                <em>空</em>
                              {:else}
                                {editedParamValues[param.name]}
                              {/if}
                            </span>
                          </div>
                        </div>
                      {/each}
                    </div>
                  {/if}
                {/if}
              </form>
            </div>
          {/key}
        {:else}
          <!-- 即使规则没有定义参数，也根据规则名称生成参数编辑框 -->
          <div class="space-y-4">
            <h3 class="text-sm font-medium">根据规则名称生成的参数:</h3>

            {#if true}
              {@const generatedSchema = generateDefaultParametersFromRuleName(selectedMatchToEditParams.rule.rule_name)}
              <form id="param-edit-form" class="space-y-4 border rounded-lg p-4 bg-blue-50">
                {#if generatedSchema.parameters.length === 0}
                  <p class="text-sm text-slate-500">无法从规则名称生成参数</p>
                {:else}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {#each generatedSchema.parameters as param}
                      <div>
                        <label for={param.name} class="block text-sm font-semibold text-blue-700 mb-2">
                          {param.label}
                          {#if param.required}
                            <span class="text-red-500 ml-0.5">*</span>
                          {/if}
                        </label>

                        <!-- 根据参数类型渲染不同的输入组件 -->
                        {#if param.type === 'string'}
                          <input
                            type="text"
                            id={param.name}
                            value={editedParamValues[param.name] || ''}
                            oninput={(e) => updateParameterValue(param.name, e.currentTarget.value)}
                            class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                            placeholder={`请输入${param.label}`}
                          />
                        {:else if param.type === 'integer'}
                          <input
                            type="number"
                            id={param.name}
                            value={editedParamValues[param.name] || ''}
                            oninput={(e) => updateParameterValue(param.name, parseInt(e.currentTarget.value) || null)}
                            class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                            placeholder={`请输入${param.label}`}
                            step="1"
                          />
                        {:else if param.type === 'number'}
                          <input
                            type="number"
                            id={param.name}
                            value={editedParamValues[param.name] || ''}
                            oninput={(e) => updateParameterValue(param.name, parseFloat(e.currentTarget.value) || null)}
                            class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                            placeholder={`请输入${param.label}`}
                            step="any"
                          />
                        {:else if param.type === 'boolean'}
                          <div class="flex items-center">
                            <input
                              type="checkbox"
                              id={param.name}
                              checked={editedParamValues[param.name] || false}
                              onchange={(e) => updateParameterValue(param.name, e.currentTarget.checked)}
                              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <label for={param.name} class="ml-2 text-sm text-gray-700">
                              {param.label}
                            </label>
                          </div>
                        {:else if param.type === 'enum' && param.options}
                          <select
                            id={param.name}
                            value={editedParamValues[param.name] || ''}
                            onchange={(e) => updateParameterValue(param.name, e.currentTarget.value)}
                            class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                          >
                            <option value="">请选择{param.label}</option>
                            {#each param.options as option}
                              <option value={option}>{option}</option>
                            {/each}
                          </select>
                        {/if}

                        <!-- 当前值显示 -->
                        <div class="mt-1 text-xs text-gray-500">
                          当前值:
                          <span class="font-mono bg-yellow-50 px-1 py-0.5 rounded border border-yellow-200">
                            {#if typeof editedParamValues[param.name] === 'boolean'}
                              {editedParamValues[param.name] ? '是' : '否'}
                            {:else if editedParamValues[param.name] === null || editedParamValues[param.name] === undefined}
                              <em>空</em>
                            {:else}
                              {editedParamValues[param.name]}
                            {/if}
                          </span>
                        </div>
                      </div>
                    {/each}
                  </div>
                {/if}
              </form>

              <div class="text-xs text-gray-500 mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                注意: 此规则没有定义参数模式，已根据规则名称 "{selectedMatchToEditParams.rule.rule_name}" 自动生成参数。
              </div>
            {/if}
          </div>
        {/if}
      {:else}
        <div class="text-gray-500 p-4 border border-gray-200 rounded-md bg-gray-50">
          未选择匹配项
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={()=>showEditParamsDialog = false}>
        取消
      </Button>
      <Button
        variant="default"
        class="bg-blue-600 hover:bg-blue-700"
        onclick={saveParameterValues}
        disabled={isUpdatingParams || !selectedMatchToEditParams}
        form="param-edit-form"
      >
        {#if isUpdatingParams}
          <span class="inline-block w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></span>
          保存中...
        {:else}
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17 21 17 13 7 13 7 21"></polyline><polyline points="7 3 7 8 15 8"></polyline></svg>
          保存参数
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 规则列表对话框 -->
<Dialog.Root bind:open={showRulesListDialog}>
  <Dialog.Content class="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
    <Dialog.Header class="shrink-0">
      <Dialog.Title>当前规则列表</Dialog.Title>
      <Dialog.Description>
        显示当前可用的所有规则定义，共 {ruleDefinitions.length} 条规则
      </Dialog.Description>
    </Dialog.Header>

    <div class="flex-1 overflow-y-auto py-4 px-1">
      <div class="bg-gray-50 p-4 border rounded-md font-mono text-sm whitespace-pre-wrap">
        {rulesListText || '暂无规则定义'}
      </div>
    </div>

    <Dialog.Footer class="shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4">
      <Button variant="outline" onclick={() => showRulesListDialog = false}>
        关闭
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 项目导入对话框 -->
<Dialog.Root bind:open={showProjectImportDialog}>
  <Dialog.Content class="w-full sm:max-w-[700px] max-h-[85vh] overflow-hidden flex flex-col">
    <Dialog.Header>
      <Dialog.Title>导入到项目</Dialog.Title>
      <Dialog.Description>
        将生成的入排标准导入到指定项目中
      </Dialog.Description>
    </Dialog.Header>

    {#if importToProjectError}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <span class="block sm:inline">{importToProjectError}</span>
      </div>
    {/if}

    {#if importToProjectSuccess}
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <span class="block sm:inline">{importToProjectSuccess}</span>
        {#if selectedProject && selectedProject.project.project_id}
          <div class="mt-3 flex justify-end">
            <button
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center gap-2 text-sm font-medium"
              onclick={() => goto(`/projects/${selectedProject!.project.project_id}`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
              查看项目详情
            </button>
          </div>
        {/if}
      </div>
    {/if}

    <div class="flex-1 overflow-y-auto py-4">
      {#if !selectedProject}
        <!-- 项目搜索 -->
        <div class="mb-4">
          <label for="project-search" class="block text-sm font-medium text-gray-700 mb-1">
            搜索项目
          </label>
          <div class="relative">
            <div class="flex gap-2">
              <div class="relative flex-1">
                <input
                  id="project-search"
                  type="text"
                  bind:value={projectSearchQuery}
                  placeholder="输入项目名称..."
                  class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  oninput={handleSearchInput}
                  onkeydown={handleSearchKeydown}
                />
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search class="h-4 w-4 text-gray-400" />
                </div>
                {#if projectSearchQuery && projectSearchQuery.trim() !== ''}
                  <button
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    onclick={() => {
                      projectSearchQuery = '';
                      projectSearchResults = [];
                      showSearchDropdown = false;
                    }}
                    aria-label="清除搜索"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                  </button>
                {/if}
              </div>
              <Button
                onclick={searchProjects}
                disabled={isSearchingProjects || !projectSearchQuery.trim()}
                class="flex items-center gap-1"
              >
                {#if isSearchingProjects}
                  <Loader2 class="h-4 w-4 animate-spin" />
                  <span>搜索中...</span>
                {:else}
                  <span>搜索</span>
                {/if}
              </Button>
            </div>

            <!-- 实时搜索下拉结果 -->
            {#if showSearchDropdown && projectSearchResults.length > 0}
              <div class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-y-auto">
                {#each projectSearchResults as project, index}
                  <button
                    type="button"
                    class="w-full text-left px-4 py-2 hover:bg-blue-50 cursor-pointer {index === selectedResultIndex ? 'bg-blue-50' : ''}"
                    onclick={() => selectProject(project)}
                    onmouseover={() => selectedResultIndex = index}
                    onfocus={() => selectedResultIndex = index}
                  >
                    <div class="font-medium">{project.project.project_name}</div>
                    <div class="text-xs text-gray-500">{project.project.project_short_name}</div>
                  </button>
                {/each}
              </div>
            {/if}
      // init history tables
      await autoCriteriaHistoryService.init();
          </div>
        </div>

        <!-- 搜索结果列表 (当没有选择项目且不显示下拉框时) -->
        {#if !showSearchDropdown && projectSearchResults.length > 0 && !selectedProject}
          <div class="mt-4">
            <h3 class="text-sm font-medium text-gray-700 mb-2">搜索结果</h3>
            <div class="border rounded-md overflow-hidden">
              <div class="max-h-60 overflow-y-auto">
                {#each projectSearchResults as project}
                  <button
                    type="button"
                    class="w-full text-left p-3 border-b last:border-b-0 hover:bg-gray-50 cursor-pointer flex justify-between items-center"
                    onclick={() => selectProject(project)}
                  >
                    <div>
                      <div class="font-medium">{project.project.project_name}</div>
                      <div class="text-sm text-gray-500">{project.project.project_short_name}</div>
                    </div>
                    <span class="px-3 py-1 text-sm bg-blue-50 text-blue-600 border border-blue-200 rounded">选择</span>
                  </button>
                {/each}
              </div>
            </div>
          </div>
        {/if}
      {:else}
        <!-- 已选择的项目 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-700 mb-2">已选择的项目</h3>
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex justify-between items-center">
              <div>
                <div class="font-medium text-blue-800">{selectedProject.project.project_name}</div>
                <div class="text-sm text-blue-600">{selectedProject.project.project_short_name}</div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onclick={()=>selectedProject = null}
                class="text-blue-600 border-blue-300"
              >
                更换项目
              </Button>
            </div>
          </div>
        </div>

        <!-- JSON预览 -->
        <div class="mb-4">
          <h3 class="text-sm font-medium text-gray-700 mb-2">将导入以下数据</h3>
          <div class="bg-gray-50 border border-gray-200 rounded-md p-2 max-h-60 overflow-auto">
            <pre class="text-xs whitespace-pre-wrap break-words">{outputJson}</pre>
          </div>
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={()=>showProjectImportDialog = false}>
        关闭
      </Button>
      {#if selectedProject && !importToProjectSuccess}
        <Button
          onclick={importToProject}
          disabled={importingToProject || !selectedProject}
          class="bg-blue-600 hover:bg-blue-700"
        >
          {#if importingToProject}
            <Loader2 class="h-4 w-4 mr-2 animate-spin" />
            导入中...
          {:else}
            导入到项目
          {/if}
        </Button>
      {/if}
      {#if importToProjectSuccess && selectedProject && selectedProject.project.project_id}
        <Button
          onclick={() => goto(`/projects/${selectedProject!.project.project_id}`)}
          class="bg-green-600 hover:bg-green-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
          查看项目详情
        </Button>
      {/if}
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
