<script lang="ts">
	import '../app.css';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import SettingsDialog from '$lib/components/SettingsDialog.svelte';
	import { registerGlobalShortcuts } from '$lib/services/keyboardShortcutService';

	let { children } = $props();
	let showSettingsDialog = $state(false);

	// 注意: page 已经被弃用，但我们暂时保留它的使用
	// 在未来的版本中应该更新为新的 API

	onMount(() => {
		// 注册全局快捷键
		registerGlobalShortcuts();

		// 监听打开设置对话框的自定义事件
		const handleOpenSettings = () => {
			showSettingsDialog = true;
		};

		window.addEventListener('open-settings-dialog', handleOpenSettings);

		// 阻止Backspace键导航回上一页
		const preventBackspaceNavigation = (e: KeyboardEvent) => {
			// 如果按下的是Backspace键，并且不是在可编辑元素中（如输入框、文本区域等）
			if (e.key === 'Backspace' &&
				!(e.target instanceof HTMLInputElement ||
				  e.target instanceof HTMLTextAreaElement ||
				  (e.target instanceof HTMLElement && e.target.isContentEditable))) {
				e.preventDefault();
			}
		};

		// 添加键盘事件监听器
		window.addEventListener('keydown', preventBackspaceNavigation);

		return () => {
			window.removeEventListener('open-settings-dialog', handleOpenSettings);
			window.removeEventListener('keydown', preventBackspaceNavigation);
		};
	});
</script>

<SettingsDialog bind:open={showSettingsDialog} />

<div class="min-h-screen flex flex-col">
	<header class="border-b px-4 py-2">
		<nav class="flex gap-4">
			<a
				href="/"
				class="px-3 py-2 rounded-md text-sm font-medium"
				class:bg-slate-100={$page.url.pathname === '/'}
				class:text-slate-900={$page.url.pathname === '/'}
				class:hover:bg-slate-100={$page.url.pathname !== '/'}
				class:hover:text-slate-900={$page.url.pathname !== '/'}
			>
				首页
			</a>
			<a
				href="/projects"
				class="px-3 py-2 rounded-md text-sm font-medium"
				class:bg-slate-100={$page.url.pathname === '/projects' || $page.url.pathname.startsWith('/projects/')}
				class:text-slate-900={$page.url.pathname === '/projects' || $page.url.pathname.startsWith('/projects/')}
				class:hover:bg-slate-100={!($page.url.pathname === '/projects' || $page.url.pathname.startsWith('/projects/'))}
				class:hover:text-slate-900={!($page.url.pathname === '/projects' || $page.url.pathname.startsWith('/projects/'))}
			>
				项目管理
			</a>
		</nav>
	</header>

	<main class="flex-1">
		{@render children()}
	</main>
</div>
