import { invoke } from '@tauri-apps/api/core';
import { get } from 'svelte/store';
import settings from '$lib/stores/settings';
import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import type { Runnable } from "@langchain/core/runnables";
import { segmentText, hasOrRelationship, splitOrClauses } from '$lib/utils/criteriaTextProcessor';

// 规则定义类型
export interface RuleDefinition {
  rule_definition_id?: number;
  rule_name: string;
  rule_description?: string;
  category?: string;
  parameter_schema: string; // JSON string
  label?: string;
  created_at?: string;
  updated_at?: string;
}

// 规则建议类型（与 RuleDefinition 结构一致但无 ID）
export type RuleSuggestion = Omit<RuleDefinition, 'rule_definition_id' | 'created_at' | 'updated_at'>;

// 生成的标准项类型
export interface GeneratedCriterion {
  rule_definition_id: number;
  parameter_values: Record<string, any>;
  display_order: number;
}

// 生成的标准组类型
export interface GeneratedOrGroup {
  group_id: number;
  criteria_ids: number[];
  operator: string;
}

// 最终生成的JSON结构
export interface GeneratedCriteriaJson {
  criteria: GeneratedCriterion[];
  or_groups: GeneratedOrGroup[];
}

// 处理结果类型
interface ProcessResult {
  success: boolean;
  criterion?: GeneratedCriterion;
  error?: string;
}

/**
 * 入排标准生成器类
 */
export class CriteriaGenerator {
  private apiKey: string;
  private baseUrl: string;
  private modelId: string;
  private ruleDefinitions: RuleDefinition[] = [];
  private ruleIndex: Array<{
    rule: RuleDefinition;
    keywords: Set<string>;
    units: Set<string>;
    paramNames: Set<string>;
  }> = [];
  private model: ChatOpenAI;
  private chain: Runnable | null = null;
  private promptTemplate: string;
  private suggestChain: Runnable | null = null;
  private suggestMultiChain: Runnable | null = null;

  /**
   * 构造函数
   */
  constructor() {
    console.log('初始化 CriteriaGenerator...');

    // 从设置存储中获取配置
    const settingsData = get(settings);
    console.log('从设置中获取配置:', {
      hasApiKey: !!settingsData.openrouterApiKey,
      hasModels: settingsData.models?.length > 0,
      baseUrl: settingsData.openrouterSiteUrl
    });

    // 设置 API 密钥
    this.apiKey = settingsData.openrouterApiKey || '';
    if (!this.apiKey) {
      console.warn('警告: API 密钥未设置');
    }

    // 设置 API 基础 URL
    this.baseUrl = settingsData.openrouterSiteUrl || 'https://openrouter.ai/api/v1';

    // 确保 baseUrl 不包含 /chat/completions
    if (this.baseUrl.endsWith('/chat/completions')) {
      this.baseUrl = this.baseUrl.replace('/chat/completions', '');
      console.log('已移除 baseUrl 中的 /chat/completions 路径，现在是:', this.baseUrl);
    }

    // 设置模型 ID
    this.modelId = settingsData.models && settingsData.models.length > 0
      ? settingsData.models[0].id
      : 'google/gemini-2.0-flash-001';
    console.log('使用模型 ID:', this.modelId);

    // 初始化 Langchain 模型
    try {
      this.model = new ChatOpenAI({
        apiKey: this.apiKey,
        modelName: this.modelId,
        openAIApiKey: this.apiKey, // 兼容性字段
        configuration: {
          baseURL: this.baseUrl,
          defaultHeaders: {
            'HTTP-Referer': 'https://peckbyte.app',
            'X-Title': 'PeckByte Criteria Generator',
            'Content-Type': 'application/json'
          }
        },
        temperature: 0.1, // 降低随机性以获取更一致的 JSON 输出
        maxRetries: 2,
      });
      console.log('成功初始化 ChatOpenAI 模型');
    } catch (error) {
      console.error('初始化 ChatOpenAI 模型失败:', error);
      throw new Error(`初始化模型失败: ${error instanceof Error ? error.message : String(error)}`);
    }

    // 设置默认提示模板
    this.promptTemplate = this.getDefaultPromptTemplate();
    console.log('CriteriaGenerator 构造函数完成');
  }

  /**
   * 初始化生成器
   */
  async initialize(): Promise<void> {
    try {
      // 加载规则定义
      await this.loadRuleDefinitions();

      // 构建规则索引
      this.buildRuleIndex();

      // 设置调用链
      this.setupChain();

      console.log('CriteriaGenerator 初始化完成，已加载规则定义:', this.ruleDefinitions.length);
    } catch (error) {
      console.error('CriteriaGenerator 初始化失败:', error);
      throw new Error(`初始化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 刷新规则定义
   * 用于在添加新规则后重新加载规则定义
   */
  async refreshRuleDefinitions(): Promise<void> {
    try {
      console.log('刷新规则定义...');
      await this.loadRuleDefinitions();
      console.log('规则定义已刷新，当前规则数量:', this.ruleDefinitions.length);
    } catch (error) {
      console.error('刷新规则定义失败:', error);
      throw new Error(`刷新规则定义失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 加载规则定义
   */
  private async loadRuleDefinitions(): Promise<void> {
    try {
      console.log('开始加载规则定义...');

      // 使用固定的数据库路径
      const dbPath = '/Users/<USER>/我的文档/sqlite/peckbyte.db';
      console.log('使用数据库路径:', dbPath);

      // 调用后端命令获取规则定义
      const query = {}; // 空查询获取所有规则定义

      try {
        // 注意：参数名必须与后端命令定义匹配
        this.ruleDefinitions = await invoke<RuleDefinition[]>('get_rule_definitions', {
          query,
          dbPath: dbPath
        });

        console.log('已加载规则定义:', this.ruleDefinitions.length);

        if (this.ruleDefinitions.length === 0) {
          console.warn('警告: 未找到任何规则定义');
        }
      } catch (invokeError) {
        console.error('调用 get_rule_definitions 失败:', invokeError);
        // 如果调用失败，使用空数组
        this.ruleDefinitions = [];
        throw new Error(`调用 get_rule_definitions 失败: ${invokeError instanceof Error ? invokeError.message : String(invokeError)}`);
      }
    } catch (error) {
      console.error('加载规则定义失败:', error);
      throw new Error(`加载规则定义失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 解析 parameter_schema（支持字符串或对象）
   */
  private parseParameterSchema(schema: string | any): { parameters: Array<{ name: string; type: string; unit?: string; options?: string[] }> } {
    try {
      const obj = typeof schema === 'string' ? JSON.parse(schema) : schema;
      if (obj && Array.isArray(obj.parameters)) {
        return {
          parameters: obj.parameters.map((p: any) => ({
            name: String(p.name || '').trim(),
            type: String(p.type || '').trim(),
            unit: p.unit ? String(p.unit) : undefined,
            options: Array.isArray(p.options) ? p.options.map((o: any) => String(o)) : undefined,
          }))
        };
      }
    } catch {}
    return { parameters: [] };
  }

  /**
   * 构建规则检索索引（基于名称/描述/参数名/单位的关键词集）
   */
  private buildRuleIndex(): void {
    const norm = (s: string) => (s || '')
      .toLowerCase()
      // 统一微符号与单位：µ/μ -> μ，ul -> μl
      .replace(/[µμ]/g, 'μ')
      .replace(/ul/g, 'μl')
      .replace(/\s+/g, '')
      .replace(/[，,；;。\.]/g, '');

    const synonyms: Record<string, string[]> = {
      // 双向同义词：无论命中“eos”还是“嗜酸”，都能互相扩展
      'eos': ['eos', '嗜酸', '嗜酸粒', '嗜酸性粒', 'eosinophil', 'eosinophils'],
      '嗜酸': ['eos', '嗜酸粒', '嗜酸性粒', 'eosinophil', 'eosinophils'],
      'fev1': ['肺功能', '一秒用力呼气量', 'fev1', '给药前', '舒张前', 'pre-bd', '给药后', '舒张后', 'post-bd'],
      'bmi': ['bmi', '体质指数', 'kg/m2', 'kg/m²', 'kg/㎡'],
      'fvc': ['肺活量', 'fvc'],
      'fev1/fvc': ['fev1/fvc', 'tiffeneau'],
      '年龄': ['岁', '年龄'],
      '吸烟史': ['吸烟', '包年', '烟'],
      '诊断史': ['确诊', '诊断', '病程'],
      '哮喘': ['哮喘', 'asthma', 'gina', 'gina定义'],
      'mMRC': ['mmrc', 'm mrc', '呼吸困难评分'],
      'ACQ': ['acq', '哮喘控制'],
      '性别': ['性别', '男女', '男或女', '男女不限', '性别不限', 'male', 'female'],
      'HRCT': ['hrct', '高分辨率ct', '高分辨率计算机断层', '高分辨率计算机断层扫描', '胸部hrct', '胸部ct'],
      '支气管扩张': ['支扩', '支气管扩张'],
      '痰脓性评分': ['脓性评分', '痰评分', 'purulence', 'purulence score', '痰脓性评分'],
      '维持治疗': ['三联', '双联', 'ics', 'laba', 'lama', '维持治疗', '吸入治疗'],
      '急性加重': ['加重', '急性加重', '急性发作', '发作', 'exacerbation', 'asthma exacerbation', 'aecopd'],
    };

    this.ruleIndex = this.ruleDefinitions.map((rule) => {
      const kw = new Set<string>();
      const un = new Set<string>();
      const pn = new Set<string>();

      const add = (s?: string) => { if (s) kw.add(norm(s)); };
      add(rule.rule_name);
      add(rule.rule_description);
      // 将可选 label 也纳入关键词
      add((rule as any).label);

      const parsed = this.parseParameterSchema(rule.parameter_schema);
      parsed.parameters.forEach(p => {
        if (p.name) pn.add(norm(p.name));
        if (p.unit) un.add(norm(p.unit));
        if (Array.isArray(p.options)) p.options.forEach(o => kw.add(norm(o)));
      });

      // 从描述与 label 中提取可匹配的词元（如 EOS）
      const collectTokens = (text?: string) => {
        if (!text) return;
        const lower = text.toLowerCase().replace(/[µμ]/g, 'μ');
        // 提取字母/数字/常见缩写作为 token
        const tokens = lower.match(/[a-zA-Z]+|[0-9]+|eosinophils?|eos/g);
        if (tokens) tokens.forEach(t => kw.add(norm(t)));
      };
      collectTokens(rule.rule_description);
      collectTokens((rule as any).label);

      // 添加同义词
      Object.entries(synonyms).forEach(([key, arr]) => {
        const needle = norm(key);
        if (norm(rule.rule_name).includes(needle) || norm(rule.rule_description || '').includes(needle)) {
          arr.forEach(a => kw.add(norm(a)));
        }
      });

      return { rule, keywords: kw, units: un, paramNames: pn };
    });
  }

  /**
   * 设置调用链
   */
  private setupChain(): void {
    // 创建提示模板
    const prompt = new PromptTemplate({
      template: this.promptTemplate,
      inputVariables: ["rules_list", "text_segment"]
    });

    // 创建 JSON 输出解析器
    const parser = new JsonOutputParser<ProcessResult>();

    // 使用 LCEL 构建链
    this.chain = prompt.pipe(this.model).pipe(parser);
    console.log("Langchain 调用链已设置");

    // 设置规则建议链
    const suggestPrompt = new PromptTemplate({
      template: `你是规则设计助手。请基于未匹配的入排标准文本，提出一个新的规则定义建议，并严格输出 JSON：\n\n【未匹配文本】\n{text_segment}\n\n【输出约束】\n- 仅输出一个 JSON 对象，不要任何解释或多余文本；\n- 字段：rule_name, rule_description, category, parameter_schema；\n- category 取 "入组标准" 或 "排除标准" 或 "其他"；\n- parameter_schema 是一个对象字符串，包含 parameters 数组，每个参数含 name,label,type,required,unit,options,readonly；\n- type 取 string/integer/number/boolean/enum；\n- 单位要符合文本（如 岁、月、次、%、个细胞/μL、包/年）；\n- name 使用驼峰或下划线英文命名，不要中文；\n\n【示例参数】\n- 年龄范围: Min_age/Max_age (integer, 单位: 岁)；\n- 吸烟史: smoking_history (integer, 单位: 包/年)；\n- FEV1 百分比: Pre_BD_FEV1_min/Pre_BD_FEV1_max (integer, 单位: %)；\n- 嗜酸计数: Min_count/Max_count (integer, 单位: 个细胞/μL)；\n- 维持治疗: Type(string), Maintenance_duration(integer, 月), Stable_dosage_duration(integer, 月)；\n- 急性加重: date_number(integer, 月), Min_count(integer, 次), degree(enum: 轻度|中度|重度), Remark(string)。\n\n以 JSON 形式输出：{{ "rule_name":..., "rule_description":..., "category":..., "parameter_schema": "{{\"parameters\":[...]}}" }}`,
      inputVariables: ["text_segment"]
    });
    const suggestParser = new JsonOutputParser<RuleSuggestion>();
    this.suggestChain = suggestPrompt.pipe(this.model).pipe(suggestParser);

    // 设置多候选规则建议链
    const suggestMultiPrompt = new PromptTemplate({
      template: `你是规则设计助手。请基于未匹配的入排标准文本，提出3个不同角度的“规则定义建议”，严格输出 JSON 数组。\n\n【未匹配文本】\n{text_segment}\n\n【输出约束】\n- 仅输出一个 JSON 数组，包含 2-3 个对象；\n- 每个对象字段：rule_name, rule_description, category, parameter_schema；\n- category 为 "入组标准"/"排除标准"/"其他"；\n- parameter_schema 是对象字符串，包含 parameters 数组：name,label,type,required,unit,options,readonly；\n- 不同候选在命名或参数上有合理差异（例如参数是否细分、是否包含备注、不同单位表达等）；\n- 单位遵循医学表达（岁、月、次、%、个细胞/μL、包/年等）。`,
      inputVariables: ["text_segment"]
    });
    const suggestMultiParser = new JsonOutputParser<RuleSuggestion[]>();
    this.suggestMultiChain = suggestMultiPrompt.pipe(this.model).pipe(suggestMultiParser);
  }

  /**
   * 获取默认提示模板
   */
  getDefaultPromptTemplate(): string {
    return `你是临床研究入排标准的结构化抽取助手。请仅在提供的候选规则中选择最匹配的一条，并抽取该规则的参数为JSON。

【候选规则（已精简）】
{rules_list}

【待处理文本】
{text_segment}

【输出约束】
1. 只能从候选规则中选择一个 rule_definition_id；
2. 仅返回该规则参数模式中出现的参数；
3. 严格使用正确的类型与单位（integer/number/boolean/string/enum），不要返回字符串数字；
4. 不确定时返回 success=false，并在 error 中说明原因（如“歧义/不在候选规则范围”）。
5. 你的整个回复必须是一个有效的 JSON 对象，不要输出任何额外文本、注释、解释、代码块标记或前后缀；不要输出中文方括号等说明性标记。
6. 严禁返回规则序号或索引（如 1/2/3）；必须返回候选中的 rule_definition_id 数字。

【区分示例】
- 实验室检测（如“EOS≥150 个/μL”）→ 血嗜酸粒细胞计数；
- 评估量表（“mMRC/CAT/ACQ”）→ 对应评分规则；
- 治疗方案（“三联/ICS+LABA+LAMA/维持治疗”）→ 维持治疗类规则；
- 数值单位必须匹配（岁、%、包/年、月、个/μL）。
 - 当文本涉及 FEV1 百分比阈值（如“FEV1≥20%且≤70%”）且候选中存在“支气管舒张剂前（Pre-BD）FEV1”规则时，应选择该规则并抽取最小/最大百分比参数；不要生成候选外的规则。

【响应JSON模板】
{{
  "success": true|false,
  "criterion": {{
    "rule_definition_id": number,
    "parameter_values": {{ }}
  }},
  "error": string|undefined
}}`;
  }

  /**
   * 从模型输出中尽力提取 JSON
   */
  private extractJsonFromText(text: string): ProcessResult | null {
    try {
      // 优先尝试直接解析
      const direct = JSON.parse(text);
      return direct;
    } catch {}

    // 去除代码块围栏
    let cleaned = text.replace(/```[a-z]*\n?|```/gi, '');
    // 去除明显的提示性括注
    cleaned = cleaned.replace(/【[^】]*】/g, '');
    // 定位第一个 { 与最后一个 }
    const start = cleaned.indexOf('{');
    const end = cleaned.lastIndexOf('}');
    if (start === -1 || end === -1 || end <= start) return null;
    let slice = cleaned.slice(start, end + 1);

    const tryParses: string[] = [];
    tryParses.push(slice);
    // 常见容错：单引号->双引号、未加引号的键名加引号、去掉尾逗号
    tryParses.push(
      slice
        .replace(/'([^']*)'/g, '"$1"')
        .replace(/(\w+)\s*:/g, '"$1":')
        .replace(/,\s*}/g, '}')
        .replace(/,\s*]/g, ']')
    );

    for (const cand of tryParses) {
      try {
        const obj = JSON.parse(cand);
        return obj;
      } catch {}
    }
    return null;
  }

  /**
   * 设置提示模板
   */
  setPromptTemplate(template: string): void {
    this.promptTemplate = this.sanitizePromptTemplate(template);
    this.setupChain();
  }

  /**
   * 将自定义 Prompt 模板中的字面量花括号自动转义，保留变量占位符
   */
  private sanitizePromptTemplate(input: string): string {
    if (!input) return input;
    // 保护占位符
    const placeholders = ["{rules_list}", "{text_segment}"];
    const tokens = placeholders.map((_, i) => `__PLACEHOLDER_${i}__`);
    let tmp = input;
    placeholders.forEach((ph, i) => {
      tmp = tmp.split(ph).join(tokens[i]);
    });
    // 转义所有字面量花括号
    tmp = tmp.replace(/\{/g, '{{').replace(/\}/g, '}}');
    // 还原占位符
    tokens.forEach((tk, i) => {
      tmp = tmp.split(tk).join(placeholders[i]);
    });
    return tmp;
  }

  /**
   * 生成规则定义建议（当未匹配任何规则时）
   */
  async suggestRuleDefinition(textSegment: string): Promise<RuleSuggestion> {
    if (!this.suggestChain) {
      this.setupChain();
    }

    // 首选：LLM 生成
    try {
      const suggestion = await (this.suggestChain as any).invoke({ text_segment: textSegment });
      // 粗校验 parameter_schema
      let schemaStr = suggestion.parameter_schema;
      if (typeof schemaStr !== 'string') {
        schemaStr = JSON.stringify(schemaStr);
      }
      // 尝试解析以确保有效
      JSON.parse(schemaStr);
      return {
        rule_name: suggestion.rule_name || textSegment.slice(0, 30),
        rule_description: suggestion.rule_description || textSegment,
        category: suggestion.category || '入组标准',
        parameter_schema: schemaStr
      };
    } catch (e) {
      console.warn('LLM 规则建议失败，使用回退：', e);
    }

    // 回退：基于启发式
    const lower = textSegment.toLowerCase();
    let suggestion: RuleSuggestion = {
      rule_name: '自定义规则',
      rule_description: textSegment,
      category: '入组标准',
      parameter_schema: JSON.stringify({ parameters: [] })
    };

    const params: any[] = [];
    const add = (p: any) => params.push(p);
    const has = (s: string) => lower.includes(s);

    // 常见模式
    if (has('吸烟')) {
      suggestion.rule_name = '吸烟史';
      add({ name: 'smoking_history', label: '吸烟史至少', type: 'integer', required: false, options: [], unit: '包/年', readonly: false });
    }
    if (/(fev1|用力呼气)/i.test(textSegment)) {
      suggestion.rule_name = '支气管舒张剂前（Pre-BD）FEV1';
      add({ name: 'Pre_BD_FEV1_min', label: '（Pre-BD）FEV1最小值', type: 'integer', required: false, options: [], unit: '%', readonly: false });
      add({ name: 'Pre_BD_FEV1_max', label: '（Pre-BD）FEV1最大值', type: 'integer', required: false, options: [], unit: '%', readonly: false });
    }
    if (/(eos|嗜酸)/i.test(textSegment)) {
      suggestion.rule_name = '血嗜酸粒细胞计数';
      add({ name: 'Min_count', label: '≥', type: 'integer', required: false, options: [], unit: '个细胞/μL', readonly: false });
      add({ name: 'data_range', label: '有效日期范围。', type: 'string', required: false, options: [], unit: '', readonly: false });
      add({ name: 'Max_count', label: '＜', type: 'integer', required: false, options: [], unit: '个细胞/μL', readonly: false });
    }
    if (/维持治疗|laba|lama|ics/i.test(textSegment)) {
      suggestion.rule_name = '慢阻肺、哮喘吸入药物维持治疗';
      add({ name: 'Type', label: '维持治疗药物类型', type: 'string', required: false, options: [], unit: '', readonly: false });
      add({ name: 'Maintenance_duration', label: '至少维持使用', type: 'integer', required: false, options: [], unit: '月', readonly: false });
      add({ name: 'Stable_dosage_duration', label: '剂量稳定至少', type: 'integer', required: false, options: [], unit: '月', readonly: false });
    }
    if (/急性加重|aecopd/i.test(textSegment)) {
      suggestion.rule_name = '慢阻肺病或哮喘急性发作病史';
      add({ name: 'date_number', label: '多少个月内', type: 'integer', required: false, options: [], unit: '月', readonly: false });
      add({ name: 'Min_count', label: '≥', type: 'integer', required: false, options: [], unit: '次', readonly: false });
      add({ name: 'degree', label: '严重程度要求', type: 'enum', required: false, options: ['轻度','中度','重度'], unit: '', readonly: false });
      add({ name: 'Remark', label: '备注', type: 'string', required: false, options: [], unit: '', readonly: false });
    }

    if (params.length === 0) {
      // 通用参数
      add({ name: 'value', label: '参数值', type: 'string', required: false, options: [], unit: '', readonly: false });
    }
    suggestion.parameter_schema = JSON.stringify({ parameters: params }, null, 2);
    return suggestion;
  }

  /**
   * 生成多候选规则建议
   */
  async suggestMultipleRuleDefinitions(textSegment: string, k = 3): Promise<RuleSuggestion[]> {
    if (!this.suggestMultiChain) {
      this.setupChain();
    }
    // 首选：LLM 生成多候选
    try {
      const arr = await (this.suggestMultiChain as any).invoke({ text_segment: textSegment });
      const cleaned: RuleSuggestion[] = [];
      for (const s of arr || []) {
        let schemaStr = typeof s.parameter_schema === 'string' ? s.parameter_schema : JSON.stringify(s.parameter_schema);
        JSON.parse(schemaStr); // 校验
        cleaned.push({
          rule_name: s.rule_name || textSegment.slice(0, 30),
          rule_description: s.rule_description || textSegment,
          category: s.category || '入组标准',
          parameter_schema: schemaStr
        });
        if (cleaned.length >= k) break;
      }
      if (cleaned.length) return cleaned;
    } catch (e) {
      console.warn('LLM 多候选建议失败，使用单候选/回退：', e);
    }
    // 回退：使用单候选并复制衍生
    const single = await this.suggestRuleDefinition(textSegment);
    const variants: RuleSuggestion[] = [single];
    // 简单变体：添加 Remark/拆分/单位切换（仅做示意）
    try {
      const base = JSON.parse(single.parameter_schema);
      variants.push({
        rule_name: single.rule_name + '（含备注）',
        rule_description: single.rule_description,
        category: single.category,
        parameter_schema: JSON.stringify({
          parameters: [...(base.parameters || []), { name: 'Remark', label: '备注', type: 'string', required: false, options: [], unit: '', readonly: false }]
        })
      });
    } catch {}
    return variants.slice(0, k);
  }

  /**
   * 设置模型ID
   */
  setModelId(modelId: string): void {
    if (modelId && modelId !== this.modelId) {
      this.modelId = modelId;

      // 重新创建模型实例
      this.model = new ChatOpenAI({
        apiKey: this.apiKey,
        modelName: modelId,
        openAIApiKey: this.apiKey,
        configuration: {
          baseURL: this.baseUrl,
          defaultHeaders: {
            'HTTP-Referer': 'https://peckbyte.app',
            'X-Title': 'PeckByte Criteria Generator',
            'Content-Type': 'application/json'
          }
        },
        temperature: 0.1,
        maxRetries: 2,
      });

      // 重新设置调用链
      this.setupChain();
      console.log('已更新模型ID:', modelId);
    }
  }

  /**
   * 准备规则列表文本
   */
  private prepareRulesList(): string {
    // 包含所有规则，即使没有描述也使用规则名称作为描述
    const validRules = this.ruleDefinitions;

    // 构建规则列表文本
    let rulesListText = '';

    validRules.forEach((rule, index) => {
      rulesListText += `规则 ${index + 1}:\n`;
      rulesListText += `ID: ${rule.rule_definition_id}\n`;
      rulesListText += `名称: ${rule.rule_name}\n`;
      // 清理描述格式：移除换行符和多余的分号
      const description = this.cleanDescription(rule.rule_description || rule.rule_name);
      rulesListText += `描述: ${description}\n`;
      rulesListText += `参数模式: ${rule.parameter_schema}\n\n`;
    });

    return rulesListText;
  }

  /**
   * 清理规则描述中的特殊字符
   */
  private cleanDescription(description: string): string {
    if (!description) return '';
    return description
      .replace(/[；，\n\r]+/g, '；') // 替换多个分隔符为单个分号
      .replace(/[；]$/g, '') // 移除末尾的分号
      .replace(/^[；，]+/g, '') // 移除开头的分号或逗号
      .replace(/\s+/g, ' ') // 替换多个空格为单个空格
      .trim();
  }

  /**
   * 分割文本为段落
   */
  private segmentText(text: string): string[] {
    // 使用工具类中的分割函数
    return segmentText(text);
  }

  /**
   * Top-K 规则召回（基于关键词/单位/参数名的简单打分）
   */
  private getTopKRulesForSegment(segment: string, k = 4): RuleDefinition[] {
    const norm = (s: string) => (s || '')
      .toLowerCase()
      // 统一微符号与单位
      .replace(/[µμ]/g, 'μ')
      .replace(/ul/g, 'μl')
      .replace(/\s+/g, '')
      .replace(/[，,；;。\.]/g, '');
    const text = norm(segment);

    const score = (entry: typeof this.ruleIndex[number]) => {
      let s = 0;
      entry.keywords.forEach(w => { if (w && text.includes(w)) s += 3; });
      entry.units.forEach(u => { if (u && text.includes(u)) s += 2; });
      entry.paramNames.forEach(p => { if (p && text.includes(p)) s += 2; });
      // 数值与百分号等形态加权
      if (/\d+%/.test(segment)) s += 1;
      if (/\d+\s*岁/.test(segment)) s += 1;
      if (/(包\/年|个\s*细胞\/[μµ]l|个\s*细胞\/ul|[μµ]l|ul|kg\s*\/\s*m\s*(2|²|㎡))/i.test(segment)) s += 1;
      if (/mmrc|acq|cat/i.test(segment)) s += 2;
      if (/ics|laba|lama|三联|双联|维持治疗/i.test(segment)) s += 2;
      if (/hrct|高分辨率|支气管扩张/i.test(segment)) s += 3;
      if (/性别|男女|男或女|男女不限|性别不限/.test(segment)) s += 3;
      if (/评分/.test(segment) && /分/.test(segment)) s += 2;
      // 诊断/哮喘 优先
      if (/(确诊|诊断)/.test(segment) && /(病史|确诊)/.test(`${entry.rule.rule_name} ${(entry.rule.rule_description || '')}`)) s += 3;
      if (/(哮喘|asthma|gina)/i.test(segment) && /哮喘/.test(`${entry.rule.rule_name} ${(entry.rule.rule_description || '')}`)) s += 4;

      // 针对 EOS + 个细胞/μL 的加权
      const isEosText = /\beos\b/i.test(segment);
      if (isEosText) s += 2;
      const eosStrong = /\beos\b[\s\S]*?(个\s*细胞\s*\/[μµ]l|个\s*细胞\s*\/ul)/i.test(segment);
      const isEosRule = /(嗜酸|eosinophil)/i.test(
        `${entry.rule.rule_name} ${(entry.rule.rule_description || '')} ${((entry.rule as any).label || '')}`
      );
      if (isEosRule && eosStrong) s += 5;

      // 针对 FEV1 百分比（含给药前/后）的加权
      const hasFEV1 = /\bfev1\b/i.test(segment) || /一秒用力呼气量/.test(segment);
      const hasPercent = /\d+\s*%/.test(segment);
      if (hasFEV1 && hasPercent) {
        s += 3;
        const isPreAfter = /(给药前|舒张前|pre-bd)/i.test(segment);
        const isPostAfter = /(给药后|舒张后|post-bd)/i.test(segment);
        const isFEV1Rule = /fev1/i.test(`${entry.rule.rule_name} ${(entry.rule.rule_description || '')} ${((entry.rule as any).label || '')}`);
        if (isFEV1Rule) {
          if (isPreAfter) s += 3;
          if (isPostAfter) s += 2; // 当前库仅有 Pre-BD，仍给予正向加权
        }
      }

      // 针对 BMI（kg/m2）的加权
      const hasBMI = /\bbmi\b/i.test(segment) || /kg\s*\/\s*m\s*(2|²|㎡)/i.test(segment);
      const isBMIRule = /bmi|体重|kg\s*\/\s*m\s*2/i.test(`${entry.rule.rule_name} ${(entry.rule.rule_description || '')} ${((entry.rule as any).label || '')}`);
      if (hasBMI && isBMIRule) s += 4;
      // 针对痰脓性评分
      const hasPurulence = /脓性|purulence/i.test(segment) && /评分/.test(segment);
      const isPurRule = /脓性|purulence/i.test(`${entry.rule.rule_name} ${(entry.rule.rule_description || '')} ${((entry.rule as any).label || '')}`) || /评分/.test(`${entry.rule.rule_name}`);
      if (hasPurulence && isPurRule) s += 4;

      // 急性发作/急性加重优先，避免误选哮喘病史/确诊史
      const hasExac = /(急性发作|急性加重|exacerbation)/i.test(segment);
      if (hasExac) {
        const isExacRule = /(加重|发作)/.test(`${entry.rule.rule_name} ${(entry.rule.rule_description || '')}`);
        const isAsthmaHistory = /(哮喘病史|确诊史)/.test(`${entry.rule.rule_name}`);
        if (isExacRule) s += 6;
        if (isAsthmaHistory) s -= 4;
      }
      return s;
    };

    const ranked = this.ruleIndex
      .map(e => ({ e, s: score(e) }))
      .sort((a, b) => b.s - a.s)
      .slice(0, Math.max(k, 8))
      .map(x => x.e.rule);
    return ranked;
  }

  /**
   * 将候选规则转为精简文本列表，供 Prompt 使用
   */
  private prepareCandidateRulesList(candidates: RuleDefinition[]): string {
    let text = '';
    candidates.forEach((rule, idx) => {
      const parsed = this.parseParameterSchema(rule.parameter_schema);
      const params = parsed.parameters.map(p => {
        const opt = p.options && p.options.length ? `, options:[${p.options.join('|')}]` : '';
        const unit = p.unit ? `, unit:${p.unit}` : '';
        return `- ${p.name} (${p.type}${unit}${opt})`;
      }).join('\n');
      const desc = this.cleanDescription(rule.rule_description || rule.rule_name);
      // 避免“规则1/2/3”引导模型返回序号，而是强调 rule_definition_id
      text += `候选 | rule_definition_id:${rule.rule_definition_id} | 名称:${rule.rule_name}\n`;
      text += `描述: ${desc}\n参数:\n${params}\n\n`;
    });
    return text;
  }

  /**
   * 使用所有规则一次性处理段落
   */
  private async processSegmentWithAllRules(segment: string, rulesListText: string): Promise<ProcessResult> {
    if (!this.model) {
      throw new Error("Langchain 模型未初始化");
    }

    // 首选：使用 JsonOutputParser 的链
    if (this.chain) {
      try {
        const params = { rules_list: rulesListText, text_segment: segment } as any;
        console.log('发送给LLM的段落:', segment);
        console.log('规则列表长度:', rulesListText.length);
        const result = await this.chain.invoke(params);
        console.log('LLM返回结果:', result);
        return result;
      } catch (error) {
        console.warn('解析器解析失败，尝试原始输出回退:', error);
      }
    }

    // 回退：直接请求原始输出并手动提取 JSON
    try {
      const prompt = new PromptTemplate({
        template: this.promptTemplate,
        inputVariables: ["rules_list", "text_segment"],
      });
      const formatted = await prompt.format({ rules_list: rulesListText, text_segment: segment });
      const raw = await this.model.invoke(formatted as any);
      const content: string = (raw as any)?.content ?? String(raw ?? '');
      const extracted = this.extractJsonFromText(content);
      if (extracted) {
        console.log('通过回退成功解析JSON');
        return extracted;
      }
      return { success: false, error: '处理失败: 无法从模型输出中提取有效JSON' };
    } catch (error) {
      console.error('回退处理段落失败:', error);
      return { success: false, error: `处理失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * 标准化参数名称以匹配规则模式
   */
  private normalizeParameterNames(
    extractedParams: Record<string, any>, 
    ruleSchema: any
  ): Record<string, any> {
    const normalized: Record<string, any> = {};
    
    if (!ruleSchema?.parameters) {
      console.warn('规则模式不包含参数定义，返回原始参数');
      return extractedParams;
    }
    
    // 创建参数名称映射（不区分大小写）
    const paramMapping = new Map<string, string>();
    ruleSchema.parameters.forEach((param: any) => {
      if (param.name) {
        paramMapping.set(param.name.toLowerCase(), param.name);
        console.log(`添加参数映射: ${param.name.toLowerCase()} -> ${param.name}`);
      }
    });
    
    // 标准化提取的参数
    Object.keys(extractedParams).forEach(extractedName => {
      const normalizedName = paramMapping.get(extractedName.toLowerCase());
      if (normalizedName) {
        normalized[normalizedName] = extractedParams[extractedName];
        console.log(`标准化参数: ${extractedName} -> ${normalizedName} = ${extractedParams[extractedName]}`);
      } else {
        console.warn(`参数 '${extractedName}' 未在规则模式中定义，将被忽略`);
      }
    });
    
    return normalized;
  }

  /**
   * 强制转换参数值类型以匹配模式定义
   */
  private coerceParameterTypes(
    params: Record<string, any>,
    ruleSchema: any
  ): Record<string, any> {
    const coerced: Record<string, any> = {};
    
    if (!ruleSchema?.parameters) {
      console.warn('规则模式不包含参数定义，返回原始参数');
      return params;
    }
    
    ruleSchema.parameters.forEach((paramDef: any) => {
      const paramName = paramDef.name;
      const paramType = paramDef.type;
      const value = params[paramName];
      
      if (value === undefined || value === null) {
        console.log(`参数 '${paramName}' 值为空，跳过类型转换`);
        return; // 跳过未定义的参数
      }
      
      let coercedValue: any;
      let conversionSuccess = true;
      
      switch (paramType) {
        case 'integer':
          coercedValue = Math.round(Number(value));
          if (isNaN(coercedValue)) {
            console.warn(`无法将值 '${value}' 转换为整数类型，参数: ${paramName}`);
            conversionSuccess = false;
          } else {
            console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (integer)`);
          }
          break;
        case 'number':
          coercedValue = Number(value);
          if (isNaN(coercedValue)) {
            console.warn(`无法将值 '${value}' 转换为数字类型，参数: ${paramName}`);
            conversionSuccess = false;
          } else {
            console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (number)`);
          }
          break;
        case 'boolean':
          if (typeof value === 'string') {
            const strValue = value.toLowerCase().trim();
            coercedValue = strValue === 'true' || strValue === '1' || strValue === '是' || strValue === '需要';
          } else {
            coercedValue = Boolean(value);
          }
          console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (boolean)`);
          break;
        case 'string':
          coercedValue = String(value);
          console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (string)`);
          break;
        case 'enum':
          if (paramDef.options && Array.isArray(paramDef.options)) {
            const stringValue = String(value);
            if (paramDef.options.includes(stringValue)) {
              coercedValue = stringValue;
              console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (enum)`);
            } else {
              console.warn(`值 '${value}' 不在允许的枚举选项中 [${paramDef.options.join(', ')}]，参数: ${paramName}`);
              conversionSuccess = false;
            }
          } else {
            coercedValue = String(value);
            console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (enum, no validation)`);
          }
          break;
        default:
          coercedValue = value;
          console.log(`类型转换: ${paramName} = ${value} -> ${coercedValue} (unknown type: ${paramType})`);
      }
      
      if (conversionSuccess) {
        coerced[paramName] = coercedValue;
      }
    });
    
    return coerced;
  }

  /**
   * 验证提取的参数是否符合规则模式
   */
  private validateExtractedParameters(
    params: Record<string, any>,
    ruleSchema: any
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!ruleSchema?.parameters) {
      console.warn('规则模式不包含参数定义，跳过验证');
      return { isValid: true, errors };
    }
    
    const schemaParams = new Set<string>();
    const requiredParams = new Set<string>();
    
    // 收集规则模式中的参数信息
    ruleSchema.parameters.forEach((paramDef: any) => {
      if (paramDef.name) {
        schemaParams.add(paramDef.name);
        if (paramDef.required) {
          requiredParams.add(paramDef.name);
        }
      }
    });
    
    const extractedParams = new Set(Object.keys(params));
    
    // 检查是否有未定义的参数
    extractedParams.forEach(paramName => {
      if (!schemaParams.has(paramName)) {
        errors.push(`参数 '${paramName}' 未在规则模式中定义`);
      }
    });
    
    // 检查必需参数
    requiredParams.forEach(paramName => {
      if (!extractedParams.has(paramName)) {
        errors.push(`缺少必需参数 '${paramName}'`);
      }
    });
    
    // 验证参数值类型
    ruleSchema.parameters.forEach((paramDef: any) => {
      const paramName = paramDef.name;
      const paramType = paramDef.type;
      const value = params[paramName];
      
      if (value === undefined || value === null) {
        return; // 跳过未定义的参数
      }
      
      let typeValid = true;
      
      switch (paramType) {
        case 'integer':
        case 'number':
          if (typeof value !== 'number' || isNaN(value)) {
            typeValid = false;
            errors.push(`参数 '${paramName}' 应该是数字类型，但得到: ${typeof value} (${value})`);
          }
          break;
        case 'boolean':
          if (typeof value !== 'boolean') {
            typeValid = false;
            errors.push(`参数 '${paramName}' 应该是布尔类型，但得到: ${typeof value} (${value})`);
          }
          break;
        case 'string':
          if (typeof value !== 'string') {
            typeValid = false;
            errors.push(`参数 '${paramName}' 应该是字符串类型，但得到: ${typeof value} (${value})`);
          }
          break;
        case 'enum':
          if (paramDef.options && Array.isArray(paramDef.options)) {
            if (!paramDef.options.includes(value)) {
              typeValid = false;
              errors.push(`参数 '${paramName}' 值 '${value}' 不在允许的枚举选项中 [${paramDef.options.join(', ')}]`);
            }
          }
          break;
      }
      
      if (typeValid) {
        console.log(`参数验证通过: ${paramName} = ${value} (${paramType})`);
      }
    });
    
    const isValid = errors.length === 0;
    if (!isValid) {
      console.warn('参数验证失败:', errors);
    } else {
      console.log('所有参数验证通过');
    }
    
    return { isValid, errors };
  }

  /**
   * 调试分析：分析特定文本的匹配情况
   */
  async debugAnalysis(text: string): Promise<{
    segmentAnalysis: any[];
    rulesList: string;
    recommendations: string[];
  }> {
    console.log('开始调试分析文本:', text);
    
    // 准备规则列表
    const rulesListText = this.prepareRulesList();
    
    // 分割文本
    const segments = this.segmentText(text);
    
    const segmentAnalysis: any[] = [];
    
    for (const segment of segments) {
      console.log(`分析段落: "${segment}"`);
      
      // 查找最相关的规则
      const relevantRules = this.findRelevantRules(segment);
      
      segmentAnalysis.push({
        segment,
        relevantRules,
        recommendations: this.generateRecommendations(segment, relevantRules)
      });
    }
    
    return {
      segmentAnalysis,
      rulesList: rulesListText,
      recommendations: this.generateOverallRecommendations(text, segmentAnalysis)
    };
  }

  /**
   * 查找与段落相关的规则
   */
  private findRelevantRules(segment: string): Array<{
    rule: RuleDefinition;
    relevanceScore: number;
    reasons: string[];
  }> {
    const results: Array<{
      rule: RuleDefinition;
      relevanceScore: number;
      reasons: string[];
    }> = [];
    
    const lowerSegment = segment.toLowerCase();
    
    for (const rule of this.ruleDefinitions) {
      let score = 0;
      const reasons: string[] = [];
      
      // 检查规则名称匹配
      if (rule.rule_name) {
        const ruleNameLower = rule.rule_name.toLowerCase();
        if (lowerSegment.includes(ruleNameLower)) {
          score += 10;
          reasons.push(`规则名称匹配: ${rule.rule_name}`);
        }
      }
      
      // 检查规则描述匹配
      if (rule.rule_description) {
        const ruleDescLower = rule.rule_description.toLowerCase();
        if (lowerSegment.includes(ruleDescLower)) {
          score += 8;
          reasons.push(`规则描述匹配: ${rule.rule_description}`);
        }
      }
      
      // 检查关键词匹配
      const keywords = this.extractKeywords(rule);
      for (const keyword of keywords) {
        if (lowerSegment.includes(keyword.toLowerCase())) {
          score += 5;
          reasons.push(`关键词匹配: ${keyword}`);
        }
      }
      
      // 检查单位匹配
      const units = this.extractUnits(rule);
      for (const unit of units) {
        if (lowerSegment.includes(unit.toLowerCase())) {
          score += 3;
          reasons.push(`单位匹配: ${unit}`);
        }
      }
      
      if (score > 0) {
        results.push({
          rule,
          relevanceScore: score,
          reasons
        });
      }
    }
    
    // 按相关性分数排序
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 从规则中提取关键词
   */
  private extractKeywords(rule: RuleDefinition): string[] {
    const keywords: string[] = [];
    
    // 从规则名称提取
    if (rule.rule_name) {
      keywords.push(rule.rule_name);
    }
    
    // 从规则描述提取
    if (rule.rule_description) {
      keywords.push(rule.rule_description);
    }
    
    // 从参数标签提取
    const schema = this.parseParameterSchema(rule.parameter_schema);
    schema.parameters.forEach((param: any) => {
      if (param.label) {
        keywords.push(param.label);
      }
    });
    
    return keywords;
  }

  /**
   * 从规则中提取单位
   */
  private extractUnits(rule: RuleDefinition): string[] {
    const units: string[] = [];
    
    const schema = this.parseParameterSchema(rule.parameter_schema);
    schema.parameters.forEach((param: any) => {
      if (param.unit) {
        units.push(param.unit);
      }
    });
    
    return units;
  }

  /**
   * 生成针对段落的建议
   */
  private generateRecommendations(segment: string, relevantRules: any[]): string[] {
    const recommendations: string[] = [];
    
    if (relevantRules.length === 0) {
      recommendations.push('没有找到相关的规则，建议检查文本是否包含足够的关键词');
      return recommendations;
    }
    
    const topRule = relevantRules[0];
    const secondRule = relevantRules[1];
    
    if (relevantRules.length > 1) {
      const scoreDiff = topRule.relevanceScore - (secondRule?.relevanceScore || 0);
      if (scoreDiff < 3) {
        recommendations.push(`多个规则相关性相近，建议LLM更仔细地区分主题：`);
        recommendations.push(`- ${topRule.rule.rule_name} (分数: ${topRule.relevanceScore})`);
        recommendations.push(`- ${secondRule.rule.rule_name} (分数: ${secondRule.relevanceScore})`);
      }
    }
    
    // 检查是否有主题不匹配的情况
    const segmentLower = segment.toLowerCase();
    if (segmentLower.includes('吸烟') && topRule.rule.rule_name.includes('FEV1')) {
      recommendations.push('⚠️ 检测到吸烟相关文本可能错误匹配到肺功能规则');
    }
    
    if (segmentLower.includes('戒烟') && !topRule.rule.rule_name.includes('吸烟')) {
      recommendations.push('⚠️ 检测到戒烟相关文本，但匹配的规则与吸烟无关');
    }
    
    return recommendations;
  }

  /**
   * 生成整体建议
   */
  private generateOverallRecommendations(_text: string, segmentAnalysis: any[]): string[] {
    const recommendations: string[] = [];
    
    // 分析常见的匹配问题
    const smokingIssues = segmentAnalysis.some(analysis => 
      analysis.segment.toLowerCase().includes('吸烟') && 
      analysis.relevantRules.length > 0 &&
      analysis.relevantRules[0].rule.rule_name.includes('FEV1')
    );
    
    if (smokingIssues) {
      recommendations.push('🔍 发现吸烟相关文本可能错误匹配到肺功能规则的问题');
      recommendations.push('建议：增强提示模板中的主题匹配指导');
    }
    
    // 检查单位混淆
    const unitConfusion = segmentAnalysis.some(analysis => {
      const segment = analysis.segment.toLowerCase();
      return (segment.includes('包年') || segment.includes('包/年')) && 
             analysis.relevantRules.length > 0 &&
             analysis.relevantRules[0].rule.rule_name.includes('FEV1');
    });
    
    if (unitConfusion) {
      recommendations.push('🔍 发现包/年单位可能被错误匹配到百分比单位的问题');
    }
    
    return recommendations;
  }

  /**
   * 分割包含OR关系的段落
   */
  private async splitOrRelationship(segment: string, _rulesListText: string): Promise<{
    success: boolean;
    segments: string[];
    error?: string;
  }> {
    if (!this.model) {
      throw new Error("Langchain 模型未初始化");
    }

    try {
      // 创建专门用于分割OR关系的提示模板
      const splitPrompt = new PromptTemplate({
        template: `你是一个专业的临床研究入排标准解析助手。你的任务是将包含"或"关系的入排标准文本分割为多个独立的标准。

待处理的入排标准文本段落：
{text_segment}

请分析上述入排标准文本，判断它是否包含"或"关系（例如"≥2次中度或≥1次重度"）。如果包含，请将其分割为多个独立的标准。

例如："队列一要求筛选前12个月内发生≥2次中度或≥1次重度COPD急性加重" 应该分为两条：
1. "队列一要求筛选前12个月内发生≥2次中度COPD急性加重"
2. "队列一要求筛选前12个月内发生≥1次重度COPD急性加重"

【输出格式要求】：
你必须严格按照以下JSON格式返回结果：
{{
  "success": true/false,
  "segments": ["分割后的段落1", "分割后的段落2", ...],
  "error": "如果success为false，这里提供错误原因"
}}

【注意事项】：
1. 只有当文本确实包含"或"关系且需要分割时，才将success设为true
2. 分割后的段落应该保持原始语义，只是将"或"关系拆分为独立的条件
3. 如果文本不包含"或"关系，或者虽然包含"或"但不应该分割（例如是术语的一部分），请将success设为false
4. 你的整个回复必须是一个有效的JSON对象，不要添加任何其他内容`,
        inputVariables: ["text_segment"]
      });

      // 创建 JSON 输出解析器
      const parser = new JsonOutputParser<{
        success: boolean;
        segments: string[];
        error?: string;
      }>();

      // 使用 LCEL 构建临时链
      const splitChain = splitPrompt.pipe(this.model).pipe(parser);

      // 调用链
      const result = await splitChain.invoke({
        text_segment: segment
      });

      return result;
    } catch (error) {
      console.error('分割OR关系失败:', error);
      return {
        success: false,
        segments: [],
        error: `分割失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 处理文本
   */
  async processText(
    text: string,
    modelId?: string,
    promptTemplate?: string,
    progressCallback?: (current: number, total: number, success: number, failed: number) => void
  ): Promise<{
    generatedJson: GeneratedCriteriaJson;
    processingDetails: {
      matchedSegments: Array<{
        segment: string;
        rule: RuleDefinition;
        criterion: GeneratedCriterion;
        remark?: string;
      }>;
      failedSegments: Array<{
        segment: string;
        errors: Array<{
          rule: RuleDefinition;
          error: string;
        }>;
      }>;
    };
  }> {
    console.log('开始处理文本...');

    // 如果提供了模型ID，更新模型
    if (modelId) {
      this.setModelId(modelId);
    }

    // 如果提供了提示模板，更新提示模板
    if (promptTemplate) {
      this.setPromptTemplate(promptTemplate);
    }

    // 分割文本为段落
    const baseSegments = this.segmentText(text);

    // 识别“备注/要求”类段落（不作为独立标准），并准备将其挂在“中度急性加重”子句上
    const isRemark = (s: string) => /^(要求|需|须|需要|应当|应)\s*.*(至少|不少于).*(全身性)?糖皮质激素/.test(s.trim());
    // 放宽识别：仅要求出现“中度”，不强制包含“急性加重”
    const isModerateExac = (s: string) => /中度/i.test(s);

    // 先把所有备注段落挑出来，并记录要挂接的目标（最近的“中度急性加重”所在的子句）
    const remarkSegments: Array<{ index: number; text: string }> = [];
    baseSegments.forEach((s, idx) => {
      if (isRemark(s)) remarkSegments.push({ index: idx, text: s.trim() });
    });

    // 计算非备注段落
    const normalBaseSegments: string[] = baseSegments.filter((_, idx) => !remarkSegments.some(r => r.index === idx));

    // 将非备注段落拆为子句，并建立“子句->备注”映射
    const segments: string[] = [];
    const remarkByClause = new Map<string, string>();
    const isSmokingQualifierOr = (s: string) => {
      const t = s.replace(/\s+/g, '');
      return /吸烟/.test(t) && hasOrRelationship(t) && /(当前吸烟者|戒烟者)/.test(t);
    };

    normalBaseSegments.forEach((seg) => {
      if (hasOrRelationship(seg)) {
        // 特判：吸烟者/戒烟者 是对象限定语，不拆成两条，作为备注附着到吸烟史规则
        if (isSmokingQualifierOr(seg)) {
          segments.push(seg);
          // 提取限定语作为备注（尽量取到“的吸烟史”之前的部分）
          const m = seg.match(/^(.*?)(?=的?吸烟史)/);
          const qualifier = (m ? m[1] : seg).trim().replace(/[；;。]$/,'');
          if (qualifier) remarkByClause.set(seg.trim(), qualifier);
          return;
        }

        const clauses = splitOrClauses(seg);
        if (clauses.length > 1) {
          clauses.forEach(c => segments.push(c));
        } else {
          segments.push(seg);
        }
      } else {
        segments.push(seg);
      }
    });

    // 将备注挂到包含“中度”的相关子句上（选择最近的一个子句）
    if (remarkSegments.length > 0) {
      const targetClause = [...segments].reverse().find(c => isModerateExac(c));
      if (targetClause) {
        // 合并多个备注为一条，以顿号连接
        const combined = remarkSegments.map(r => r.text.replace(/^(:?要求|需|须|需要|应当|应)[:：]*/,'').trim()).join('；');
        remarkByClause.set(targetClause.trim(), combined);
      }
    }

    console.log(`已将文本分割为 ${segments.length} 个段落（含 OR 拆分），备注绑定数: ${remarkByClause.size}`);

    // 初始化结果
    const generatedJson: GeneratedCriteriaJson = {
      criteria: [],
      or_groups: []
    };

    // 初始化处理详情
    const processingDetails = {
      matchedSegments: [] as Array<{
        segment: string;
        rule: RuleDefinition;
        criterion: GeneratedCriterion;
      }>,
      failedSegments: [] as Array<{
        segment: string;
        errors: Array<{
          rule: RuleDefinition;
          error: string;
        }>;
      }>
    };

    // 统计成功和失败的数量
    let successCount = 0;
    let failedCount = 0;
    let nextGroupId = 1; // 用于生成OR组ID

    // 将段落按是否为 OR 组进行分组
    // 提取时间范围（月）帮助函数
    const extractMonths = (s: string): number | undefined => {
      const t = s.replace(/\s+/g, '');
      // 年 -> 12 个月
      if (/(前|近|过去|筛选前)(一|1)年/.test(t) || /(一|1)年内/.test(t) || /(近|过去)一年/.test(t)) return 12;
      if (/(前|近|过去|筛选前)12个月/.test(t) || /12个月内/.test(t)) return 12;
      // 通用 N 个月
      const m1 = t.match(/(前|近|过去|筛选前)(\d{1,2})个月/);
      if (m1) return Number(m1[2]);
      const m2 = t.match(/(\d{1,2})个月内/);
      if (m2) return Number(m2[1]);
      // N 周（近似换算为月）：向上取整(周/4)
      const w1 = t.match(/(前|近|过去|筛选前)(\d{1,2})周/);
      if (w1) return Math.max(1, Math.ceil(Number(w1[2]) / 4));
      const w2 = t.match(/(\d{1,2})周内/);
      if (w2) return Math.max(1, Math.ceil(Number(w2[1]) / 4));
      // N 天（近似换算为月）：向上取整(天/30)
      const d1 = t.match(/(前|近|过去|筛选前)(\d{1,3})天/);
      if (d1) return Math.max(1, Math.ceil(Number(d1[2]) / 30));
      const d2 = t.match(/(\d{1,3})天内/);
      if (d2) return Math.max(1, Math.ceil(Number(d2[1]) / 30));
      return undefined;
    };

    // 进一步提取与维持治疗相关的共享参数
    const extractMaintenanceMonths = (s: string): number | undefined => {
      const t = s.replace(/\s+/g, '');
      const m = t.match(/(至少)?连续(\d{1,2})个月/);
      if (m) return Number(m[2]);
      const m2 = t.match(/至少(\d{1,2})个月/);
      if (m2) return Number(m2[1]);
      return undefined;
    };
    const extractStableMonths = (s: string): number | undefined => {
      const t = s.replace(/\s+/g, '');
      const m = t.match(/稳定.*?(\d{1,2})个月/);
      if (m) return Number(m[1]);
      return undefined;
    };

    const segmentGroups: Array<{ raw: string; clauses: string[]; isOr: boolean; dateMonths?: number; maintenanceMonths?: number; stableMonths?: number }> = normalBaseSegments.map(seg => {
      const dateMonths = extractMonths(seg);
      const maintenanceMonths = extractMaintenanceMonths(seg);
      const stableMonths = extractStableMonths(seg);
      if (hasOrRelationship(seg)) {
        const clauses = splitOrClauses(seg);
        return { raw: seg, clauses, isOr: clauses.length > 1, dateMonths, maintenanceMonths, stableMonths };
      }
      return { raw: seg, clauses: [seg], isOr: false, dateMonths, maintenanceMonths, stableMonths };
    });

    // 处理每个段落/组
    const totalSegments = segmentGroups.reduce((acc, g) => acc + g.clauses.length, 0);
    let processedCount = 0;
    for (const group of segmentGroups) {
      const groupCriteriaIndices: number[] = [];
      for (const seg of group.clauses) {
        const segment = seg.trim();
        if (!segment) continue;

        processedCount++;
        console.log(`处理段落 ${processedCount}/${totalSegments}: ${segment.substring(0, 50)}...`);

        // 调用进度回调
        if (progressCallback) {
          progressCallback(processedCount, totalSegments, successCount, failedCount);
        }

        // 召回 Top-K 候选规则并生成精简规则列表
        const candidates = this.getTopKRulesForSegment(segment, 4);
        const rulesListText = this.prepareCandidateRulesList(candidates);

        // 处理段落
        const processResult = await this.processSegmentWithAllRules(segment, rulesListText);
        if (processResult.success && processResult.criterion) {
          // 验证规则ID是否有效
          const ruleId = processResult.criterion.rule_definition_id;

          // 检查规则ID是否存在于当前加载的规则定义中
          let matchedRule = this.ruleDefinitions.find(
            rule => rule.rule_definition_id === ruleId
          );

          // 若不存在，尝试将“小序号”解释为候选序号（1..K），映射为真实 rule_definition_id
          if (!matchedRule && Number.isInteger(ruleId as any)) {
            const idx = Number(ruleId);
            if (idx >= 1 && idx <= candidates.length) {
              const mapped = candidates[idx - 1];
              if (mapped && mapped.rule_definition_id) {
                console.warn(`LLM 返回了候选序号 ${idx}，自动映射为 rule_definition_id=${mapped.rule_definition_id}`);
                processResult.criterion.rule_definition_id = mapped.rule_definition_id as any;
                matchedRule = this.ruleDefinitions.find(r => r.rule_definition_id === mapped.rule_definition_id);
              }
            }
          }

          if (!matchedRule) {
          console.error(`段落 ${processedCount} 匹配了不存在的规则ID: ${ruleId}`);

          // 尝试刷新规则定义，可能是新添加的规则
          try {
            await this.refreshRuleDefinitions();
            console.log(`已刷新规则定义，当前规则数量: ${this.ruleDefinitions.length}`);

            // 再次检查规则ID是否存在
            const refreshedMatchedRule = this.ruleDefinitions.find(
              rule => rule.rule_definition_id === ruleId
            );

            if (!refreshedMatchedRule) {
              // 规则ID仍然不存在，记录错误
              processingDetails.failedSegments.push({
                segment,
                errors: [{
                  rule: { rule_name: '未知规则', rule_definition_id: ruleId } as RuleDefinition,
                  error: `匹配了不存在的规则ID: ${ruleId}（即使刷新规则定义后仍未找到）`
                }]
              });
              failedCount++;
              continue;
            } else {
              // 找到了刷新后的规则
              console.log(`刷新规则定义后找到了规则ID ${ruleId}: ${refreshedMatchedRule.rule_name}`);
              matchedRule = refreshedMatchedRule;
            }
          } catch (refreshError) {
            console.error('刷新规则定义失败:', refreshError);
            processingDetails.failedSegments.push({
              segment,
              errors: [{
                rule: { rule_name: '未知规则', rule_definition_id: ruleId } as RuleDefinition,
                error: `匹配了不存在的规则ID: ${ruleId}，且刷新规则定义失败: ${refreshError instanceof Error ? refreshError.message : String(refreshError)}`
              }]
            });
            failedCount++;
            continue;
          }
        }

        // 解析规则模式
        let ruleSchema;
        try {
          ruleSchema = this.parseParameterSchema(matchedRule.parameter_schema);
          console.log(`成功解析规则模式，规则: ${matchedRule.rule_name}`);
        } catch (schemaError) {
          console.error(`解析规则模式失败，规则: ${matchedRule.rule_name}`, schemaError);
          processingDetails.failedSegments.push({
            segment,
            errors: [{
              rule: matchedRule,
              error: `解析规则模式失败: ${schemaError instanceof Error ? schemaError.message : String(schemaError)}`
            }]
          });
          failedCount++;
          continue;
        }

        // 确保参数值是对象而不是字符串
        let paramValues = processResult.criterion.parameter_values;
        if (typeof paramValues === 'string') {
          try {
            paramValues = JSON.parse(paramValues);
            console.log('成功解析段落的参数值:', paramValues);
          } catch (e) {
            console.error('解析段落参数值字符串失败:', e);
            // 尝试处理可能的格式问题
            const cleanedStr = String(paramValues)
              .replace(/'/g, '"')  // 替换单引号为双引号
              .replace(/(\w+):/g, '"$1":')  // 给键名添加双引号
              .replace(/,\s*}/g, '}');  // 移除尾部逗号

            try {
              paramValues = JSON.parse(cleanedStr);
              console.log('清理后成功解析段落参数值:', paramValues);
            } catch (cleanParseError) {
              console.error('清理后仍然无法解析段落参数值:', cleanParseError);
              paramValues = {}; // 解析失败时使用空对象
            }
          }
        }

        // 应用参数标准化和类型转换
        console.log(`开始处理段落 ${processedCount} 参数...`);
        paramValues = this.normalizeParameterNames(paramValues, ruleSchema);

        // 在类型转换前，补充组级共享参数（如 date_number / Maintenance_duration / Stable_dosage_duration）
        try {
          if (group.dateMonths !== undefined && ruleSchema.parameters?.some((p: any) => (p.name || '').toLowerCase() === 'date_number')) {
            if (paramValues['date_number'] === undefined && paramValues['Date_number'] === undefined && paramValues['DateNumber'] === undefined) {
              paramValues['date_number'] = group.dateMonths;
              console.log(`注入组级共享参数: date_number=${group.dateMonths}`);
            }
          }
          // 激发/舒张等试验有效期：Effective_time_range（筛选前多少个月内有效）
          if (ruleSchema.parameters?.some((p: any) => (p.name || '') === 'Effective_time_range') && paramValues['Effective_time_range'] === undefined) {
            // 优先使用组级解析到的月份
            let months = group.dateMonths;
            try {
              // 如可用，调用同函数解析当前子句
              // @ts-ignore
              if (typeof extractMonths === 'function') {
                // @ts-ignore
                const m = extractMonths(segment);
                if (m !== undefined) months = m;
              }
            } catch {}
            if (months !== undefined) {
              paramValues['Effective_time_range'] = months;
              console.log(`注入试验有效期: Effective_time_range=${months} 月`);
            }
          }
          // 维持治疗时长注入
          if (group.maintenanceMonths !== undefined && ruleSchema.parameters?.some((p: any) => (p.name || '').toLowerCase() === 'maintenance_duration')) {
            if (paramValues['Maintenance_duration'] === undefined) {
              paramValues['Maintenance_duration'] = group.maintenanceMonths;
              console.log(`注入组级共享参数: Maintenance_duration=${group.maintenanceMonths}`);
            }
          }
          // 剂量稳定时长注入
          if (group.stableMonths !== undefined && ruleSchema.parameters?.some((p: any) => (p.name || '').toLowerCase() === 'stable_dosage_duration')) {
            if (paramValues['Stable_dosage_duration'] === undefined) {
              paramValues['Stable_dosage_duration'] = group.stableMonths;
              console.log(`注入组级共享参数: Stable_dosage_duration=${group.stableMonths}`);
            }
          }
          // 维持治疗规则的其他提取
          const isMaintenanceRule = /维持治疗/.test(`${matchedRule.rule_name} ${(matchedRule.rule_description || '')}`);
          if (isMaintenanceRule) {
            const text = segment;
            // 是否每日维持治疗（支持未用药场景）
            const hasDailyParam = ruleSchema.parameters?.some((p: any) => p.name === 'Has_daily_maintenance_therapy');
            if (hasDailyParam && paramValues['Has_daily_maintenance_therapy'] === undefined) {
              const neg = /(未(接受|进行).{0,6}?每日.{0,4}吸入(维持)?治疗|未.{0,4}?维持治疗|未用药|无维持治疗)/.test(text);
              const pos = /(接受|进行).{0,6}?每日.{0,4}吸入(维持)?治疗/.test(text);
              if (neg) paramValues['Has_daily_maintenance_therapy'] = false;
              else if (pos) paramValues['Has_daily_maintenance_therapy'] = true;
            }
            // 剂量强度范围（中-高剂量）
            if (ruleSchema.parameters?.some((p: any) => p.name === 'Dose_intensity_range') && paramValues['Dose_intensity_range'] === undefined) {
              const m = text.match(/(中[-−—~至到]?高)剂量|中-高剂量/);
              if (m) paramValues['Dose_intensity_range'] = '中-高剂量';
            }
            // 给药频次 次/天（例如 每天两次/每日2次）
            if (ruleSchema.parameters?.some((p: any) => p.name === 'Frequency_per_day') && paramValues['Frequency_per_day'] === undefined) {
              const m = text.match(/每[天日].{0,2}?([0-9]+|两)次/);
              if (m) {
                const v = m[1] === '两' ? 2 : Number(m[1]);
                if (!Number.isNaN(v)) paramValues['Frequency_per_day'] = v;
              }
            }
            // ICS当量最小/最大日剂量（μg/天）
            const hasMinDose = ruleSchema.parameters?.some((p: any) => p.name === 'ICS_equiv_min_per_day');
            const hasMaxDose = ruleSchema.parameters?.some((p: any) => p.name === 'ICS_equiv_max_per_day');
            if ((hasMinDose || hasMaxDose) && (paramValues['ICS_equiv_min_per_day'] === undefined || paramValues['ICS_equiv_max_per_day'] === undefined)) {
              // 例如：丙酸氟替卡松≥250 µg每天两次 -> 最小日剂量 = 250 * 2
              const mMin = text.match(/≥\s*(\d{2,5})\s*(μg|ug)[^；;。]*?每[天日].{0,2}?([0-9]+|两)次/i);
              if (mMin && hasMinDose && paramValues['ICS_equiv_min_per_day'] === undefined) {
                const dose = Number(mMin[1]);
                const times = mMin[3] === '两' ? 2 : Number(mMin[3]);
                const perDay = !Number.isNaN(dose) && !Number.isNaN(times) ? dose * times : undefined;
                if (perDay) paramValues['ICS_equiv_min_per_day'] = perDay;
              }
              const mMax = text.match(/不超过[^\d]*(\d{2,5})\s*(μg|ug)\/?[天日]/i);
              if (mMax && hasMaxDose && paramValues['ICS_equiv_max_per_day'] === undefined) {
                const max = Number(mMax[1]);
                if (!Number.isNaN(max)) paramValues['ICS_equiv_max_per_day'] = max;
              }
            }
            // 联合控制药物至少N种
            if (ruleSchema.parameters?.some((p: any) => p.name === 'Controller_count_min') && paramValues['Controller_count_min'] === undefined) {
              const m = text.match(/联合\s*([0-9]+|一|两|二)种/);
              if (m) {
                const map: any = { '一': 1, '二': 2, '两': 2 };
                const v = m[1] in map ? map[m[1]] : Number(m[1]);
                if (!Number.isNaN(v)) paramValues['Controller_count_min'] = v;
              } else if (/联合\s*1种/.test(text)) {
                paramValues['Controller_count_min'] = 1;
              }
            }
            // 控制药物类型（若列举则标记为“任一”）
            if (ruleSchema.parameters?.some((p: any) => p.name === 'Controller_type') && paramValues['Controller_type'] === undefined) {
              if (/LABA|LTRA|LAMA|茶碱/i.test(text)) paramValues['Controller_type'] = '任一';
            }
          }
          
          // 急性发作/加重：最小次数提取（≥N次）
          const hasExac = /(急性发作|急性加重|exacerbation)/i.test(segment);
          const hasMinCountParam = ruleSchema.parameters?.some((p: any) => (p.name || '') === 'Min_count');
          if (hasExac && hasMinCountParam && paramValues['Min_count'] === undefined) {
            const mc = segment.match(/≥\s*(\d{1,3})\s*次/);
            if (mc) {
              const v = Number(mc[1]);
              if (!Number.isNaN(v)) {
                paramValues['Min_count'] = v;
                console.log(`注入急性发作最小次数: ${v} 次`);
              }
            }
          }
          // BMI 注入：当文本包含 BMI 且当前规则为“体重/BMI”类，但未抽取到 BMI 时，解析阈值
          const isWeightOrBMI = /(体重|bmi)/i.test(`${matchedRule.rule_name} ${(matchedRule.rule_description || '')}`);
          const seg = segment;
          if (isWeightOrBMI) {
            const bmiMatch = seg.match(/bmi\s*(≥|>=|＞)?\s*(\d+(?:\.\d+)?)/i) || seg.match(/(\d+(?:\.\d+)?)\s*kg\s*\/\s*m\s*(2|²|㎡)/i);
            if (bmiMatch && paramValues['BMI'] === undefined) {
              const bmiVal = Number(bmiMatch[2] || bmiMatch[1]);
              if (!Number.isNaN(bmiVal)) {
                paramValues['BMI'] = bmiVal;
                console.log(`注入 BMI 阈值: ${bmiVal}`);
              }
            }
            // 性别区分的体重阈值提取
            const femaleMatch = seg.match(/(女性|女).{0,6}?体重\s*(≥|>=|＞)?\s*(\d+(?:\.\d+)?)\s*kg/i);
            const maleMatch = seg.match(/(男性|男).{0,6}?体重\s*(≥|>=|＞)?\s*(\d+(?:\.\d+)?)\s*kg/i);
            if (femaleMatch) {
              const v = Number(femaleMatch[3]);
              if (!Number.isNaN(v)) {
                paramValues['weight_min_female'] = v;
                console.log(`注入女性最小体重: ${v} kg`);
              }
            }
            if (maleMatch) {
              const v = Number(maleMatch[3]);
              if (!Number.isNaN(v)) {
                paramValues['weight_min_male'] = v;
              console.log(`注入男性最小体重: ${v} kg`);
              }
            }
          }
          // 性别枚举注入
          const hasGenderEnum = ruleSchema.parameters?.some((p: any) => (p.name || '').toLowerCase() === 'gender' && p.type === 'enum');
          if (hasGenderEnum && paramValues['gender'] === undefined) {
            const segLower = seg.toLowerCase();
            const wantAny = /(男女不限|性别不限|男或女)/.test(seg);
            const wantMale = /(男性|男)(?!或女)/.test(seg);
            const wantFemale = /(女性|女)(?!或男)/.test(seg);
            const genderParam = ruleSchema.parameters.find((p: any) => (p.name || '').toLowerCase() === 'gender');
            const options: string[] = genderParam?.options || [];
            const pick = (vals: string[]): string | undefined => vals.find(v => options.includes(v));
            let val: string | undefined;
            if (wantAny) {
              val = pick(['男女不限','性别不限','男性或女性']);
            } else if (wantMale && !wantFemale) {
              val = pick(['男性']);
            } else if (wantFemale && !wantMale) {
              val = pick(['女性']);
            }
            if (val) {
              paramValues['gender'] = val;
              console.log(`注入性别要求: ${val}`);
            }
          }
          // HRCT 有效期与受累肺叶注入
          const hasHRCTValidParam = ruleSchema.parameters?.some((p: any) => (p.name || '').toLowerCase() === 'hrct_valid_within_months');
          if (hasHRCTValidParam && paramValues['HRCT_valid_within_months'] === undefined) {
            // 优先使用组级月份；否则从当前句解析
            const months = (typeof extractMonths === 'function' ? extractMonths(seg) : undefined) ?? group.dateMonths;
            if (months !== undefined) {
              paramValues['HRCT_valid_within_months'] = months;
              console.log(`注入 HRCT 有效期: ${months} 月`);
            }
          }
          const hasLobesParam = ruleSchema.parameters?.some((p: any) => (p.name || '').toLowerCase() === 'lobes_affected_min');
          if (hasLobesParam && paramValues['Lobes_affected_min'] === undefined) {
            let lobes: number | undefined;
            // 一个或以上肺叶 / 至少1个肺叶
            if (/一个或以上肺叶|至少\s*1\s*个肺叶/.test(seg)) lobes = 1;
            // 至少N个肺叶 / ≥N个肺叶
            const m2 = seg.match(/(?:至少|≥|>=|＞)\s*(\d+)\s*个肺叶/);
            if (!lobes && m2) lobes = Number(m2[1]);
            // N个肺叶（无限定词）
            if (!lobes) {
              const m3 = seg.match(/(\d+)\s*个肺叶/);
              if (m3) lobes = Number(m3[1]);
            }
            if (lobes !== undefined && !Number.isNaN(lobes)) {
              paramValues['Lobes_affected_min'] = lobes;
              console.log(`注入受累肺叶数下限: ${lobes}`);
            }
          }
          // 痰脓性评分注入
          const isPurulenceRule = /(脓性|purulence|痰评分)/i.test(`${matchedRule.rule_name} ${(matchedRule.rule_description || '')}`);
          if (isPurulenceRule && (paramValues['Purulence_score_min'] === undefined)) {
            const m = seg.match(/评分\s*(?:需)?\s*(?:≥|>=|＞|不低于)\s*(\d{1,2})\s*分/);
            if (m) {
              const v = Number(m[1]);
              if (!Number.isNaN(v)) {
                paramValues['Purulence_score_min'] = v;
                console.log(`注入痰脓性评分下限: ${v} 分`);
              }
            }
          }
        } catch {}

        paramValues = this.coerceParameterTypes(paramValues, ruleSchema);

        // 验证参数
        const validation = this.validateExtractedParameters(paramValues, ruleSchema);
        if (!validation.isValid) {
          console.warn(`段落 ${processedCount} 参数验证失败:`, validation.errors);
          // 记录验证错误但继续处理，因为有些错误可能是可以接受的
          validation.errors.forEach(error => {
            console.warn(`参数验证警告: ${error}`);
          });
        }

        // 如果该段有备注且规则支持 Remark 字段，则注入
        try {
          const matchedRemark = ((): string | undefined => {
            // 访问外部映射需要闭包变量，此处通过日志控制在调用点注入
            return undefined;
          })();
        } catch {}

        const criterionWithOrder = {
          ...processResult.criterion,
          parameter_values: paramValues, // 使用处理后的参数值（下方可能扩展 Remark）
          display_order: generatedJson.criteria.length + 1 // 设置显示顺序
        } as GeneratedCriterion;

        generatedJson.criteria.push(criterionWithOrder);

        // 记录分组内的 criteria 索引（从1开始）
        groupCriteriaIndices.push(generatedJson.criteria.length);

        // 记录匹配成功的详情
        const matchedRemark = remarkByClause.get(segment);
        // 若存在备注映射，写入到 parameter_values.Remark 并显示（后端允许额外字段）
        let remark = remarkByClause.get(segment);
        // 行内备注提取：需要…全身性糖皮质激素/需要住院/急诊
        const inlineRemarks: string[] = [];
        const m1 = segment.match(/需要[^；;。]*?(全身性)?糖皮质激素[^；;。]*/);
        if (m1) inlineRemarks.push(m1[0].trim());
        const m2 = segment.match(/需要[^；;。]*?(住院|急诊)[^；;。]*/);
        if (m2) inlineRemarks.push(m2[0].trim());
        if (inlineRemarks.length) {
          remark = remark ? `${remark}；${inlineRemarks.join('；')}` : inlineRemarks.join('；');
        }
        if (remark) {
          try {
            (criterionWithOrder.parameter_values as any)['Remark'] = remark;
          } catch {}
        }

        // 更新组级共享参数（供同组后续子句继承）
        try {
          if (group.maintenanceMonths === undefined && (criterionWithOrder.parameter_values as any)['Maintenance_duration'] !== undefined) {
            group.maintenanceMonths = Number((criterionWithOrder.parameter_values as any)['Maintenance_duration']);
          }
          if (group.stableMonths === undefined && (criterionWithOrder.parameter_values as any)['Stable_dosage_duration'] !== undefined) {
            group.stableMonths = Number((criterionWithOrder.parameter_values as any)['Stable_dosage_duration']);
          }
        } catch {}

        processingDetails.matchedSegments.push({
          segment,
          rule: matchedRule,
          criterion: criterionWithOrder,
          remark: remark
        });

        successCount++;
        console.log(`段落 ${processedCount} 处理成功，规则: ${matchedRule.rule_name}, 验证状态: ${validation.isValid ? '通过' : '有警告'}`);
      } else {
        // 处理失败，尝试轻量回退：针对 EOS ≥ N 个细胞/μL
        console.log(`段落 ${processedCount} 未找到匹配的规则:`, processResult.error);
        const eosLike = /\beos\b/i.test(segment) && /(个\s*细胞\s*\/[μµ]l|个\s*细胞\s*\/ul)/i.test(segment);
        if (eosLike) {
          // 提取阈值数字
          const numMatch = segment.match(/([≥>=＞]\s*)?(\d{1,5})(?=\s*个\s*细胞\s*\/[μµ]l|\s*个\s*细胞\s*\/ul)/i) || segment.match(/\beos\b[\s\S]*?(\d{1,5})/i);
          const minCount = numMatch ? Number(numMatch[2] || numMatch[1]) : undefined;

          // 寻找嗜酸相关规则
          let eosRule = this.ruleDefinitions.find(r => /嗜酸|eosinophil/i.test(`${r.rule_name} ${r.rule_description || ''} ${(r as any).label || ''}`));

          if (eosRule && eosRule.rule_definition_id) {
            try {
              const ruleSchema = this.parseParameterSchema(eosRule.parameter_schema);
              const values: Record<string, any> = {};
              // 优先匹配 Min_count 参数名
              const minParam = ruleSchema.parameters.find((p: any) => /min.*count/i.test(p.name) || p.name === 'Min_count');
              if (minParam && typeof minCount === 'number' && !Number.isNaN(minCount)) {
                values[minParam.name] = minCount;
              }

              const criterionWithOrder = {
                rule_definition_id: eosRule.rule_definition_id,
                parameter_values: values,
                display_order: generatedJson.criteria.length + 1
              };

              generatedJson.criteria.push(criterionWithOrder);
              groupCriteriaIndices.push(generatedJson.criteria.length);
              processingDetails.matchedSegments.push({ segment, rule: eosRule, criterion: criterionWithOrder });
              successCount++;
              console.log(`段落 ${processedCount} 通过 EOS 回退规则匹配成功，规则: ${eosRule.rule_name}`);
              continue; // 进入下一子句
            } catch (fbErr) {
              console.warn('EOS 回退处理失败:', fbErr);
            }
          }
        }

        // 回退：针对 FEV1 百分比阈值（优先匹配 Pre-BD FEV1）
        const fev1Like = /\bfev1\b/i.test(segment) || /一秒用力呼气量/.test(segment);
        const hasPercent = /\d+\s*%/.test(segment);
        if (fev1Like && hasPercent) {
          try {
            // 提取最小和最大百分比
            const minM = segment.match(/(≥|>=|＞)\s*(\d{1,3})\s*%/);
            const maxM = segment.match(/(≤|<=|＜)\s*(\d{1,3})\s*%/);
            const numbers = Array.from(segment.matchAll(/(\d{1,3})\s*%/g)).map(m => Number(m[1]));
            const minVal = minM ? Number(minM[2]) : (numbers.length ? Math.min(...numbers) : undefined);
            const maxVal = maxM ? Number(maxM[2]) : (numbers.length ? Math.max(...numbers) : undefined);

            // 找到 FEV1 相关规则（当前库：Pre-BD）
            let fev1Rule = this.ruleDefinitions.find(r => /fev1/i.test(`${r.rule_name} ${r.rule_description || ''} ${(r as any).label || ''}`));
            if (fev1Rule && fev1Rule.rule_definition_id) {
              const ruleSchema = this.parseParameterSchema(fev1Rule.parameter_schema);
              const values: Record<string, any> = {};

              const pMin = ruleSchema.parameters.find((p: any) => /pre.*fev1.*min/i.test(p.name) || p.name === 'Pre_BD_FEV1_min');
              const pMax = ruleSchema.parameters.find((p: any) => /pre.*fev1.*max/i.test(p.name) || p.name === 'Pre_BD_FEV1_max');
              if (pMin && typeof minVal === 'number' && !Number.isNaN(minVal)) values[pMin.name] = minVal;
              if (pMax && typeof maxVal === 'number' && !Number.isNaN(maxVal)) values[pMax.name] = maxVal;

              // 访视与时间点提取
              const visitMatch = segment.match(/访视\s*([0-9]+[a-zA-Z]?)/);
              if (visitMatch && ruleSchema.parameters.find((p: any) => p.name === 'Visit_label')) {
                values['Visit_label'] = `访视${visitMatch[1]}`;
              }
              // 时间点（给药前 / 支气管舒张剂前）
              if (/给药前/.test(segment) && ruleSchema.parameters.find((p: any) => p.name === 'Timepoint_label')) {
                values['Timepoint_label'] = '给药前';
              } else if (/(支气管舒张剂前|pre-?bd)/i.test(segment) && ruleSchema.parameters.find((p: any) => p.name === 'Timepoint_label')) {
                values['Timepoint_label'] = '支气管舒张剂前(Pre-BD)';
              }
              // 相对偏差 ±N% 与参考访视
              const devM = segment.match(/±\s*(\d{1,2})\s*%/);
              const refVisitM = segment.match(/访视\s*([0-9]+[a-zA-Z]?)/);
              if (devM && ruleSchema.parameters.find((p: any) => p.name === 'Allowed_deviation_percent')) {
                values['Allowed_deviation_percent'] = Number(devM[1]);
              }
              // 若句子指明“在访视1…范围内”，尝试参考访视
              const refContext = segment.match(/在访视\s*([0-9]+[a-zA-Z]?)/);
              if (refContext && ruleSchema.parameters.find((p: any) => p.name === 'Reference_visit_label')) {
                values['Reference_visit_label'] = `访视${refContext[1]}`;
              }
              if (/支气管舒张剂前|pre-?bd/i.test(segment) && ruleSchema.parameters.find((p: any) => p.name === 'Reference_timepoint_label')) {
                values['Reference_timepoint_label'] = '支气管舒张剂前(Pre-BD)';
              }

              const criterionWithOrder = {
                rule_definition_id: fev1Rule.rule_definition_id,
                parameter_values: values,
                display_order: generatedJson.criteria.length + 1
              };
              generatedJson.criteria.push(criterionWithOrder);
              groupCriteriaIndices.push(generatedJson.criteria.length);
              processingDetails.matchedSegments.push({ segment, rule: fev1Rule, criterion: criterionWithOrder });
              successCount++;
              console.log(`段落 ${processedCount} 通过 FEV1 回退规则匹配成功，规则: ${fev1Rule.rule_name}`);
              continue;
            }
          } catch (fevErr) {
            console.warn('FEV1 回退处理失败:', fevErr);
          }
        }

        // 回退：诊断/病史（哮喘/GINA），优先匹配“哮喘病史”，否则“确诊史”
        const hasDiagnosis = /(确诊|诊断)/.test(segment) || /(gina|asthma|哮喘)/i.test(segment);
        const monthsMatch = segment.match(/(至少|≥|>=|＞)?\s*(\d{1,3})\s*个月/);
        if (hasDiagnosis) {
          try {
            // 找到最合适的规则
            let diagRule = this.ruleDefinitions.find(r => /哮喘/.test(`${r.rule_name}`) && /病史/.test(`${r.rule_name}`));
            if (!diagRule) {
              diagRule = this.ruleDefinitions.find(r => /确诊/.test(`${r.rule_name}`));
            }
            if (diagRule && diagRule.rule_definition_id) {
              const ruleSchema = this.parseParameterSchema(diagRule.parameter_schema);
              const values: Record<string, any> = {};
              const monthVal = monthsMatch ? Number(monthsMatch[2]) : undefined;
              if (!Number.isNaN(monthVal as number) && monthVal !== undefined) {
                const monthParam = ruleSchema.parameters.find((p: any) => /diagnose.*month|asthma.*month/i.test(p.name) || p.name === 'diagnose_month' || p.name === 'asthma_diagnose_month');
                if (monthParam) values[monthParam.name] = monthVal;
              }

              const criterionWithOrder = {
                rule_definition_id: diagRule.rule_definition_id,
                parameter_values: values,
                display_order: generatedJson.criteria.length + 1
              };
              generatedJson.criteria.push(criterionWithOrder);
              groupCriteriaIndices.push(generatedJson.criteria.length);
              processingDetails.matchedSegments.push({ segment, rule: diagRule, criterion: criterionWithOrder });
              successCount++;
              console.log(`段落 ${processedCount} 通过 诊断 回退匹配成功，规则: ${diagRule.rule_name}`);
              continue;
            }
          } catch (dErr) {
            console.warn('诊断回退处理失败:', dErr);
          }
        }

        // 记录失败
        processingDetails.failedSegments.push({
          segment,
          errors: [{
            rule: { rule_name: '所有规则' } as RuleDefinition,
            error: processResult.error || '未找到匹配的规则'
          }]
        });
        failedCount++;
      }

      // 结束当前组内的子句循环
      }

      // 如果这是一个 OR 组且匹配成功数量 ≥ 2，则记录 OR 关系
      if (group.isOr && groupCriteriaIndices.length >= 2) {
        generatedJson.or_groups.push({
          group_id: nextGroupId++,
          criteria_ids: groupCriteriaIndices,
          operator: 'OR'
        });
      }
    }

    console.log(`处理完成，成功: ${successCount}，失败: ${failedCount}`);
    console.log(`生成的JSON:`, generatedJson);

    return {
      generatedJson,
      processingDetails
    };
  }
}
