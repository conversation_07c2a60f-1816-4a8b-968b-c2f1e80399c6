import { invoke } from '@tauri-apps/api/core';

export interface AutoCriteriaHistory {
  history_id?: number;
  created_at?: string;
  input_text: string;
  segmented_text?: string;
  model_id?: string;
  prompt_template?: string;
  generated_json?: string;
  processing_details?: string;
  success_count?: number;
  failed_count?: number;
  notes?: string;
}

export interface AutoCriteriaHistoryQuery {
  limit?: number;
  offset?: number;
  keyword?: string;
}

class AutoCriteriaHistoryService {
  private dbPath = '/Users/<USER>/我的文档/sqlite/peckbyte.db';

  async init(): Promise<boolean> {
    return await invoke('init_auto_criteria_history_tables', { dbPath: this.dbPath });
  }

  async save(data: AutoCriteriaHistory): Promise<number> {
    const request = { ...data };
    return await invoke('save_auto_criteria_history', { dbPath: this.dbPath, request });
  }

  async list(query: AutoCriteriaHistoryQuery = {}): Promise<AutoCriteriaHistory[]> {
    return await invoke('get_auto_criteria_histories', { dbPath: this.dbPath, query });
  }

  async get(historyId: number): Promise<AutoCriteriaHistory | null> {
    return await invoke('get_auto_criteria_history', { dbPath: this.dbPath, historyId });
  }

  async remove(historyId: number): Promise<boolean> {
    return await invoke('delete_auto_criteria_history', { dbPath: this.dbPath, historyId });
  }
}

export const autoCriteriaHistoryService = new AutoCriteriaHistoryService();

