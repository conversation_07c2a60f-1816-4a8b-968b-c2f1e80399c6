# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type check
npm run check

# Watch mode type checking
npm run check:watch
```

### Tauri Development
```bash
# Start Tauri development mode (opens desktop app)
npm run tauri dev

# Build Tauri application
npm run tauri build

# Tauri commands
npm run tauri
```

### Database Operations
- SQLite database is automatically created at: `/Users/<USER>/我的文档/sqlite/peckbyte.db`
- MongoDB configuration is managed through the app's settings interface
- Database tables are initialized automatically on first run

### CSS Processing
- PostCSS configuration is available at `postcss.config.js`
- Tailwind CSS is used for styling with autoprefixer

## Architecture Overview

This is a clinical research project management desktop application built with:
- **Frontend**: SvelteKit + TypeScript + Tailwind CSS
- **Backend**: Rust + Tauri
- **Databases**: SQLite (local) + MongoDB (cloud config)
- **AI Integration**: Langchain.js + OpenAI API

### Backend Architecture (Rust/Tauri)
The backend follows a layered architecture:

- **Commands Layer** (`src-tauri/src/commands/`): Tauri commands exposed to frontend
- **Services Layer** (`src-tauri/src/services/`): Business logic implementation  
- **Repositories Layer** (`src-tauri/src/repositories/`): Data access abstraction
- **Models Layer** (`src-tauri/src/models/`): Data structures and types

Key files:
- `src-tauri/src/lib.rs`: Main library with all Tauri command registrations and app setup
- `src-tauri/src/app.rs`: Application initialization and database setup
- `src-tauri/src/db.rs`: Database connection management with SQLite and MongoDB support
- `src-tauri/src/error.rs`: Unified error handling
- `src-tauri/src/response.rs`: Standard API response formats
- `src-tauri/src/config.rs`: Application configuration management

Key command modules:
- `project_commands.rs`: Project management operations
- `config_commands.rs`: System configuration management
- `rule_designer_commands.rs`: Clinical trial rule definitions
- `dashboard_commands.rs`: Analytics and reporting
- `staff_commands.rs`: Personnel management
- `lighthouse_commands.rs`: External API integrations

### Frontend Architecture (SvelteKit)
- **Routes** (`src/routes/`): File-based routing with SvelteKit conventions
- **Components** (`src/lib/components/`): Reusable UI components organized by feature
- **Services** (`src/lib/services/`): Frontend service layer that calls Tauri commands
- **Stores** (`src/lib/stores/`): Svelte reactive state management
- **Utils** (`src/lib/utils/`): Shared utilities and type definitions

Key frontend services:
- `projectService.ts`: Project management operations
- `configService.ts`: System configuration management
- `ruleDesignerService.ts`: Rule definition and criteria management
- `dashboardService.ts`: Data visualization and analytics
- `staffService.ts`: Personnel management
- `notionService.ts`: Notion API integration
- `lighthouseApiService.ts`: External system integration

Key frontend stores:
- `settings.ts`: Application settings and preferences
- `dashboardStores.ts`: Dashboard data state management
- `filterStore.ts`: UI filter state management
- `cardStore.ts`: Card-based UI state management

### Data Storage Strategy
- **SQLite**: Core business data (projects, staff, rules, criteria, recruitment policies)
- **MongoDB**: System configuration and sensitive settings (API keys)
- **Browser Storage**: UI preferences via Svelte stores

### Menu System
The application includes a comprehensive menu system defined in `lib.rs`:
- **Project Management**: Dashboard, project list, rule definitions, auto criteria
- **Tools**: Inspiration notes, Lighthouse users, referrer management
- **System Settings**: Configuration, dictionary management, staff management
- **Edit**: Standard text editing operations with shortcuts

## Key Development Patterns

### Frontend-Backend Communication
- Frontend calls backend via `@tauri-apps/api/core` `invoke()` function
- All backend functions are registered as Tauri commands in `lib.rs`
- Use `camelCase` in frontend payloads, `snake_case` in Rust structs
- Watch for `#[serde(flatten)]` in Rust - requires flattened JSON structure from frontend

### Error Handling
- Backend: Use `Result<T, AppError>` pattern with unified error types
- Frontend: Try-catch with detailed error logging in service layer
- All errors flow through standardized response format

### Database Access
- Use repository pattern for data access abstraction
- Support both SQLite and MongoDB through unified interfaces
- All database operations should be transactional where appropriate

### AI Integration
- Langchain.js integration for intelligent features
- OpenAI API for content generation and classification
- Configuration managed through secure settings system

## Important Conventions

### Rust Development
- Use `#[tauri::command]` macro for all frontend-callable functions
- Register all commands in `lib.rs` 
- Follow repository pattern for data access
- Use unified error handling via `AppError` type
- Add logging with `info!`, `debug!`, `warn!`, `error!` macros

### Frontend Development  
- Use TypeScript for type safety
- Follow SvelteKit file routing conventions
- Use Tailwind CSS for styling (avoid custom CSS)
- Organize components by feature in `lib/components/`
- Use service layer pattern for backend communication
- Handle loading states and errors consistently

### Component Development
- Use `{#key}` blocks to force re-renders when data structure stays same but content changes
- For complex objects use `JSON.stringify()` in key expressions
- Default filter states should be "all selected" not "none selected" for better UX

### Common Debugging
- Add `console.log` in frontend services to trace payloads
- Add `info!()` logs in Rust command entry points  
- Check browser dev tools for network request payloads
- Verify JSON structure matches Rust struct expectations, especially with `#[serde(flatten)]`
- Use the Tauri devtools for debugging backend issues
- Check SQLite database using the built-in dictionary explorer

### File Structure Maintenance
When adding new files, maintain the project structure documentation in relevant files. The PostCSS configuration at `postcss.config.js` should be updated if CSS processing requirements change.