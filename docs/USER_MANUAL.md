# 临床研究项目管理系统 - 用户使用手册

本手册提供系统各功能模块的详细使用指南。

## 📚 目录

1. [项目管理](#1-项目管理)
2. [入排标准规则引擎](#2-入排标准规则引擎)
3. [人员管理](#3-人员管理)
4. [数据字典管理](#4-数据字典管理)
5. [智能笔记系统](#5-智能笔记系统)
6. [数据分析仪表盘](#6-数据分析仪表盘)
7. [系统配置管理](#7-系统配置管理)

## 1. 项目管理

### 功能概述
项目管理模块是系统的核心功能，提供完整的临床研究项目生命周期管理。用户可以创建、查看、编辑和删除临床研究项目，并管理项目相关的详细信息，包括申办方、研究药物、人员分配和补贴信息。

### 主要功能特性

**📋 项目基本信息管理**
- **项目创建**: 支持从模板创建或全新创建项目
- **项目信息维护**: 项目名称、简称、编号、研究阶段、状态等
- **项目状态跟踪**: 准备中、在研、已结束、暂停等状态管理
- **招募状态管理**: 招募中、招募完成、暂停招募等状态

**🏢 申办方管理**
- **多申办方支持**: 单个项目可关联多个申办方
- **申办方信息维护**: 申办方基本信息和联系方式
- **申办方搜索**: 支持申办方快速搜索和选择

**💊 研究药物管理**
- **药物信息录入**: 研究药物的详细信息
- **药物分组管理**: 支持药物分组和比例配置
- **药物关系管理**: 药物间的关联关系

**👥 人员角色分配**
- **研究人员管理**: 项目团队成员的角色分配
- **权限控制**: 基于角色的权限管理
- **PI 标识**: 主要研究者 (PI) 特殊标识
- **批量人员操作**: 支持批量添加和管理人员

**💰 补贴方案管理**
- **补贴项目配置**: 各类补贴项目的单价和数量
- **补贴方案设计**: 将多个补贴项目组合成方案
- **费用计算**: 自动计算总费用和分项费用
- **补贴类型管理**: 支持多种补贴类型和单位

### 用户操作指南

**创建新项目**
1. 点击"新建项目"按钮
2. 选择项目模板（可选）
3. 填写项目基本信息
4. 配置申办方信息
5. 添加研究药物
6. 分配研究人员
7. 设置补贴方案
8. 保存项目

**编辑现有项目**
1. 在项目列表中找到目标项目
2. 点击"编辑"按钮进入编辑模式
3. 在各个标签页中修改相应信息
4. 保存更改

**项目文件管理**
- **文件夹访问**: 点击"文件夹"按钮直接打开项目目录
- **文件组织**: 系统自动为每个项目创建文件夹结构
- **文档管理**: 支持项目相关文档的存储和管理

## 2. 入排标准规则引擎

### 功能概述
入排标准规则引擎是系统的智能化核心功能之一，专门用于定义、管理和应用临床研究项目的入选标准和排除标准。该模块基于预定义的规则模板构建，支持参数化配置，并提供基于模板的自动生成功能。

### 主要功能特性

**📝 规则模板管理**
- **规则定义**: 创建和管理可复用的入排标准规则模板
- **参数化设计**: 支持 JSON Schema 定义的参数化规则
- **规则分类**: 按医学领域或研究类型对规则进行分类
- **版本控制**: 规则模板的版本管理和历史追踪

**⚙️ 项目标准配置**
- **标准应用**: 将规则模板应用到具体项目
- **参数定制**: 为每个项目定制规则参数
- **逻辑关系**: 支持 AND/OR 逻辑关系配置
- **优先级管理**: 设置标准的显示顺序和重要性

**🔄 批量操作功能**
- **JSON 导入导出**: 支持 JSON 格式的批量标准导入导出
- **模板应用**: 快速应用预设的标准模板
- **批量编辑**: 同时修改多个标准的参数
- **批量删除**: 支持选择性批量删除功能

**🤖 AI 智能生成**
- **文本解析**: 基于自然语言描述自动生成入排标准
- **规则匹配**: 智能匹配现有规则模板
- **参数提取**: 自动提取和填充规则参数
- **结果优化**: 对生成结果进行智能优化和建议

### 用户操作指南

**创建规则模板**
1. 进入"规则定义管理"页面
2. 点击"新建规则"按钮
3. 填写规则基本信息（名称、描述、分类）
4. 定义参数 Schema（JSON 格式）
5. 保存规则模板

**配置项目标准**
1. 进入项目详情页面
2. 点击"配置标准"按钮
3. 选择入选标准或排除标准类型
4. 从规则库中选择适用的规则
5. 配置具体参数值
6. 设置逻辑关系（如需要）
7. 保存配置

**JSON 批量导入**
1. 准备符合格式的 JSON 文件
2. 在项目标准配置页面点击"导入"
3. 选择 JSON 文件并上传
4. 预览导入内容
5. 确认导入

**AI 自动生成**
1. 在项目标准配置页面点击"AI 生成"
2. 输入项目描述或标准要求文本
3. 选择生成模式（入选/排除/全部）
4. 等待 AI 处理并生成结果
5. 审核和调整生成的标准
6. 确认应用到项目

## 3. 人员管理

### 功能概述
人员管理模块提供全面的研究人员信息管理功能，包括员工或项目团队成员的个人详细信息、角色分配和其他管理数据。该模块是项目管理的重要支撑，为项目人员分配提供基础数据。

### 主要功能特性

**👤 人员档案管理**
- **基本信息维护**: 姓名、性别、生日、联系方式等
- **职位信息**: 职位、部门、组织架构关系
- **PI 标识**: 主要研究者 (Principal Investigator) 特殊标识
- **档案完整性**: 确保人员信息的完整性和准确性

**🔍 智能搜索功能**
- **多条件搜索**: 支持按姓名、性别、职位、组织等条件搜索
- **模糊匹配**: 支持姓名和组织的模糊搜索
- **快速筛选**: 提供常用筛选条件的快速访问
- **搜索结果排序**: 支持多种排序方式

**🏥 组织架构管理**
- **部门管理**: 维护组织的部门结构
- **职位体系**: 管理不同级别的职位信息
- **权限关联**: 职位与系统权限的关联管理
- **层级关系**: 支持复杂的组织层级关系

### 用户操作指南

**添加新员工**
1. 进入"人员管理"页面
2. 点击"新增员工"按钮
3. 填写基本信息（姓名、性别、生日）
4. 填写联系信息（电话、邮箱）
5. 选择职位和组织
6. 设置 PI 标识（如适用）
7. 保存员工信息

**编辑员工信息**
1. 在员工列表中找到目标员工
2. 点击"编辑"按钮
3. 修改相应信息
4. 保存更改

**搜索和筛选员工**
1. 使用搜索框输入关键词
2. 选择筛选条件（性别、职位、组织等）
3. 查看搜索结果
4. 点击员工查看详细信息

## 4. 数据字典管理

### 功能概述
数据字典管理模块负责管理和维护存储在本地 SQLite 数据库中的各种通用字典数据。这些字典可用于填充下拉菜单、提供分类选项或存储其他键值类型的数据，为整个系统提供标准化的基础数据支持。

### 主要功能特性

**📚 字典分类管理**
- **字典创建**: 创建新的字典分类
- **元数据管理**: 字典名称、描述、类型等元信息
- **版本控制**: 字典的版本管理和更新追踪
- **状态管理**: 字典的启用/禁用状态控制

**🏷️ 字典项管理**
- **键值对管理**: 管理字典中的具体键值对数据
- **项目描述**: 为每个字典项添加详细描述
- **状态控制**: 字典项的激活/停用状态
- **批量操作**: 支持字典项的批量导入导出

**🔗 标签系统**
- **标签创建**: 为字典创建分类标签
- **标签关联**: 字典与标签的多对多关联
- **标签筛选**: 基于标签的字典快速筛选
- **标签管理**: 标签的增删改查操作

### 用户操作指南

**创建新字典**
1. 进入"数据字典管理"页面
2. 点击"新建字典"按钮
3. 填写字典基本信息
4. 选择字典类型
5. 添加相关标签
6. 保存字典

**管理字典项**
1. 选择目标字典
2. 进入字典详情页面
3. 点击"添加项目"按钮
4. 填写键、值和描述
5. 设置项目状态
6. 保存字典项

**数据迁移**
1. 进入字典迁移页面
2. 选择源数据格式
3. 上传数据文件
4. 预览迁移内容
5. 执行迁移操作

## 5. 智能笔记系统

### 功能概述
智能笔记系统为用户提供了一个记录、管理和智能处理灵感想法的空间。该系统集成了 Langchain.js 框架，提供 AI 驱动的功能，如文本分类、摘要或其他基于大型语言模型的处理，并能将分类后的笔记同步到 Notion 工作区。

### 主要功能特性

**✍️ 笔记编辑功能**
- **富文本编辑**: 支持多种文本格式的笔记编辑
- **实时保存**: 自动保存编辑内容，防止数据丢失
- **批量处理**: 支持批量笔记的创建和管理
- **分隔符支持**: 使用自定义分隔符批量处理多条笔记

**🤖 AI 智能分类**
- **自动分类**: 基于 Langchain.js 的智能内容分类
- **多模型支持**: 支持不同的 AI 模型进行分类
- **分类准确性**: 高精度的内容识别和分类
- **自定义分类**: 支持用户自定义分类规则

**🔄 Notion 集成**
- **自动同步**: 分类后的笔记自动同步到 Notion 数据库
- **双向同步**: 支持从 Notion 获取已有笔记
- **数据库管理**: 管理 Notion 中的笔记数据库
- **实时更新**: 实时显示同步状态和结果

**📊 笔记管理**
- **分类展示**: 按分类展示笔记内容
- **搜索功能**: 快速搜索和定位笔记
- **标签系统**: 为笔记添加标签进行组织
- **历史记录**: 查看笔记的编辑历史

### 用户操作指南

**创建和分类笔记**
1. 进入"智能笔记"页面
2. 在文本框中输入笔记内容
3. 选择单条或批量模式
4. 点击"AI 分类"按钮
5. 等待 AI 处理并查看分类结果
6. 确认分类并同步到 Notion

**批量处理笔记**
1. 选择批量模式
2. 设置分隔符（如换行符、特殊字符等）
3. 输入多条笔记内容
4. 执行批量分类
5. 逐条确认分类结果

**管理 Notion 同步**
1. 配置 Notion API 密钥
2. 设置目标数据库 ID
3. 测试连接状态
4. 执行同步操作
5. 查看同步结果

## 6. 数据分析仪表盘

### 功能概述
数据分析仪表盘为用户提供项目数据的可视化概览，包括关键指标、统计图表和项目状态的快速访问。通过多维度的数据展示，帮助管理者快速了解项目整体情况和重要信息。

### 主要功能特性

**📊 项目统计分析**
- **项目状态分布**: 在研、已结束、准备中等状态的项目数量统计
- **疾病类型分析**: 按疾病类型统计项目分布情况
- **研究阶段分析**: I期、II期、III期等研究阶段的项目分布
- **招募状态统计**: 招募中、招募完成等状态的统计分析

**👥 人员分析**
- **人员分配统计**: 各项目的人员分配情况
- **角色分布分析**: PI、研究员、协调员等角色的分布
- **工作量分析**: 人员在不同项目中的工作量统计
- **组织架构分析**: 按部门和组织的人员分布

**💰 财务分析**
- **补贴统计**: 各类补贴的总额和分布
- **费用分析**: 项目成本和预算分析
- **申办方分析**: 不同申办方的项目投入统计
- **财务趋势**: 财务数据的时间趋势分析

**📈 趋势分析**
- **项目增长趋势**: 新增项目的时间趋势
- **完成率分析**: 项目完成情况的趋势分析
- **招募进度**: 患者招募进度的时间序列分析
- **绩效指标**: 关键绩效指标的趋势变化

### 用户操作指南

**查看仪表盘概览**
1. 进入"数据分析仪表盘"页面
2. 查看各类统计卡片
3. 点击卡片查看详细信息
4. 使用筛选器调整数据范围

**分析项目数据**
1. 选择特定的分析维度
2. 设置时间范围
3. 查看图表和统计结果
4. 导出分析报告

**自定义仪表盘**
1. 选择要显示的指标
2. 调整图表类型和布局
3. 保存个性化配置
4. 分享仪表盘视图

## 7. 系统配置管理

### 功能概述
系统配置管理模块允许用户管理各种应用程序级别的设置，包括外部服务的 API 密钥（如 OpenAI、Notion、Lighthouse）、应用程序行为偏好设置，以及外部系统的连接详细信息。

### 主要功能特性

**🔑 API 密钥管理**
- **OpenAI 配置**: OpenAI API 密钥和模型参数设置
- **Notion 集成**: Notion API 密钥和数据库配置
- **Lighthouse 连接**: Lighthouse 服务器连接参数
- **安全存储**: 敏感信息的加密存储和管理

**🌐 外部服务配置**
- **服务器地址**: 各种外部服务的服务器地址配置
- **连接参数**: 端口、协议、认证等连接参数
- **超时设置**: 网络请求的超时和重试配置
- **连接测试**: 测试外部服务的连接状态

**🎨 界面偏好设置**
- **主题配置**: 明暗主题切换
- **语言设置**: 界面语言选择
- **布局偏好**: 界面布局和显示偏好
- **通知设置**: 系统通知的开关和配置

**📁 路径配置**
- **数据库路径**: SQLite 数据库文件路径
- **项目路径**: 临床研究项目文件夹路径
- **导出路径**: 数据导出的默认路径
- **备份路径**: 数据备份的存储路径

### 用户操作指南

**配置 API 密钥**
1. 打开系统设置对话框
2. 进入"API 配置"选项卡
3. 输入相应的 API 密钥
4. 测试连接状态
5. 保存配置

**设置外部服务**
1. 选择要配置的外部服务
2. 填写服务器地址和端口
3. 配置认证信息
4. 测试连接
5. 保存设置

**个性化界面**
1. 进入"界面设置"选项卡
2. 选择主题和语言
3. 调整布局偏好
4. 配置通知选项
5. 应用设置

---

**© 2024 临床研究项目管理系统用户手册. 最后更新: 2024年**