use crate::models::sqlite_dictionary::{Dictionary, DictionaryItem, DictionaryQuery};
use crate::services::sqlite_service::SqliteService;
// 导入
use log::{error, info};
use rusqlite::params;
use std::error::Error;
use std::sync::Arc;

/// SQLite 字典服务
pub struct SqliteDictionaryService {
    sqlite_service: Arc<SqliteService>,
}

impl SqliteDictionaryService {
    /// 创建新的 SQLite 字典服务
    pub fn new(sqlite_service: Arc<SqliteService>) -> Self {
        Self { sqlite_service }
    }

    /// 获取所有字典
    pub fn get_dictionaries(
        &self,
        query: Option<&DictionaryQuery>,
    ) -> Result<Vec<Dictionary>, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        let mut sql = String::from(
            "SELECT d.id, d.mongo_oid, d.name, d.description, d.type, d.created_at, d.updated_at, d.version
             FROM dictionaries d"
        );

        let mut params_values: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(q) = query {
            let mut where_clauses = Vec::new();

            if let Some(name) = &q.name {
                where_clauses.push("d.name LIKE ?");
                params_values.push(Box::new(format!("%{}%", name)));
            }

            if let Some(type_) = &q.type_ {
                where_clauses.push("d.type = ?");
                params_values.push(Box::new(type_.clone()));
            }

            if let Some(tags) = &q.tags {
                sql.push_str(" JOIN dictionary_tags dt ON d.id = dt.dictionary_id JOIN tags t ON dt.tag_id = t.tag_id");
                where_clauses.push("t.tag_name = ?");
                // 只使用第一个标签进行查询
                if let Some(tag) = tags.first() {
                    params_values.push(Box::new(tag.clone()));
                }
            }

            if !where_clauses.is_empty() {
                sql.push_str(" WHERE ");
                sql.push_str(&where_clauses.join(" AND "));
            }
        }

        sql.push_str(" ORDER BY d.name");

        let mut stmt = conn.prepare(&sql)?;

        let params_slice: Vec<&dyn rusqlite::ToSql> =
            params_values.iter().map(|p| p.as_ref()).collect();

        let dict_iter = stmt.query_map(params_slice.as_slice(), |row| {
            Ok(Dictionary {
                id: Some(row.get(0)?),
                mongo_oid: row.get(1)?,
                name: row.get(2)?,
                description: row.get(3)?,
                type_: row.get(4)?,
                created_at: row.get(5)?,
                updated_at: row.get(6)?,
                version: row.get(7)?,
                tags: None,
                items: None,
            })
        })?;

        let mut dicts = Vec::new();
        for dict_result in dict_iter {
            let mut dict = dict_result?;
            // 获取标签
            dict.tags = Some(self.get_dictionary_tags(dict.id.unwrap())?);
            dicts.push(dict);
        }

        Ok(dicts)
    }

    /// 根据 ID 获取字典
    pub fn get_dictionary_by_id(&self, id: i64) -> Result<Option<Dictionary>, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        match conn.query_row(
            "SELECT id, mongo_oid, name, description, type, created_at, updated_at, version
             FROM dictionaries
             WHERE id = ?",
            params![id],
            |row| {
                Ok(Dictionary {
                    id: Some(row.get(0)?),
                    mongo_oid: row.get(1)?,
                    name: row.get(2)?,
                    description: row.get(3)?,
                    type_: row.get(4)?,
                    created_at: row.get(5)?,
                    updated_at: row.get(6)?,
                    version: row.get(7)?,
                    tags: None,
                    items: None,
                })
            },
        ) {
            Ok(mut dict) => {
                // 获取标签
                dict.tags = Some(self.get_dictionary_tags(id)?);

                // 获取字典项
                dict.items = Some(self.get_dictionary_items(id)?);

                Ok(Some(dict))
            }
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(e.into()),
        }
    }

    /// 根据名称获取字典
    pub fn get_dictionary_by_name(&self, name: &str) -> Result<Option<Dictionary>, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        match conn.query_row(
            "SELECT id, mongo_oid, name, description, type, created_at, updated_at, version
             FROM dictionaries
             WHERE name = ?",
            params![name],
            |row| {
                Ok(Dictionary {
                    id: Some(row.get(0)?),
                    mongo_oid: row.get(1)?,
                    name: row.get(2)?,
                    description: row.get(3)?,
                    type_: row.get(4)?,
                    created_at: row.get(5)?,
                    updated_at: row.get(6)?,
                    version: row.get(7)?,
                    tags: None,
                    items: None,
                })
            },
        ) {
            Ok(mut dict) => {
                let id = dict.id.unwrap();
                // 获取标签
                dict.tags = Some(self.get_dictionary_tags(id)?);

                // 获取字典项
                dict.items = Some(self.get_dictionary_items(id)?);

                Ok(Some(dict))
            }
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(e.into()),
        }
    }

    /// 创建字典
    pub fn create_dictionary(&self, dictionary: &Dictionary) -> Result<i64, Box<dyn Error>> {
        let mut conn = self.sqlite_service.get_connection()?;

        // 开始事务
        let tx = conn.transaction()?;

        // 插入字典
        let now = chrono::Utc::now().to_rfc3339();
        let _dict_id = tx.execute(
            "INSERT INTO dictionaries (mongo_oid, name, description, type, created_at, updated_at, version)
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            params![
                dictionary.mongo_oid,
                dictionary.name,
                dictionary.description,
                dictionary.type_,
                dictionary.created_at.as_ref().unwrap_or(&now),
                dictionary.updated_at.as_ref().unwrap_or(&now),
                dictionary.version
            ],
        )?;

        let dict_id = tx.last_insert_rowid();

        // 插入标签
        if let Some(tags) = &dictionary.tags {
            for tag in tags {
                // 确保标签存在
                tx.execute(
                    "INSERT OR IGNORE INTO tags (tag_name) VALUES (?)",
                    params![tag],
                )?;

                // 获取标签 ID
                let tag_id: i64 = tx.query_row(
                    "SELECT tag_id FROM tags WHERE tag_name = ?",
                    params![tag],
                    |row| row.get(0),
                )?;

                // 关联字典和标签
                tx.execute(
                    "INSERT OR IGNORE INTO dictionary_tags (dictionary_id, tag_id) VALUES (?, ?)",
                    params![dict_id, tag_id],
                )?;
            }
        }

        // 插入字典项
        if let Some(items) = &dictionary.items {
            for item in items {
                tx.execute(
                    "INSERT INTO dictionary_items (dictionary_id, item_key, item_value, item_description, status)
                     VALUES (?, ?, ?, ?, ?)",
                    params![
                        dict_id,
                        item.key,
                        item.value,
                        item.description,
                        item.status
                    ],
                )?;
            }
        }

        // 提交事务
        tx.commit()?;

        Ok(dict_id)
    }

    /// 更新字典
    pub fn update_dictionary(
        &self,
        id: i64,
        dictionary: &Dictionary,
    ) -> Result<bool, Box<dyn Error>> {
        let mut conn = self.sqlite_service.get_connection()?;

        // 开始事务
        let tx = conn.transaction()?;

        // 更新字典
        let now = chrono::Utc::now().to_rfc3339();
        let updated = tx.execute(
            "UPDATE dictionaries
             SET name = ?, description = ?, type = ?, updated_at = ?, version = version + 1
             WHERE id = ?",
            params![
                dictionary.name,
                dictionary.description,
                dictionary.type_,
                dictionary.updated_at.as_ref().unwrap_or(&now),
                id
            ],
        )?;

        if updated == 0 {
            return Ok(false);
        }

        // 更新标签
        if let Some(tags) = &dictionary.tags {
            // 删除现有标签关联
            tx.execute(
                "DELETE FROM dictionary_tags WHERE dictionary_id = ?",
                params![id],
            )?;

            // 添加新标签关联
            for tag in tags {
                // 确保标签存在
                tx.execute(
                    "INSERT OR IGNORE INTO tags (tag_name) VALUES (?)",
                    params![tag],
                )?;

                // 获取标签 ID
                let tag_id: i64 = tx.query_row(
                    "SELECT tag_id FROM tags WHERE tag_name = ?",
                    params![tag],
                    |row| row.get(0),
                )?;

                // 关联字典和标签
                tx.execute(
                    "INSERT OR IGNORE INTO dictionary_tags (dictionary_id, tag_id) VALUES (?, ?)",
                    params![id, tag_id],
                )?;
            }
        }

        // 提交事务
        tx.commit()?;

        Ok(true)
    }

    /// 删除字典
    pub fn delete_dictionary(&self, id: i64) -> Result<bool, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        let deleted = conn.execute("DELETE FROM dictionaries WHERE id = ?", params![id])?;

        Ok(deleted > 0)
    }

    /// 获取字典项
    pub fn get_dictionary_items(
        &self,
        dictionary_id: i64,
    ) -> Result<Vec<DictionaryItem>, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT item_id, dictionary_id, item_key, item_value, item_description, status
             FROM dictionary_items
             WHERE dictionary_id = ?
             ORDER BY item_key",
        )?;

        let item_iter = stmt.query_map(params![dictionary_id], |row| {
            Ok(DictionaryItem {
                item_id: Some(row.get(0)?),
                dictionary_id: Some(row.get(1)?),
                key: row.get(2)?,
                value: row.get(3)?,
                description: row.get(4)?,
                status: row.get(5)?,
            })
        })?;

        let mut items = Vec::new();
        for item_result in item_iter {
            items.push(item_result?);
        }

        Ok(items)
    }

    /// 添加字典项
    pub fn add_dictionary_item(
        &self,
        dictionary_id: i64,
        item: &DictionaryItem,
    ) -> Result<i64, Box<dyn Error>> {
        info!(
            "SqliteDictionaryService::add_dictionary_item: dictionary_id = {}, key = {}",
            dictionary_id, item.key
        );
        info!("SqliteDictionaryService::add_dictionary_item: 字典项数据: value = {}, description = {}, status = {}",
            item.value,
            item.description.as_deref().unwrap_or(""),
            item.status
        );

        // 检查参数是否有效
        if item.key.is_empty() {
            let error_msg = "字典项键不能为空";
            error!(
                "SqliteDictionaryService::add_dictionary_item: {}",
                error_msg
            );
            return Err(error_msg.into());
        }

        if item.value.is_empty() {
            let error_msg = "字典项值不能为空";
            error!(
                "SqliteDictionaryService::add_dictionary_item: {}",
                error_msg
            );
            return Err(error_msg.into());
        }

        // 检查字典是否存在
        match self.get_dictionary_by_id(dictionary_id) {
            Ok(None) => {
                let error_msg = format!("字典不存在: ID = {}", dictionary_id);
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                return Err(error_msg.into());
            }
            Err(e) => {
                let error_msg = format!("获取字典失败: {}", e);
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                return Err(error_msg.into());
            }
            Ok(Some(dict)) => {
                info!(
                    "SqliteDictionaryService::add_dictionary_item: 字典存在: ID = {}, 名称 = {}",
                    dictionary_id, dict.name
                );
            }
        }

        // 获取数据库连接
        let mut conn = match self.sqlite_service.get_connection() {
            Ok(conn) => {
                info!("SqliteDictionaryService::add_dictionary_item: 获取数据库连接成功");
                conn
            }
            Err(e) => {
                let error_msg = format!("获取数据库连接失败: {}", e);
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                return Err(error_msg.into());
            }
        };

        // 检查字典项键是否已存在
        let item_exists: bool = match conn.query_row(
            "SELECT 1 FROM dictionary_items WHERE dictionary_id = ? AND item_key = ?",
            params![dictionary_id, item.key],
            |_| Ok(true),
        ) {
            Ok(_) => {
                let error_msg = format!(
                    "字典项键已存在: dictionary_id = {}, key = {}",
                    dictionary_id, item.key
                );
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                true
            }
            Err(rusqlite::Error::QueryReturnedNoRows) => {
                info!("SqliteDictionaryService::add_dictionary_item: 字典项键不存在，可以添加");
                false
            }
            Err(e) => {
                let error_msg = format!("检查字典项键失败: {}", e);
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                return Err(error_msg.into());
            }
        };

        if item_exists {
            let error_msg = format!("字典项键已存在: {}", item.key);
            return Err(error_msg.into());
        }

        info!("SqliteDictionaryService::add_dictionary_item: 准备添加字典项");

        // 尝试开始事务
        let tx = match conn.transaction() {
            Ok(tx) => {
                info!("SqliteDictionaryService::add_dictionary_item: 开始事务成功");
                tx
            }
            Err(e) => {
                let error_msg = format!("开始事务失败: {}", e);
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                return Err(error_msg.into());
            }
        };

        // 添加字典项
        let result = tx.execute(
            "INSERT INTO dictionary_items (dictionary_id, item_key, item_value, item_description, status)
             VALUES (?, ?, ?, ?, ?)",
            params![
                dictionary_id,
                item.key,
                item.value,
                item.description,
                item.status
            ],
        );

        match result {
            Ok(rows) => {
                info!(
                    "SqliteDictionaryService::add_dictionary_item: 添加字典项成功: rows = {}",
                    rows
                );

                // 更新字典的更新时间
                let now = chrono::Utc::now().to_rfc3339();
                let update_result = tx.execute(
                    "UPDATE dictionaries SET updated_at = ?, version = version + 1 WHERE id = ?",
                    params![now, dictionary_id],
                );

                match update_result {
                    Ok(updated) => {
                        info!("SqliteDictionaryService::add_dictionary_item: 更新字典时间成功: updated = {}", updated);
                    }
                    Err(e) => {
                        let error_msg = format!("更新字典时间失败: {}", e);
                        error!(
                            "SqliteDictionaryService::add_dictionary_item: {}",
                            error_msg
                        );
                        return Err(error_msg.into());
                    }
                }

                // 提交事务
                match tx.commit() {
                    Ok(_) => {
                        info!("SqliteDictionaryService::add_dictionary_item: 提交事务成功");
                    }
                    Err(e) => {
                        let error_msg = format!("提交事务失败: {}", e);
                        error!(
                            "SqliteDictionaryService::add_dictionary_item: {}",
                            error_msg
                        );
                        return Err(error_msg.into());
                    }
                }

                // 获取新插入的行 ID
                let id = conn.last_insert_rowid();
                info!(
                    "SqliteDictionaryService::add_dictionary_item: 新字典项ID: {}",
                    id
                );

                Ok(id)
            }
            Err(e) => {
                let error_msg = format!("添加字典项失败: {}", e);
                error!(
                    "SqliteDictionaryService::add_dictionary_item: {}",
                    error_msg
                );
                Err(error_msg.into())
            }
        }
    }

    /// 更新字典项
    pub fn update_dictionary_item(
        &self,
        dictionary_id: i64,
        key: &str,
        item: &DictionaryItem,
    ) -> Result<bool, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        let updated = conn.execute(
            "UPDATE dictionary_items
             SET item_key = ?, item_value = ?, item_description = ?, status = ?
             WHERE dictionary_id = ? AND item_key = ?",
            params![
                item.key,
                item.value,
                item.description,
                item.status,
                dictionary_id,
                key
            ],
        )?;

        if updated > 0 {
            // 更新字典的更新时间
            let now = chrono::Utc::now().to_rfc3339();
            conn.execute(
                "UPDATE dictionaries SET updated_at = ?, version = version + 1 WHERE id = ?",
                params![now, dictionary_id],
            )?;
        }

        Ok(updated > 0)
    }

    /// 删除字典项
    pub fn delete_dictionary_item(
        &self,
        dictionary_id: i64,
        key: &str,
    ) -> Result<bool, Box<dyn Error>> {
        info!(
            "SqliteDictionaryService::delete_dictionary_item: dictionary_id = {}, key = {}",
            dictionary_id, key
        );

        // 检查参数是否有效
        if key.is_empty() {
            error!("SqliteDictionaryService::delete_dictionary_item: 字典项键为空");
            return Err("字典项键不能为空".into());
        }

        // 检查字典是否存在
        match self.get_dictionary_by_id(dictionary_id) {
            Ok(None) => {
                error!(
                    "SqliteDictionaryService::delete_dictionary_item: 字典不存在: ID = {}",
                    dictionary_id
                );
                return Err(format!("字典不存在: ID = {}", dictionary_id).into());
            }
            Err(e) => {
                error!(
                    "SqliteDictionaryService::delete_dictionary_item: 获取字典失败: {}",
                    e
                );
                return Err(format!("获取字典失败: {}", e).into());
            }
            _ => {}
        }

        // 检查字典项是否存在
        let conn = self.sqlite_service.get_connection()?;
        let item_exists: bool = match conn.query_row(
            "SELECT 1 FROM dictionary_items WHERE dictionary_id = ? AND item_key = ?",
            params![dictionary_id, key],
            |_| Ok(true),
        ) {
            Ok(_) => true,
            Err(rusqlite::Error::QueryReturnedNoRows) => {
                error!("SqliteDictionaryService::delete_dictionary_item: 字典项不存在: dictionary_id = {}, key = {}", dictionary_id, key);
                false
            }
            Err(e) => {
                error!(
                    "SqliteDictionaryService::delete_dictionary_item: 检查字典项失败: {}",
                    e
                );
                return Err(format!("检查字典项失败: {}", e).into());
            }
        };

        if !item_exists {
            return Ok(false);
        }

        info!("SqliteDictionaryService::delete_dictionary_item: 字典项存在，准备删除");

        // 删除字典项
        let deleted = conn.execute(
            "DELETE FROM dictionary_items WHERE dictionary_id = ? AND item_key = ?",
            params![dictionary_id, key],
        )?;

        info!(
            "SqliteDictionaryService::delete_dictionary_item: 删除结果: deleted = {}",
            deleted
        );

        if deleted > 0 {
            // 更新字典的更新时间
            let now = chrono::Utc::now().to_rfc3339();
            let updated = conn.execute(
                "UPDATE dictionaries SET updated_at = ?, version = version + 1 WHERE id = ?",
                params![now, dictionary_id],
            )?;

            info!(
                "SqliteDictionaryService::delete_dictionary_item: 更新字典时间结果: updated = {}",
                updated
            );
        }

        Ok(deleted > 0)
    }

    /// 批量删除字典项
    pub fn batch_delete_dictionary_items(
        &self,
        dictionary_id: i64,
        keys: &[String],
    ) -> Result<i32, Box<dyn Error>> {
        info!(
            "SqliteDictionaryService::batch_delete_dictionary_items: dictionary_id = {}, keys = {:?}",
            dictionary_id, keys
        );

        // 检查参数是否有效
        if keys.is_empty() {
            error!("SqliteDictionaryService::batch_delete_dictionary_items: 字典项键列表为空");
            return Err("字典项键列表不能为空".into());
        }

        // 检查字典是否存在
        match self.get_dictionary_by_id(dictionary_id) {
            Ok(None) => {
                error!(
                    "SqliteDictionaryService::batch_delete_dictionary_items: 字典不存在: ID = {}",
                    dictionary_id
                );
                return Err(format!("字典不存在: ID = {}", dictionary_id).into());
            }
            Err(e) => {
                error!(
                    "SqliteDictionaryService::batch_delete_dictionary_items: 获取字典失败: {}",
                    e
                );
                return Err(format!("获取字典失败: {}", e).into());
            }
            _ => {}
        }

        let mut conn = self.sqlite_service.get_connection()?;
        let tx = conn.transaction()?;

        let mut total_deleted = 0;

        // 批量删除字典项
        for key in keys {
            if key.is_empty() {
                continue;
            }

            let deleted = tx.execute(
                "DELETE FROM dictionary_items WHERE dictionary_id = ? AND item_key = ?",
                params![dictionary_id, key],
            )?;

            total_deleted += deleted;
            info!(
                "SqliteDictionaryService::batch_delete_dictionary_items: 删除字典项 '{}': {}",
                key, deleted
            );
        }

        // 如果有删除操作，更新字典的更新时间
        if total_deleted > 0 {
            let now = chrono::Utc::now().to_rfc3339();
            let updated = tx.execute(
                "UPDATE dictionaries SET updated_at = ?, version = version + 1 WHERE id = ?",
                params![now, dictionary_id],
            )?;

            info!(
                "SqliteDictionaryService::batch_delete_dictionary_items: 更新字典时间结果: updated = {}",
                updated
            );
        }

        tx.commit()?;

        info!(
            "SqliteDictionaryService::batch_delete_dictionary_items: 总共删除了 {} 个字典项",
            total_deleted
        );

        Ok(total_deleted as i32)
    }

    /// 获取字典标签
    fn get_dictionary_tags(&self, dictionary_id: i64) -> Result<Vec<String>, Box<dyn Error>> {
        let conn = self.sqlite_service.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT t.tag_name
             FROM tags t
             JOIN dictionary_tags dt ON t.tag_id = dt.tag_id
             WHERE dt.dictionary_id = ?
             ORDER BY t.tag_name",
        )?;

        let tag_iter = stmt.query_map(params![dictionary_id], |row| row.get::<_, String>(0))?;

        let mut tags = Vec::new();
        for tag_result in tag_iter {
            tags.push(tag_result?);
        }

        Ok(tags)
    }
}

// 创建单例实例
lazy_static::lazy_static! {
    pub static ref SQLITE_DICTIONARY_SERVICE: std::sync::Mutex<Option<SqliteDictionaryService>> = std::sync::Mutex::new(None);
}

/// 初始化 SQLite 字典服务
pub fn init_sqlite_dictionary_service(
    sqlite_service: Arc<SqliteService>,
) -> Result<(), Box<dyn Error>> {
    let service = SqliteDictionaryService::new(sqlite_service);
    let mut sqlite_dict_service = SQLITE_DICTIONARY_SERVICE.lock().unwrap();
    *sqlite_dict_service = Some(service);
    Ok(())
}

/// 获取 SQLite 字典服务实例
pub fn get_sqlite_dictionary_service() -> Result<SqliteDictionaryService, Box<dyn Error>> {
    let sqlite_dict_service = SQLITE_DICTIONARY_SERVICE.lock().unwrap();
    match &*sqlite_dict_service {
        Some(service) => Ok(service.clone()),
        None => Err("SQLite 字典服务未初始化".into()),
    }
}

// 为 SqliteDictionaryService 实现 Clone trait
impl Clone for SqliteDictionaryService {
    fn clone(&self) -> Self {
        Self {
            sqlite_service: Arc::clone(&self.sqlite_service),
        }
    }
}
