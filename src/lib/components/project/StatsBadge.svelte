<script lang="ts">
  export let title = '';
  export let count: number | string = 0;
  export let color: 'blue' | 'green' | 'purple' | 'orange' | 'gray' = 'blue';
  export let selectable = false;
  export let active = false;
  export let onClick: (() => void) | null = null;

  function colorClasses(c: string) {
    const map: Record<string, { bg: string; text: string; border: string; hover: string }> = {
      blue: {
        bg: 'bg-blue-50',
        text: 'text-blue-700',
        border: 'border-blue-200',
        hover: 'hover:bg-blue-100'
      },
      green: {
        bg: 'bg-green-50',
        text: 'text-green-700',
        border: 'border-green-200',
        hover: 'hover:bg-green-100'
      },
      purple: {
        bg: 'bg-purple-50',
        text: 'text-purple-700',
        border: 'border-purple-200',
        hover: 'hover:bg-purple-100'
      },
      orange: {
        bg: 'bg-orange-50',
        text: 'text-orange-700',
        border: 'border-orange-200',
        hover: 'hover:bg-orange-100'
      },
      gray: {
        bg: 'bg-gray-50',
        text: 'text-gray-700',
        border: 'border-gray-200',
        hover: 'hover:bg-gray-100'
      }
    };
    return map[c] || map.blue;
  }

  function handleClick() {
    if (onClick) {
      onClick();
    }
  }
</script>

{#if selectable}
  <button
    type="button"
    class={`inline-flex items-center gap-1 px-3 py-1.5 rounded-full border transition-colors duration-150 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 ${colorClasses(color).border} ${colorClasses(color).text} ${colorClasses(color).hover} ${active ? `${colorClasses(color).bg} ring-2 ring-offset-0` : 'bg-white'}`}
    class:shadow={active}
    on:click={handleClick}
    aria-pressed={active}
  >
    <span class="text-[12px] leading-none font-medium">{title}</span>
    <span class="text-[13px] font-semibold tabular-nums leading-none">{count}</span>
  </button>
{:else}
  <div
    class={`inline-flex items-center gap-1 px-2.5 py-1 rounded-full border ${colorClasses(color).bg} ${colorClasses(color).text} ${colorClasses(color).border}`}
    aria-hidden="true"
  >
    <span class="text-[12px] leading-none">{title}</span>
    <span class="text-[13px] font-semibold tabular-nums leading-none">{count}</span>
  </div>
{/if}
