# Lighthouse API 集成文档

## 文档说明

> **注意**: 本文档提供 Lighthouse API 的详细接口规范和集成指南。用户功能说明请参考 [功能使用手册](../../FUNCTIONAL_MANUAL.md#9-lighthouse-api-集成)。

## API 基本信息

- **API 基础地址**: `http://**************:3001`
- **API 版本**: v1.0
- **协议**: HTTP/HTTPS
- **数据格式**: JSON

## 后端技术栈

### 核心特性

- **TypeScript & NestJS**: Modern, type-safe backend development
- **Prisma ORM**: Type-safe database access with MySQL
- **JWT Authentication**: Secure user authentication and authorization
- **Role-based Access Control**: Admin and User role management
- **RESTful API**: Well-structured API endpoints
- **Docker Support**: Containerized deployment for Tencent Cloud
- **Validation**: Request validation using class-validator
- **Security Features**:
  - Helmet for HTTP security headers
  - Rate limiting to prevent abuse
  - CORS protection
  - Environment variable validation
- **API Documentation**:
  - Swagger/OpenAPI interactive documentation
  - Compodoc for comprehensive code documentation

## 项目概述

Lighthouse Backend 是一个基于 NestJS 框架的后端 API 服务，使用 Prisma ORM 连接 MySQL 数据库。该项目实现了用户认证、角色管理、书籍管理、文章管理和患者管理等功能。患者管理模块包括患者基本信息管理、就诊/随访记录管理和患者疾病记录管理，适用于医疗机构和临床研究场景。

## 模块结构

项目包含以下主要模块：

1. **AppModule**: 应用程序的根模块
2. **AuthModule**: 处理认证和授权
3. **UsersModule**: 用户管理
4. **BooksModule**: 书籍管理
5. **PostsModule**: 文章管理
6. **PatientsModule**: 患者管理
7. **VisitsModule**: 就诊/随访记录管理
8. **PatientConditionsModule**: 患者疾病记录管理
9. **PrismaModule**: 数据库连接
10. **ConfigModule**: 配置管理

## API 端点概览

### 认证 API

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/auth/login | 用户登录 | 否 |

### 用户 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/users | 创建用户 | 否 | 所有人 |
| GET | /api/users | 获取所有用户 | JWT | ADMIN |
| GET | /api/users/:id | 获取单个用户 | JWT | 所有认证用户 |
| PATCH | /api/users/:id | 更新用户 | JWT | 所有认证用户 |
| DELETE | /api/users/:id | 删除用户 | JWT | ADMIN |

### 书籍 API

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/books | 创建书籍 | JWT |
| GET | /api/books | 获取所有书籍 | 否 |
| GET | /api/books/:id | 获取单本书籍 | 否 |
| PUT | /api/books/:id | 更新书籍 | JWT |
| DELETE | /api/books/:id | 删除书籍 | JWT |

### 文章 API

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/posts | 创建文章 | JWT |
| GET | /api/posts | 获取所有文章 | 否 |
| GET | /api/posts/:id | 获取单篇文章 | 否 |
| PATCH | /api/posts/:id | 更新文章 | JWT |
| DELETE | /api/posts/:id | 删除文章 | JWT |

### 患者管理 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/patients | 创建患者记录 | JWT | 所有认证用户 |
| GET | /api/patients | 获取患者列表 | JWT | 所有认证用户 |
| GET | /api/patients/:id | 获取单个患者详情 | JWT | 所有认证用户 |
| PATCH | /api/patients/:id | 更新患者信息 | JWT | 所有认证用户 |
| DELETE | /api/patients/:id | 删除患者记录 | JWT | ADMIN |

### 就诊/随访记录 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/visits | 创建就诊/随访记录 | JWT | 所有认证用户 |
| GET | /api/visits | 获取就诊/随访记录列表 | JWT | 所有认证用户 |
| GET | /api/visits/:id | 获取单个就诊/随访记录详情 | JWT | 所有认证用户 |
| PATCH | /api/visits/:id | 更新就诊/随访记录信息 | JWT | 所有认证用户 |
| DELETE | /api/visits/:id | 删除就诊/随访记录 | JWT | ADMIN |

### 患者疾病记录 API

| 方法 | 路径 | 描述 | 认证 | 权限 |
|------|------|------|------|------|
| POST | /api/patient-conditions | 创建患者疾病记录 | JWT | 所有认证用户 |
| GET | /api/patient-conditions | 获取患者疾病记录列表 | JWT | 所有认证用户 |
| GET | /api/patient-conditions/:id | 获取单个患者疾病记录详情 | JWT | 所有认证用户 |
| PATCH | /api/patient-conditions/:id | 更新患者疾病记录信息 | JWT | 所有认证用户 |
| DELETE | /api/patient-conditions/:id | 删除患者疾病记录 | JWT | ADMIN |

## 认证与授权

### JWT 认证流程

1. 用户通过 `/api/auth/login` 端点提交用户名和密码
2. 服务器验证凭据并返回 JWT 令牌
3. 客户端在后续请求中使用 Bearer 认证头包含该令牌
4. 受保护的端点使用 `JwtAuthGuard` 验证令牌

### 角色权限

| 角色 | 描述 | 权限 |
|------|------|------|
| USER | 普通用户 | 可以访问自己的用户信息，查看公开内容 |
| ADMIN | 管理员 | 可以管理所有用户、文章和书籍，拥有完全访问权限 |

### 请求认证

在需要认证的API请求中，需要在HTTP头部添加Authorization字段：

```
Authorization: Bearer <JWT_TOKEN>
```

## 数据模型

### 用户模型 (User)

```typescript
{
  id: number;
  email: string;
  username: string;
  password: string; // 哈希值
  fullName?: string;
  role: 'USER' | 'ADMIN';
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  birthDate?: Date;
  avatar?: string;
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### 患者模型 (Patient)

```typescript
{
  id: number;
  assignedUserId: number;
  name: string;
  gender?: 'Male' | 'Female' | 'Other';
  dateOfBirth?: Date;
  nationalIdNumber?: string;
  ethnicity?: string;
  occupation?: string;
  educationLevel?: string;
  maritalStatus?: string;
  registrationDate: Date;
  notesGeneral?: string;
  primaryPhoneNumber: string;
  secondaryPhoneNumber?: string;
  // ... 更多字段
}
```

## 环境变量配置

```bash
# 数据库连接
DATABASE_URL="mysql://username:password@host:3306/lighthouse_db"

# JWT 认证
JWT_SECRET="your-jwt-secret-key-here"
JWT_EXPIRES_IN="1d"

# 服务器配置
PORT=3001
NODE_ENV="production"

# CORS
ALLOWED_ORIGINS="*"

# 速率限制
THROTTLE_TTL=60
THROTTLE_LIMIT=10
```

## 部署信息

### Docker 部署

```yaml
version: '3'
services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=mysql://username:password@db:3306/lighthouse_db
      - JWT_SECRET=your-jwt-secret-key-here
      - NODE_ENV=production
    depends_on:
      - db
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=lighthouse_db
    volumes:
      - mysql_data:/var/lib/mysql
```

### 腾讯云部署

1. **数据库连接**: 使用腾讯云轻量数据库的连接信息
2. **防火墙配置**: 确保开放API服务器使用的端口（默认3001）
3. **使用 Nginx 反向代理**: 建议使用 Nginx 作为反向代理，并配置 HTTPS

## API 测试

### Swagger UI

访问 `http://**************:3001/api/docs` 查看交互式API文档。

### 示例请求

#### 用户登录

```bash
curl -X POST http://**************:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password"
  }'
```

#### 获取患者列表

```bash
curl -X GET http://**************:3001/api/patients \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json"
```

## 错误处理

### 标准错误响应格式

```json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Bad Request"
}
```

### 常见错误码

| 状态码 | 描述 | 解决方案 |
|--------|------|----------|
| 401 | 未授权 | 检查JWT令牌是否有效 |
| 403 | 权限不足 | 确认用户角色权限 |
| 404 | 资源不存在 | 检查请求的资源ID |
| 429 | 请求过于频繁 | 降低请求频率 |

## 相关文档

- [功能使用手册 - Lighthouse API 集成](../../FUNCTIONAL_MANUAL.md#9-lighthouse-api-集成)
- [用户管理逻辑文档](./lighthouse-user-management.md)
- [系统配置管理](../../FUNCTIONAL_MANUAL.md#7-系统配置管理)

---

**最后更新**: 2024年12月  
**维护者**: 开发团队  
**文档版本**: v2.0