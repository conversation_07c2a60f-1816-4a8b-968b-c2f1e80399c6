use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct FilterCondition {
    pub field: String,
    pub operator: String, // 'equals', 'in', 'contains', 'between', 'not_equals'
    pub value: serde_json::Value,
    pub label: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FilterConfig {
    pub id: Option<String>,
    pub name: String,
    pub description: Option<String>,
    pub conditions: Vec<FilterCondition>,
    pub is_default: Option<bool>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateFilterConfigRequest {
    pub name: String,
    pub description: Option<String>,
    pub conditions: Vec<FilterCondition>,
    pub is_default: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateFilterConfigRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub conditions: Option<Vec<FilterCondition>>,
    pub is_default: Option<bool>,
}
