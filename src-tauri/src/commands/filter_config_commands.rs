use crate::models::filter_config::{
    CreateFilterConfigRequest, FilterConfig, UpdateFilterConfigRequest,
};
use crate::services::filter_config_service::FilterConfigService;
use rusqlite::Connection;
use tauri::command;

#[command]
pub fn init_filter_config_tables(db_path: String) -> Result<(), String> {
    let conn = Connection::open(&db_path).map_err(|e| format!("打开数据库失败: {}", e))?;
    FilterConfigService::init_tables(&conn)
}

#[command]
pub fn get_filter_configs(db_path: String) -> Result<Vec<FilterConfig>, String> {
    let conn = Connection::open(&db_path).map_err(|e| format!("打开数据库失败: {}", e))?;
    FilterConfigService::get_all_configs(&conn)
}

#[command]
pub fn get_filter_config(id: String, db_path: String) -> Result<Option<FilterConfig>, String> {
    let conn = Connection::open(&db_path).map_err(|e| format!("打开数据库失败: {}", e))?;
    FilterConfigService::get_config_by_id(&conn, &id)
}

#[command]
pub fn save_filter_config(
    config: CreateFilterConfigRequest,
    db_path: String,
) -> Result<FilterConfig, String> {
    let conn = Connection::open(&db_path).map_err(|e| format!("打开数据库失败: {}", e))?;
    FilterConfigService::create_config(&conn, config)
}

#[command]
pub fn update_filter_config(
    id: String,
    config: UpdateFilterConfigRequest,
    db_path: String,
) -> Result<FilterConfig, String> {
    let conn = Connection::open(&db_path).map_err(|e| format!("打开数据库失败: {}", e))?;
    FilterConfigService::update_config(&conn, &id, config)
}

#[command]
pub fn delete_filter_config(id: String, db_path: String) -> Result<(), String> {
    let conn = Connection::open(&db_path).map_err(|e| format!("打开数据库失败: {}", e))?;
    FilterConfigService::delete_config(&conn, &id)
}
