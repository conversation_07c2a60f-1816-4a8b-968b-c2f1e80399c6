use rusqlite::{params, Connection};
use std::error::Error;

use crate::models::auto_criteria_history::{
    AutoCriteriaHistory, AutoCriteriaHistoryQuery, CreateAutoCriteriaHistoryRequest,
};

pub struct AutoCriteriaHistoryRepository {
    db_path: String,
}

impl AutoCriteriaHistoryRepository {
    pub fn new(db_path: String) -> Self {
        Self { db_path }
    }

    fn get_connection(&self) -> Result<Connection, Box<dyn Error>> {
        let conn = Connection::open(&self.db_path)?;
        Ok(conn)
    }

    pub fn init_tables(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;
        conn.execute(
            "CREATE TABLE IF NOT EXISTS auto_criteria_history (
                history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                created_at TEXT NOT NULL,
                input_text TEXT NOT NULL,
                segmented_text TEXT,
                model_id TEXT,
                prompt_template TEXT,
                generated_json TEXT,
                processing_details TEXT,
                success_count INTEGER,
                failed_count INTEGER,
                notes TEXT
            )",
            [],
        )?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_auto_criteria_history_created_at ON auto_criteria_history(created_at)",
            [],
        )?;
        Ok(())
    }

    pub fn create(&self, req: &CreateAutoCriteriaHistoryRequest) -> Result<i64, Box<dyn Error>> {
        let conn = self.get_connection()?;
        let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();
        conn.execute(
            "INSERT INTO auto_criteria_history (
                created_at, input_text, segmented_text, model_id, prompt_template,
                generated_json, processing_details, success_count, failed_count, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            params![
                now,
                req.input_text,
                req.segmented_text,
                req.model_id,
                req.prompt_template,
                req.generated_json,
                req.processing_details,
                req.success_count,
                req.failed_count,
                req.notes,
            ],
        )?;
        Ok(conn.last_insert_rowid())
    }

    pub fn list(
        &self,
        q: &AutoCriteriaHistoryQuery,
    ) -> Result<Vec<AutoCriteriaHistory>, Box<dyn Error>> {
        let conn = self.get_connection()?;
        let mut sql = String::from(
            "SELECT history_id, created_at, input_text, segmented_text, model_id, prompt_template, generated_json, processing_details, success_count, failed_count, notes FROM auto_criteria_history"
        );
        let kw = q.keyword.as_ref().map(|k| format!("%{}%", k));
        let use_kw = kw.is_some();
        if use_kw {
            sql.push_str(" WHERE input_text LIKE ? OR segmented_text LIKE ?");
        }
        sql.push_str(" ORDER BY datetime(created_at) DESC");
        if let Some(limit) = q.limit {
            sql.push_str(&format!(" LIMIT {}", limit));
        }
        if let Some(offset) = q.offset {
            sql.push_str(&format!(" OFFSET {}", offset));
        }
        let mut stmt = conn.prepare(&sql)?;
        let mut out = vec![];
        if use_kw {
            let rows = stmt.query_map(
                rusqlite::params![
                    kw.as_ref().as_deref().unwrap(),
                    kw.as_ref().as_deref().unwrap()
                ],
                |row| {
                    Ok(AutoCriteriaHistory {
                        history_id: row.get(0)?,
                        created_at: row.get(1)?,
                        input_text: row.get(2)?,
                        segmented_text: row.get(3)?,
                        model_id: row.get(4)?,
                        prompt_template: row.get(5)?,
                        generated_json: row.get(6)?,
                        processing_details: row.get(7)?,
                        success_count: row.get(8)?,
                        failed_count: row.get(9)?,
                        notes: row.get(10)?,
                    })
                },
            )?;
            for r in rows {
                out.push(r?);
            }
        } else {
            let rows = stmt.query_map([], |row| {
                Ok(AutoCriteriaHistory {
                    history_id: row.get(0)?,
                    created_at: row.get(1)?,
                    input_text: row.get(2)?,
                    segmented_text: row.get(3)?,
                    model_id: row.get(4)?,
                    prompt_template: row.get(5)?,
                    generated_json: row.get(6)?,
                    processing_details: row.get(7)?,
                    success_count: row.get(8)?,
                    failed_count: row.get(9)?,
                    notes: row.get(10)?,
                })
            })?;
            for r in rows {
                out.push(r?);
            }
        }
        Ok(out)
    }

    pub fn get(&self, history_id: i64) -> Result<Option<AutoCriteriaHistory>, Box<dyn Error>> {
        let conn = self.get_connection()?;
        let mut stmt = conn.prepare(
            "SELECT history_id, created_at, input_text, segmented_text, model_id, prompt_template, generated_json, processing_details, success_count, failed_count, notes FROM auto_criteria_history WHERE history_id=?"
        )?;
        let mut rows = stmt.query(params![history_id])?;
        if let Some(row) = rows.next()? {
            Ok(Some(AutoCriteriaHistory {
                history_id: row.get(0)?,
                created_at: row.get(1)?,
                input_text: row.get(2)?,
                segmented_text: row.get(3)?,
                model_id: row.get(4)?,
                prompt_template: row.get(5)?,
                generated_json: row.get(6)?,
                processing_details: row.get(7)?,
                success_count: row.get(8)?,
                failed_count: row.get(9)?,
                notes: row.get(10)?,
            }))
        } else {
            Ok(None)
        }
    }

    pub fn delete(&self, history_id: i64) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;
        conn.execute(
            "DELETE FROM auto_criteria_history WHERE history_id=?",
            params![history_id],
        )?;
        Ok(())
    }
}
