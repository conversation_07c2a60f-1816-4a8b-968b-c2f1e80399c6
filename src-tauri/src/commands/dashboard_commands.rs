use log::{error, info};
use tauri::command;

use crate::models::dashboard::{
    ChartDataPoint, DashboardFilterParams, DashboardOverview, DiseaseDistribution,
    FinancialMetrics, MonthlyNewProjects, PersonnelMetrics, ProjectStageDistribution,
    ProjectStatusDistribution, RecruitmentStatusDistribution, SponsorDistribution, TimelineMetrics,
};
use crate::services::dashboard_service::DashboardService;

/// 获取仪表盘概览数据
#[command]
pub fn get_dashboard_overview(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<DashboardOverview, String> {
    info!("获取仪表盘概览数据");

    let service = DashboardService::new(db_path);

    match service.get_dashboard_overview(filter_params.as_ref()) {
        Ok(overview) => Ok(overview),
        Err(e) => {
            error!("获取仪表盘概览数据失败: {}", e);
            Err(format!("获取仪表盘概览数据失败: {}", e))
        }
    }
}

/// 获取项目状态分布
#[command]
pub fn get_project_status_distribution(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ProjectStatusDistribution>, String> {
    info!("获取项目状态分布");

    let service = DashboardService::new(db_path);

    match service.get_project_status_distribution(filter_params.as_ref()) {
        Ok(distribution) => Ok(distribution),
        Err(e) => {
            error!("获取项目状态分布失败: {}", e);
            Err(format!("获取项目状态分布失败: {}", e))
        }
    }
}

/// 获取项目状态分布图表数据
#[command]
pub fn get_project_status_chart_data(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ChartDataPoint>, String> {
    info!("获取项目状态分布图表数据");

    let service = DashboardService::new(db_path);

    match service.get_project_status_distribution(filter_params.as_ref()) {
        Ok(distribution) => {
            let chart_data = service.convert_status_distribution_to_chart_data(distribution);
            Ok(chart_data)
        }
        Err(e) => {
            error!("获取项目状态分布图表数据失败: {}", e);
            Err(format!("获取项目状态分布图表数据失败: {}", e))
        }
    }
}

/// 获取项目阶段分布
#[command]
pub fn get_project_stage_distribution(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ProjectStageDistribution>, String> {
    info!("获取项目阶段分布");

    let service = DashboardService::new(db_path);

    match service.get_project_stage_distribution(filter_params.as_ref()) {
        Ok(distribution) => Ok(distribution),
        Err(e) => {
            error!("获取项目阶段分布失败: {}", e);
            Err(format!("获取项目阶段分布失败: {}", e))
        }
    }
}

/// 获取项目阶段分布图表数据
#[command]
pub fn get_project_stage_chart_data(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ChartDataPoint>, String> {
    info!("获取项目阶段分布图表数据");

    let service = DashboardService::new(db_path);

    match service.get_project_stage_distribution(filter_params.as_ref()) {
        Ok(distribution) => {
            let chart_data = service.convert_stage_distribution_to_chart_data(distribution);
            Ok(chart_data)
        }
        Err(e) => {
            error!("获取项目阶段分布图表数据失败: {}", e);
            Err(format!("获取项目阶段分布图表数据失败: {}", e))
        }
    }
}

/// 获取招募状态分布
#[command]
pub fn get_recruitment_status_distribution(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<RecruitmentStatusDistribution>, String> {
    info!("获取招募状态分布");

    let service = DashboardService::new(db_path);

    match service.get_recruitment_status_distribution(filter_params.as_ref()) {
        Ok(distribution) => Ok(distribution),
        Err(e) => {
            error!("获取招募状态分布失败: {}", e);
            Err(format!("获取招募状态分布失败: {}", e))
        }
    }
}

/// 获取招募状态分布图表数据
#[command]
pub fn get_recruitment_status_chart_data(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ChartDataPoint>, String> {
    info!("获取招募状态分布图表数据");

    let service = DashboardService::new(db_path);

    match service.get_recruitment_status_distribution(filter_params.as_ref()) {
        Ok(distribution) => {
            let chart_data =
                service.convert_recruitment_status_distribution_to_chart_data(distribution);
            Ok(chart_data)
        }
        Err(e) => {
            error!("获取招募状态分布图表数据失败: {}", e);
            Err(format!("获取招募状态分布图表数据失败: {}", e))
        }
    }
}

/// 获取疾病领域分布
#[command]
pub fn get_disease_distribution(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<DiseaseDistribution>, String> {
    info!("获取疾病领域分布");

    let service = DashboardService::new(db_path);

    match service.get_disease_distribution(filter_params.as_ref()) {
        Ok(distribution) => Ok(distribution),
        Err(e) => {
            error!("获取疾病领域分布失败: {}", e);
            Err(format!("获取疾病领域分布失败: {}", e))
        }
    }
}

/// 获取疾病领域分布图表数据
#[command]
pub fn get_disease_chart_data(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ChartDataPoint>, String> {
    info!("获取疾病领域分布图表数据");

    let service = DashboardService::new(db_path);

    match service.get_disease_distribution(filter_params.as_ref()) {
        Ok(distribution) => {
            let chart_data = service.convert_disease_distribution_to_chart_data(distribution);
            Ok(chart_data)
        }
        Err(e) => {
            error!("获取疾病领域分布图表数据失败: {}", e);
            Err(format!("获取疾病领域分布图表数据失败: {}", e))
        }
    }
}

/// 获取申办方项目分布
#[command]
pub fn get_sponsor_distribution(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<SponsorDistribution>, String> {
    info!("获取申办方项目分布");

    let service = DashboardService::new(db_path);

    match service.get_sponsor_distribution(filter_params.as_ref()) {
        Ok(distribution) => Ok(distribution),
        Err(e) => {
            error!("获取申办方项目分布失败: {}", e);
            Err(format!("获取申办方项目分布失败: {}", e))
        }
    }
}

/// 获取申办方项目分布图表数据
#[command]
pub fn get_sponsor_chart_data(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ChartDataPoint>, String> {
    info!("获取申办方项目分布图表数据");

    let service = DashboardService::new(db_path);

    match service.get_sponsor_distribution(filter_params.as_ref()) {
        Ok(distribution) => {
            let chart_data = service.convert_sponsor_distribution_to_chart_data(distribution);
            Ok(chart_data)
        }
        Err(e) => {
            error!("获取申办方项目分布图表数据失败: {}", e);
            Err(format!("获取申办方项目分布图表数据失败: {}", e))
        }
    }
}

/// 获取每月新启动项目数
#[command]
pub fn get_monthly_new_projects(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<MonthlyNewProjects>, String> {
    info!("获取每月新启动项目数");

    let service = DashboardService::new(db_path);

    match service.get_monthly_new_projects(filter_params.as_ref()) {
        Ok(monthly_data) => Ok(monthly_data),
        Err(e) => {
            error!("获取每月新启动项目数失败: {}", e);
            Err(format!("获取每月新启动项目数失败: {}", e))
        }
    }
}

/// 获取每月新启动项目数图表数据
#[command]
pub fn get_monthly_new_projects_chart_data(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<Vec<ChartDataPoint>, String> {
    info!("获取每月新启动项目数图表数据");

    let service = DashboardService::new(db_path);

    match service.get_monthly_new_projects(filter_params.as_ref()) {
        Ok(monthly_data) => {
            let chart_data = service.convert_monthly_new_projects_to_chart_data(monthly_data);
            Ok(chart_data)
        }
        Err(e) => {
            error!("获取每月新启动项目数图表数据失败: {}", e);
            Err(format!("获取每月新启动项目数图表数据失败: {}", e))
        }
    }
}

/// 调试：检查项目数据统计
#[command]
pub fn debug_project_date_statistics(db_path: String) -> Result<String, String> {
    info!("调试：检查项目数据统计");

    let service = DashboardService::new(db_path);

    match service.repository.get_project_date_statistics() {
        Ok(_) => Ok("项目数据统计已输出到日志".to_string()),
        Err(e) => {
            error!("获取项目数据统计失败: {}", e);
            Err(format!("获取项目数据统计失败: {}", e))
        }
    }
}

/// 获取财务指标数据
#[command]
pub fn get_financial_metrics(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<FinancialMetrics, String> {
    info!("获取财务指标数据");

    let service = DashboardService::new(db_path);

    match service.get_financial_metrics(filter_params.as_ref()) {
        Ok(metrics) => Ok(metrics),
        Err(e) => {
            error!("获取财务指标数据失败: {}", e);
            Err(format!("获取财务指标数据失败: {}", e))
        }
    }
}

/// 获取人员指标数据
#[command]
pub fn get_personnel_metrics(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<PersonnelMetrics, String> {
    info!("获取人员指标数据");

    let service = DashboardService::new(db_path);

    match service.get_personnel_metrics(filter_params.as_ref()) {
        Ok(metrics) => Ok(metrics),
        Err(e) => {
            error!("获取人员指标数据失败: {}", e);
            Err(format!("获取人员指标数据失败: {}", e))
        }
    }
}

/// 获取时间线指标数据
#[command]
pub fn get_timeline_metrics(
    filter_params: Option<DashboardFilterParams>,
    db_path: String,
) -> Result<TimelineMetrics, String> {
    info!("获取时间线指标数据");

    let service = DashboardService::new(db_path);

    match service.get_timeline_metrics(filter_params.as_ref()) {
        Ok(metrics) => Ok(metrics),
        Err(e) => {
            error!("获取时间线指标数据失败: {}", e);
            Err(format!("获取时间线指标数据失败: {}", e))
        }
    }
}
