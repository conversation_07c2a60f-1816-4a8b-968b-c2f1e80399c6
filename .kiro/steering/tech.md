# Technology Stack & Build System

## Core Architecture
- **Frontend**: SvelteKit + TypeScript + Tailwind CSS
- **Backend**: Rust + Tauri (cross-platform desktop framework)
- **Databases**: SQLite (local storage) + MongoDB (cloud configuration)
- **AI Integration**: Langchain.js + OpenAI API

## Key Dependencies

### Frontend (`package.json`)
- **Framework**: `@sveltejs/kit`, `svelte`
- **UI/Styling**: `tailwindcss`, `bits-ui`, `lucide-svelte`
- **Charts**: `echarts`, `svelte-echarts`
- **AI**: `@langchain/core`, `@langchain/openai`, `langchain`
- **Tauri Integration**: `@tauri-apps/api`, `@tauri-apps/plugin-*`

### Backend (`Cargo.toml`)
- **Framework**: `tauri`, `tokio`
- **Database**: `rusqlite`, `mongodb`, `r2d2`, `r2d2_sqlite`
- **HTTP**: `reqwest`
- **Serialization**: `serde`, `serde_json`
- **Utilities**: `chrono`, `uuid`, `regex`, `dirs`

## Development Commands

### Frontend Development
```bash
npm run dev              # Start SvelteKit dev server
npm run build            # Build frontend for production
npm run preview          # Preview production build
npm run check            # TypeScript type checking
npm run check:watch      # Watch mode type checking
```

### Tauri Development
```bash
npm run tauri dev        # Start desktop app in development mode
npm run tauri build      # Build production desktop application
npm run tauri            # Access Tauri CLI commands
```

### Installation & Setup
```bash
# Install frontend dependencies
npm install
# or
pnpm install

# Database setup (auto-created on first run)
mkdir -p "/Users/<USER>/我的文档/sqlite"
```

## Build Configuration

### Frontend Config
- **Adapter**: `@sveltejs/adapter-static` (for Tauri compatibility)
- **Bundler**: Vite with SvelteKit
- **CSS**: Tailwind CSS with custom design system
- **TypeScript**: Strict mode enabled

### Backend Config
- **Target**: Cross-platform desktop (macOS, Windows, Linux)
- **Bundle Output**: 
  - macOS: `src-tauri/target/release/bundle/dmg/`
  - Windows: `src-tauri/target/release/bundle/msi/`
  - Linux: `src-tauri/target/release/bundle/deb/`

## Development Environment Requirements
- **Node.js**: v18+ recommended
- **Rust**: Latest stable version
- **Platform-specific**:
  - macOS: `xcode-select --install`
  - Windows: Visual Studio C++ Build Tools
  - Linux: `libwebkit2gtk-4.0-dev build-essential curl wget file`

## Database Paths
- **SQLite**: `/Users/<USER>/我的文档/sqlite/peckbyte.db`
- **MongoDB**: Configured via system settings UI