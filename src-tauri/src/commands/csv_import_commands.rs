use log::{error, info};
use tauri::command;

use crate::models::project_management::{
    CsvImportResult, CsvImportValidation, QualityControlResult,
};
use crate::services::csv_import_service::CsvImportService;

/// CSV导入相关命令

/// 解析并验证CSV文件内容
#[command]
pub fn parse_csv_personnel_data(
    csv_content: String,
    db_path: String,
) -> Result<CsvImportValidation, String> {
    info!("开始解析CSV人员数据");

    let service = CsvImportService::new(db_path);

    match service.parse_and_validate_csv(&csv_content) {
        Ok(validation) => {
            info!(
                "CSV解析完成: 总行数={}, 成功行数={}, 错误行数={}, 总分配数={}",
                validation.statistics.total_rows,
                validation.statistics.successful_rows,
                validation.statistics.error_rows,
                validation.statistics.total_assignments
            );
            Ok(validation)
        }
        Err(e) => {
            error!("CSV解析失败: {}", e);
            Err(format!("CSV解析失败: {}", e))
        }
    }
}

/// 执行CSV导入
#[command]
pub fn execute_csv_personnel_import(
    validation: CsvImportValidation,
    db_path: String,
) -> Result<CsvImportResult, String> {
    info!("开始执行CSV人员导入");

    let service = CsvImportService::new(db_path);

    match service.execute_import(validation) {
        Ok(result) => {
            if result.success {
                info!(
                    "CSV导入成功: 导入记录数={}, 跳过记录数={}",
                    result.imported_records, result.skipped_records
                );
            } else {
                error!("CSV导入失败: 错误数={}", result.errors.len());
            }
            Ok(result)
        }
        Err(e) => {
            error!("CSV导入执行失败: {}", e);
            Err(format!("CSV导入执行失败: {}", e))
        }
    }
}

/// 执行项目质量控制检查
#[command]
pub fn perform_project_quality_control(
    project_ids: Option<Vec<String>>,
    db_path: String,
) -> Result<Vec<QualityControlResult>, String> {
    info!("开始执行项目质量控制检查");

    let service = CsvImportService::new(db_path);

    match service.perform_quality_control(project_ids) {
        Ok(results) => {
            let failed_projects = results.iter().filter(|r| !r.quality_passed).count();
            info!(
                "质量控制检查完成: 检查项目数={}, 未通过项目数={}",
                results.len(),
                failed_projects
            );
            Ok(results)
        }
        Err(e) => {
            error!("质量控制检查失败: {}", e);
            Err(format!("质量控制检查失败: {}", e))
        }
    }
}

/// 获取CSV导入模板
#[command]
pub fn get_csv_import_template() -> Result<String, String> {
    info!("获取CSV导入模板");

    let template = "序号,项目,项目全称,启动日期,授权人员,提交人,修改人,提交时间,修改时间,填写时长,填写设备,操作系统,浏览器,填写地区,IP\n\
1,202208-平安野义-HAP-头孢地尔-III期,202208-平安野义-HAP-头孢地尔-III期,2021/12/3,\"1：（人员名称：宋文军，角色：主要研究者），2：（人员名称：段文霞，角色：临床协调员(CRC)），3：（人员名称：熊志珍，角色：CRA）\",,,2025/6/12 16:45,2025/6/12 16:45,9分2秒,Windows,Windows 10,Chrome *********,江西省,***************\n\
2,202420-健康元-哮喘-JKN23051-Ⅱa期,一项评价JKN23051在控制不佳的中、重度哮喘患者中的有效性和安全性的多中心、随机、双盲、安慰剂对照 Ⅱa期临床研究,2024/10/31,\"1：（人员名称：陈淑情，角色：主要研究者），2：（人员名称：王庭庭，角色：临床协调员(CRC)），3：（人员名称：李雨洁，角色：研究护士）\",,,2025/6/12 16:49,2025/6/12 16:49,6分4秒,iPhone,iOS 18.5,Wechat Browser 8.0.59,江西省,**************";

    Ok(template.to_string())
}

/// 验证CSV格式
#[command]
pub fn validate_csv_format(csv_content: String) -> Result<bool, String> {
    info!("验证CSV格式");

    // 基本格式验证
    let lines: Vec<&str> = csv_content.lines().collect();

    if lines.is_empty() {
        return Err("CSV文件为空".to_string());
    }

    // 检查标题行
    let header = lines[0];
    if !header.contains("项目") || !header.contains("授权人员") {
        return Err("CSV文件缺少必要的列标题: 项目, 授权人员".to_string());
    }

    // 检查数据行
    if lines.len() < 2 {
        return Err("CSV文件没有数据行".to_string());
    }

    for (index, line) in lines.iter().enumerate().skip(1) {
        if line.trim().is_empty() {
            continue;
        }

        // 简单的字段数量检查（至少需要5个字段：序号,项目,项目全称,启动日期,授权人员）
        let field_count = line.matches(',').count() + 1;
        if field_count < 5 {
            return Err(format!(
                "第 {} 行格式错误: 字段数量不足，至少需要5个字段",
                index + 1
            ));
        }

        // 检查是否包含授权人员信息的基本格式
        if !line.contains("人员名称：") || !line.contains("角色：") {
            return Err(format!(
                "第 {} 行授权人员格式错误: 应包含'人员名称：'和'角色：'",
                index + 1
            ));
        }
    }

    Ok(true)
}

/// 获取导入历史记录
#[command]
pub fn get_import_history(
    _limit: Option<usize>,
    _db_path: String,
) -> Result<Vec<ImportHistoryRecord>, String> {
    info!("获取导入历史记录");

    // 这里可以实现导入历史记录的查询逻辑
    // 暂时返回空列表
    Ok(Vec::new())
}

/// 导入历史记录结构
#[derive(serde::Serialize, serde::Deserialize)]
pub struct ImportHistoryRecord {
    pub id: i64,
    pub import_time: String,
    pub file_name: String,
    pub total_records: usize,
    pub successful_records: usize,
    pub failed_records: usize,
    pub status: String,
}

/// 批量删除人员角色分配
#[command]
pub fn batch_delete_personnel_assignments(
    project_id: String,
    assignment_ids: Vec<i64>,
    db_path: String,
) -> Result<usize, String> {
    info!(
        "批量删除人员角色分配: 项目={}, 分配数={}",
        project_id,
        assignment_ids.len()
    );

    use rusqlite::{params, Connection};

    let conn = Connection::open(&db_path).map_err(|e| format!("连接数据库失败: {}", e))?;

    // 开始事务
    conn.execute("BEGIN TRANSACTION", [])
        .map_err(|e| format!("开始事务失败: {}", e))?;

    let mut deleted_count = 0;

    for assignment_id in assignment_ids {
        let result = conn.execute(
            "DELETE FROM project_personnel_roles WHERE assignment_id = ? AND project_id = ?",
            params![assignment_id, project_id],
        );

        match result {
            Ok(count) => {
                deleted_count += count;
            }
            Err(e) => {
                // 回滚事务
                conn.execute("ROLLBACK", []).ok();
                return Err(format!("删除分配记录失败: {}", e));
            }
        }
    }

    // 提交事务
    conn.execute("COMMIT", [])
        .map_err(|e| format!("提交事务失败: {}", e))?;

    info!("成功删除 {} 条人员角色分配记录", deleted_count);
    Ok(deleted_count)
}

/// 获取项目人员统计信息
#[command]
pub fn get_project_personnel_statistics(
    project_id: Option<String>,
    db_path: String,
) -> Result<PersonnelStatistics, String> {
    info!("获取项目人员统计信息");

    use rusqlite::{params, Connection};

    let conn = Connection::open(&db_path).map_err(|e| format!("连接数据库失败: {}", e))?;

    let (total_assignments, unique_personnel, unique_roles) = if let Some(pid) = project_id {
        // 单个项目统计
        let total: i64 = conn
            .query_row(
                "SELECT COUNT(*) FROM project_personnel_roles WHERE project_id = ?",
                params![pid],
                |row| row.get(0),
            )
            .map_err(|e| format!("查询总分配数失败: {}", e))?;

        let personnel: i64 = conn.query_row(
            "SELECT COUNT(DISTINCT personnel_id) FROM project_personnel_roles WHERE project_id = ?",
            params![pid],
            |row| row.get(0),
        ).map_err(|e| format!("查询人员数失败: {}", e))?;

        let roles: i64 = conn.query_row(
            "SELECT COUNT(DISTINCT role_item_id) FROM project_personnel_roles WHERE project_id = ?",
            params![pid],
            |row| row.get(0),
        ).map_err(|e| format!("查询角色数失败: {}", e))?;

        (total as usize, personnel as usize, roles as usize)
    } else {
        // 全局统计
        let total: i64 = conn
            .query_row("SELECT COUNT(*) FROM project_personnel_roles", [], |row| {
                row.get(0)
            })
            .map_err(|e| format!("查询总分配数失败: {}", e))?;

        let personnel: i64 = conn
            .query_row(
                "SELECT COUNT(DISTINCT personnel_id) FROM project_personnel_roles",
                [],
                |row| row.get(0),
            )
            .map_err(|e| format!("查询人员数失败: {}", e))?;

        let roles: i64 = conn
            .query_row(
                "SELECT COUNT(DISTINCT role_item_id) FROM project_personnel_roles",
                [],
                |row| row.get(0),
            )
            .map_err(|e| format!("查询角色数失败: {}", e))?;

        (total as usize, personnel as usize, roles as usize)
    };

    Ok(PersonnelStatistics {
        total_assignments,
        unique_personnel,
        unique_roles,
    })
}

/// 人员统计信息结构
#[derive(serde::Serialize, serde::Deserialize)]
pub struct PersonnelStatistics {
    pub total_assignments: usize,
    pub unique_personnel: usize,
    pub unique_roles: usize,
}
