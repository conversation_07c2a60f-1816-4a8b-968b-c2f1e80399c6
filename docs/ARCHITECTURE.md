# 系统架构文档

本文档详细说明临床研究项目管理系统的技术架构设计和实现原理。

## 🏗️ 整体架构

系统采用现代化的分层架构设计，前后端分离，确保代码的组织性、可维护性和可扩展性。

### 架构概览

```
┌─────────────────────────────────────────────────────────┐
│                    前端层 (SvelteKit)                    │
├─────────────────────────────────────────────────────────┤
│  页面路由层    │  组件层      │  服务层    │  状态管理    │
│  (Routes)     │ (Components) │ (Services) │ (Stores)    │
└─────────────────────────────────────────────────────────┘
                            ↕ Tauri IPC
┌─────────────────────────────────────────────────────────┐
│                    后端层 (Rust/Tauri)                   │
├─────────────────────────────────────────────────────────┤
│  命令层       │  服务层      │  仓储层    │  模型层      │
│ (Commands)    │ (Services)   │ (Repos)    │ (Models)    │
└─────────────────────────────────────────────────────────┘
                            ↕
┌─────────────────────────────────────────────────────────┐
│                      数据层                             │
├─────────────────────────────────────────────────────────┤
│  SQLite       │  MongoDB     │  浏览器存储 │  外部 API    │
│ (本地数据)     │ (云端配置)    │ (用户偏好)  │ (集成服务)   │
└─────────────────────────────────────────────────────────┘
```

## 🔧 前端架构 (SvelteKit)

### 技术特点
- 基于 SvelteKit 的现代化前端架构
- 采用文件系统路由和组件化设计
- TypeScript 类型安全
- Tailwind CSS 实用优先的样式系统

### 核心分层

#### 1. 页面路由层 (`src/routes/`)
- **职责**: 定义应用的页面结构和路由逻辑
- **特点**: 基于文件系统的自动路由生成
- **组织**: 按功能模块组织路由目录

#### 2. 组件层 (`src/lib/components/`)
- **设计原则**: 遵循原子设计原则，组件职责单一
- **分类**:
  - **基础组件** (`ui/`): 通用 UI 组件
  - **业务组件**: 特定功能的组件库
  - **复合组件**: 由多个基础组件组合的复杂组件

#### 3. 服务层 (`src/lib/services/`)
- **职责**: 封装后端 API 调用和业务逻辑
- **特点**: 
  - 统一的错误处理
  - 请求响应拦截
  - 数据格式转换

#### 4. 状态管理 (`src/lib/stores/`)
- **技术**: Svelte stores
- **分类**:
  - **全局状态**: 应用级别的状态管理
  - **页面状态**: 页面级别的状态管理
  - **组件状态**: 组件内部状态

### 数据流

```
用户交互 → 组件事件 → 服务层调用 → Tauri 命令 → 后端处理 → 数据返回 → 状态更新 → UI 重渲染
```

## ⚙️ 后端架构 (Rust + Tauri)

### 技术特点
- 基于 Rust 的高性能后端
- 采用分层架构和仓储模式
- 统一的错误处理机制
- 类型安全的数据处理

### 分层架构设计

#### 1. 命令层 (Commands) - `src-tauri/src/commands/`
- **职责**: 定义 Tauri 命令，作为前端调用的 API 端点
- **功能**:
  - 请求参数验证
  - 响应格式化
  - 错误处理
- **规范**: 使用 `#[tauri::command]` 宏标记

#### 2. 服务层 (Services) - `src-tauri/src/services/`
- **职责**: 实现核心业务逻辑
- **功能**:
  - 协调多个仓储层操作
  - 处理事务管理
  - 业务规则验证
- **设计**: 面向接口编程，易于测试

#### 3. 仓储层 (Repositories) - `src-tauri/src/repositories/`
- **职责**: 封装数据库访问逻辑
- **特点**:
  - 提供统一的数据操作接口
  - 支持 SQLite 和 MongoDB 双后端
  - 抽象化数据存储细节

#### 4. 模型层 (Models) - `src-tauri/src/models/`
- **职责**: 定义数据结构和类型
- **功能**:
  - 数据验证
  - 序列化/反序列化
  - 类型转换

### 核心模块

#### 错误处理 (`error.rs`)
```rust
pub enum AppError {
    Database(DatabaseError),
    Validation(ValidationError),
    External(ExternalError),
    Internal(String),
}
```

#### 统一响应 (`response.rs`)
```rust
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}
```

## 💾 数据存储架构

### 多层次存储策略
根据数据特性和使用场景，采用不同的存储方案：

#### 1. SQLite 本地数据库
- **用途**: 存储核心业务数据
- **特点**: 高性能本地存储，支持复杂查询和事务
- **数据类型**: 项目信息、人员数据、入排标准规则等
- **位置**: `/Users/<USER>/我的文档/sqlite/peckbyte.db`

#### 2. MongoDB 云端存储
- **用途**: 存储系统配置、API 密钥等敏感信息
- **特点**: 支持分布式部署，便于配置同步
- **数据类型**: 应用配置、外部服务密钥

#### 3. 浏览器本地存储
- **用途**: 存储用户界面偏好设置
- **实现**: 通过 Svelte stores 管理状态
- **数据类型**: 主题设置、布局偏好

#### 4. 外部存储 (Notion)
- **用途**: 智能笔记功能的持久化层
- **管理**: 通过 Notion 集成模块

### 数据访问模式

```
应用层 → 仓储接口 → 具体实现 → 数据库
```

## 🤖 AI 功能架构

### Langchain.js 集成框架

#### 核心组件
- **模型管理**: 支持 OpenAI GPT 系列模型
- **提示工程**: 结构化的提示模板系统
- **链式处理**: LCEL (LangChain Expression Language) 模式
- **输出解析**: JSON 结构化输出处理

#### 实现策略
```javascript
// 笔记分类链
const classificationChain = promptTemplate
  .pipe(model)
  .pipe(outputParser);
```

#### 应用场景
- 智能笔记分类
- 入排标准自动生成
- 内容摘要提取
- 语义搜索

### 配置管理
- API 密钥通过系统配置模块安全管理
- 支持多种模型和参数动态配置
- 实时模型切换和性能优化

## 🔗 外部系统集成架构

### Notion 集成
```
应用 → Notion Service → Notion API → Notion Database
```

### Lighthouse API 集成
```
应用 → HTTP Client → Lighthouse API → 临床数据系统
```

### 通用集成模式
- HTTP 代理模式
- API 密钥管理
- 错误重试机制
- 连接状态检测

## 🔒 安全架构

### 数据安全
- 敏感信息加密存储
- API 密钥安全管理
- 本地数据访问控制

### 网络安全
- HTTPS 通信
- API 请求验证
- 超时和重试机制

### 应用安全
- Tauri 权限系统
- 文件系统访问控制
- 进程隔离

## 📈 性能优化

### 前端优化
- 组件懒加载
- 虚拟列表
- 图片优化
- 缓存策略

### 后端优化
- 数据库索引
- 连接池管理
- 异步处理
- 内存管理

### 渲染优化
- Svelte 编译时优化
- CSS 打包优化
- 资源压缩

## 🧪 测试架构

### 测试策略
- **单元测试**: 核心业务逻辑
- **集成测试**: API 端点验证
- **E2E 测试**: 完整用户流程

### 测试工具
- Rust: `cargo test`
- TypeScript: Jest/Vitest
- E2E: Playwright

## 🚀 部署架构

### 构建流程
```
源码 → 前端构建 → Rust 编译 → 打包 → 分发
```

### 平台支持
- macOS (DMG)
- Windows (MSI)
- Linux (DEB/AppImage)

### 更新机制
- Tauri 内置更新器
- 增量更新支持
- 版本管理

---

这个架构设计确保了系统的可扩展性、可维护性和高性能，为临床研究项目管理提供了稳定可靠的技术基础。