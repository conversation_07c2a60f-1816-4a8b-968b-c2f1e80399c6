<script context="module" lang="ts">
  // 选项类型
  export interface SelectOption {
    value: string;
    label: string;
    searchText?: string; // 用于搜索的额外文本
  }
</script>

<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { ChevronDown, Search, X } from 'lucide-svelte';

  // Props
  export let options: SelectOption[] = [];
  export let value: string = '';
  export let placeholder: string = '请选择...';
  export let searchPlaceholder: string = '搜索...';
  export let disabled: boolean = false;
  export let required: boolean = false;
  export let emptyMessage: string = '暂无选项';
  export let noResultsMessage: string = '未找到匹配项';
  export let loading: boolean = false;
  export let loadingMessage: string = '加载中...';

  // 事件分发器
  const dispatch = createEventDispatcher<{
    change: string;
  }>();

  // 组件状态
  let isOpen = false;
  let searchTerm = '';
  let filteredOptions: SelectOption[] = [];
  let highlightedIndex = -1;
  let dropdownElement: HTMLDivElement;
  let searchInputElement: HTMLInputElement;

  // 计算选中的选项
  $: selectedOption = options.find(option => option.value === value);

  // 过滤选项
  $: {
    if (searchTerm.trim() === '') {
      filteredOptions = options;
    } else {
      const term = searchTerm.toLowerCase();
      filteredOptions = options.filter(option => 
        option.label.toLowerCase().includes(term) ||
        option.value.toLowerCase().includes(term) ||
        (option.searchText && option.searchText.toLowerCase().includes(term))
      );
    }
    highlightedIndex = -1;
  }

  // 打开下拉框
  function openDropdown() {
    if (disabled) return;
    isOpen = true;
    searchTerm = '';
    setTimeout(() => {
      searchInputElement?.focus();
    }, 0);
  }

  // 关闭下拉框
  function closeDropdown() {
    isOpen = false;
    searchTerm = '';
    highlightedIndex = -1;
  }

  // 选择选项
  function selectOption(option: SelectOption) {
    value = option.value;
    dispatch('change', option.value);
    closeDropdown();
  }

  // 清除选择
  function clearSelection() {
    value = '';
    dispatch('change', '');
  }

  // 键盘导航
  function handleKeydown(event: KeyboardEvent) {
    if (!isOpen) {
      if (event.key === 'Enter' || event.key === ' ' || event.key === 'ArrowDown') {
        event.preventDefault();
        openDropdown();
      }
      return;
    }

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        closeDropdown();
        break;
      case 'ArrowDown':
        event.preventDefault();
        highlightedIndex = Math.min(highlightedIndex + 1, filteredOptions.length - 1);
        scrollToHighlighted();
        break;
      case 'ArrowUp':
        event.preventDefault();
        highlightedIndex = Math.max(highlightedIndex - 1, -1);
        scrollToHighlighted();
        break;
      case 'Enter':
        event.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
          selectOption(filteredOptions[highlightedIndex]);
        }
        break;
    }
  }

  // 滚动到高亮项
  function scrollToHighlighted() {
    if (highlightedIndex >= 0 && dropdownElement) {
      const highlightedElement = dropdownElement.querySelector(`[data-index="${highlightedIndex}"]`) as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }

  // 点击外部关闭
  function handleClickOutside(event: MouseEvent) {
    if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
      closeDropdown();
    }
  }

  // 高亮匹配文本
  function highlightMatch(text: string, searchTerm: string): string {
    if (!searchTerm.trim()) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
  }

  onMount(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
</script>

<div class="relative" bind:this={dropdownElement}>
  <!-- 触发器 -->
  <div class="relative">
    <button
      type="button"
      class="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed"
      class:border-red-500={required && !value}
      {disabled}
      onclick={openDropdown}
      onkeydown={handleKeydown}
      aria-haspopup="listbox"
      aria-expanded={isOpen}
    >
      <div class="flex items-center justify-between">
        <span class="block truncate" class:text-gray-500={!selectedOption}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <ChevronDown
          class={`h-4 w-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </div>
    </button>

    {#if value && !disabled}
      <button
        type="button"
        class="absolute right-8 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
        onclick={(e) => { e.stopPropagation(); clearSelection(); }}
        aria-label="清除选择"
      >
        <X class="h-3 w-3 text-gray-400" />
      </button>
    {/if}
  </div>

  <!-- 下拉框 -->
  {#if isOpen}
    <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-hidden">
      <!-- 搜索框 -->
      <div class="p-2 border-b border-gray-200">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            bind:this={searchInputElement}
            bind:value={searchTerm}
            type="text"
            class="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder={searchPlaceholder}
            onkeydown={handleKeydown}
          />
        </div>
      </div>

      <!-- 选项列表 -->
      <div class="max-h-48 overflow-y-auto">
        {#if loading}
          <div class="px-3 py-2 text-sm text-gray-500 text-center">
            {loadingMessage}
          </div>
        {:else if filteredOptions.length === 0}
          <div class="px-3 py-2 text-sm text-gray-500 text-center">
            {searchTerm.trim() ? noResultsMessage : emptyMessage}
          </div>
        {:else}
          {#each filteredOptions as option, index}
            <button
              type="button"
              class="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
              class:bg-blue-50={highlightedIndex === index}
              class:text-blue-600={highlightedIndex === index}
              class:bg-blue-100={option.value === value}
              class:text-blue-700={option.value === value}
              data-index={index}
              onclick={() => selectOption(option)}
              onmouseenter={() => highlightedIndex = index}
            >
              {@html highlightMatch(option.label, searchTerm)}
            </button>
          {/each}
        {/if}
      </div>
    </div>
  {/if}
</div>

<style>
  :global(mark) {
    background-color: #fef08a;
    padding: 0;
  }
</style>
