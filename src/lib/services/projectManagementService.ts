import { invoke } from '@tauri-apps/api/core';

// 项目模型
export interface Project {
  project_id?: string;
  project_name: string;
  project_short_name: string;
  project_path?: string;
  disease_item_id?: number;
  project_stage_item_id?: number;
  project_status_item_id?: number;
  recruitment_status_item_id?: number;
  contract_case_total?: number;
  project_start_date?: string;
  last_updated?: string;
  drug_mechanism?: string;
  drug_mechanism_item_id?: number;  // 药物作用机制字典ID
  drug_introduction?: string;
  is_pinned?: boolean;
  pinned_at?: string;
  pinned_rank?: number;
}

// 项目申办方模型
export interface ProjectSponsor {
  id?: number;
  project_id: string;
  sponsor_item_id: number;
}

// 研究药物模型
export interface ResearchDrug {
  drug_info_id?: number;
  project_id: string;
  research_drug: string;
  // 药物类型和基本信息
  drug_type_item_id?: number;            // 药物类型：研究药物/对照药物/安慰剂
  drug_classification_item_id?: number;  // 药物分类（关联字典）
  usage_method_item_id?: number;         // 用药方法（关联字典）
  usage_frequency_item_id?: number;      // 用药频率（关联字典）
  mechanism_of_action_item_id?: number;  // 药物作用机制（关联字典）
  dosage?: string;                       // 剂量信息
  share?: number;                        // 份额/占比
  drug_characteristics?: string;         // 药物特性描述
  notes?: string;                        // 其他备注信息
}

// 带详情的研究药物模型
export interface ResearchDrugWithDetails extends ResearchDrug {
  // 关联的字典项详情
  drug_type?: DictionaryItem;            // 药物类型详情
  drug_classification?: DictionaryItem;  // 药物分类详情
  usage_method?: DictionaryItem;         // 用药方法详情
  usage_frequency?: DictionaryItem;      // 用药频率详情
  mechanism_of_action?: DictionaryItem;  // 药物作用机制详情
}

// 药物分组模型
export interface DrugGroup {
  group_id?: number;
  project_id: string;
  drug_name: string;
  share: number;
}

// 项目人员角色模型
export interface ProjectPersonnelRole {
  assignment_id?: number;
  project_id: string;
  personnel_id: number;
  role_item_id: number;
}

// 补贴方案模型
export interface SubsidyScheme {
  scheme_id?: number;
  project_id: string;
  scheme_name: string;
  total_amount: number;
  included_subsidies?: number[]; // 包含的补贴项ID列表
}

// 补贴项模型
export interface Subsidy {
  subsidy_item_id?: number;
  project_id: string;
  subsidy_type_item_id: number;
  unit_amount: number;
  total_units: number;
  unit_item_id: number;
  total_amount: number;
  last_updated?: string;
}

// 字典项
export interface DictionaryItem {
  item_id: number;
  dictionary_id: number;
  item_key: string;
  item_value: string;
  item_description?: string;
  status?: string;
}

// 人员信息
export interface Personnel {
  id: number;
  name: string;
  gender?: string;
  birthday?: string;
  phone?: string;
  email?: string;
  position_item_id?: number;
  isPI?: boolean;
  organization?: string;
}

// 带详情的项目申办方
export interface ProjectSponsorWithDetails extends ProjectSponsor {
  sponsor?: DictionaryItem;
}

// 带详情的项目人员
export interface ProjectPersonnelWithDetails extends ProjectPersonnelRole {
  personnel?: Personnel;
  role?: DictionaryItem;
}

// 带详情的补贴项
export interface SubsidyWithDetails extends Subsidy {
  subsidy_type?: DictionaryItem;
  unit?: DictionaryItem;
}

// 入组/排除标准统计信息
export interface CriteriaStats {
  inclusion_count: number;    // 入组标准数量
  exclusion_count: number;    // 排除标准数量
  total_count: number;        // 总标准数量
  has_criteria: boolean;      // 是否配置了标准
}

// 项目详情（包含关联数据）
export interface ProjectWithDetails {
  project: Project;
  disease?: DictionaryItem;
  project_stage?: DictionaryItem;
  project_status?: DictionaryItem;
  recruitment_status?: DictionaryItem;
  sponsors?: ProjectSponsorWithDetails[];
  research_drugs?: ResearchDrugWithDetails[];
  drug_groups?: DrugGroup[];
  personnel?: ProjectPersonnelWithDetails[];
  subsidy_schemes?: SubsidyScheme[];
  subsidies?: SubsidyWithDetails[];
  criteria?: any[]; // 项目入排标准
  criteria_stats?: CriteriaStats;  // 入组/排除标准统计信息
  has_recruitment_companies?: boolean;  // 是否有招募公司政策
}

// 项目查询参数
export interface ProjectQuery {
  name?: string;
  disease_item_id?: number;
  project_stage_item_id?: number;
  project_status_item_id?: number;
  recruitment_status_item_id?: number;
  // 新增：申办方筛选（字典项ID列表）
  sponsor_item_ids?: number[];
  // 新增：PI 人员筛选（人员ID列表）
  pi_personnel_ids?: number[];
  only_pinned?: boolean;
  include_pinned?: boolean;
  staff_id?: number;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: string;
}

// 项目导出查询参数
export interface ProjectExportQuery {
  project_status_item_id?: number;
  recruitment_status_item_id?: number;
  disease_item_id?: number;
  project_stage_item_id?: number;
  project_ids?: string[];
  include_criteria?: boolean;
}

// 项目分页结果
export interface ProjectPagination {
  items: ProjectWithDetails[];
  total: number;
  page: number;
  page_size: number;
}

// 创建项目请求
export interface CreateProjectRequest {
  project: Project;
  sponsors?: ProjectSponsor[];
  research_drugs?: ResearchDrug[];
  drug_groups?: DrugGroup[];
  personnel?: ProjectPersonnelRole[];
  subsidy_schemes?: SubsidyScheme[];
  subsidies?: Subsidy[];
}

// 更新项目请求
export interface UpdateProjectRequest {
  project: Project;
  sponsors?: ProjectSponsor[];
  research_drugs?: ResearchDrug[];
  drug_groups?: DrugGroup[];
  personnel?: ProjectPersonnelRole[];
  subsidy_schemes?: SubsidyScheme[];
  subsidies?: Subsidy[];
}

// SQLite 数据库路径
const DB_PATH = '/Users/<USER>/我的文档/sqlite/peckbyte.db';

// 项目导出数据
export interface ProjectExportData extends ProjectWithDetails {
  // 扩展字段，用于导出
  export_date: string;
  export_version: string;
}

// 项目管理服务
export const projectManagementService = {
  // 初始化项目管理相关表
  async initTables(): Promise<void> {
    try {
      await invoke('init_project_management_tables', { dbPath: DB_PATH });
    } catch (error) {
      console.error('初始化项目管理相关表失败:', error);
      throw error;
    }
  },

  // 获取项目列表
  async getProjects(query: ProjectQuery = {}): Promise<ProjectPagination> {
    try {
      return await invoke('get_projects_list', {
        query: {
          name: query.name,
          disease_item_id: query.disease_item_id,
          project_stage_item_id: query.project_stage_item_id,
          project_status_item_id: query.project_status_item_id,
          recruitment_status_item_id: query.recruitment_status_item_id,
          sponsor_item_ids: query.sponsor_item_ids,
          pi_personnel_ids: query.pi_personnel_ids,
          only_pinned: query.only_pinned,
          include_pinned: query.include_pinned,
          staff_id: query.staff_id,
          page: query.page || 1,
          page_size: query.page_size || 10,
          sort_by: query.sort_by || 'project_short_name',
          sort_order: query.sort_order || 'desc'
        },
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目列表失败:', error);
      throw error;
    }
  },

  // 获取项目详情
  async getProjectDetails(projectId: string): Promise<ProjectWithDetails | null> {
    try {
      // 确保 projectId 是有效的
      if (!projectId || projectId === 'undefined' || projectId === 'null') {
        throw new Error('无效的项目ID');
      }

      console.log('调用 get_project_details 命令，参数:', { projectId, dbPath: DB_PATH });
      const result = await invoke<ProjectWithDetails | null>('get_project_details', {
        projectId,
        dbPath: DB_PATH
      });
      return result;
    } catch (error) {
      console.error('获取项目详情失败:', error);
      throw error;
    }
  },

  // 创建项目
  async createProject(project: Project): Promise<string> {
    try {
      return await invoke<string>('pm_create_project', {
        project,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('创建项目失败:', error);
      throw error;
    }
  },

  // 更新项目
  async updateProject(project: Project): Promise<void> {
    try {
      await invoke('pm_update_project', {
        project,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('更新项目失败:', error);
      throw error;
    }
  },

  // 保存项目完整信息
  async saveProjectWithDetails(
    project: Project,
    sponsors?: ProjectSponsor[],
    research_drugs?: ResearchDrug[],
    drug_groups?: DrugGroup[],
    personnel?: ProjectPersonnelRole[],
    subsidy_schemes?: SubsidyScheme[],
    subsidies?: Subsidy[]
  ): Promise<string> {
    try {
      // 确保所有 key 都使用 camelCase 以匹配 Tauri 的参数检查器
      const payload = {
        project: project,
        sponsors: sponsors || null,
        researchDrugs: research_drugs || null,
        drugGroups: drug_groups || null,
        personnel: personnel || null,
        subsidySchemes: subsidy_schemes || [],
        subsidies: subsidies || null,
        dbPath: DB_PATH
      };
      console.log("[projectManagementService] Payload being sent to invoke:", JSON.stringify(payload));
      return await invoke<string>('save_project_with_details', payload);
    } catch (error) {
      console.error('保存项目完整信息失败:', error);
      throw error;
    }
  },

  // 删除项目
  async deleteProject(projectId: string): Promise<void> {
    try {
      await invoke('pm_delete_project', {
        projectId,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('删除项目失败:', error);
      throw error;
    }
  },

  // 切换项目置顶状态
  async toggleProjectPin(
    projectId: string,
    staffId: number,
    pinned: boolean,
    pinnedRank?: number
  ): Promise<void> {
    try {
      await invoke('toggle_project_pin', {
        projectId,
        staffId,
        pinned,
        pinnedRank: pinnedRank ?? null,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('更新项目置顶状态失败:', error);
      throw error;
    }
  },

  // 调整置顶项目顺序
  async reorderProjectPins(staffId: number, projectIds: string[]): Promise<void> {
    try {
      await invoke('reorder_project_pins', {
        staffId,
        projectIds,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('调整置顶项目顺序失败:', error);
      throw error;
    }
  },

  // 检查数据库表和数据
  async checkDatabaseTables(): Promise<string> {
    try {
      return await invoke<string>('check_database_tables', {
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('检查数据库表和数据失败:', error);
      throw error;
    }
  },

  // 重置数据库表
  async resetDatabaseTables(): Promise<string> {
    try {
      return await invoke<string>('reset_database_tables', {
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('重置数据库表失败:', error);
      throw error;
    }
  },

  // 导出项目完整数据（包含入排标准）
  async exportProjectData(projectId: string): Promise<ProjectExportData | null> {
    try {
      // 确保 projectId 是有效的
      if (!projectId || projectId === 'undefined' || projectId === 'null') {
        throw new Error('无效的项目ID');
      }

      console.log('导出项目数据，项目ID:', projectId);

      // 1. 获取项目基本信息和关联数据
      const projectDetails = await this.getProjectDetails(projectId);
      if (!projectDetails) {
        throw new Error('项目不存在');
      }

      // 2. 从 ruleDesignerService 获取项目入排标准
      const inclusionQuery = { project_id: projectId, criterion_type: 'inclusion' };
      const exclusionQuery = { project_id: projectId, criterion_type: 'exclusion' };

      try {
        // 导入 ruleDesignerService
        const { ruleDesignerService } = await import('./ruleDesignerService');

        // 获取入组标准
        const inclusionCriteria = await ruleDesignerService.getProjectCriteria(inclusionQuery);

        // 获取排除标准
        const exclusionCriteria = await ruleDesignerService.getProjectCriteria(exclusionQuery);

        // 3. 组合所有数据
        const exportData: ProjectExportData = {
          ...projectDetails,
          criteria: [
            ...inclusionCriteria,
            ...exclusionCriteria
          ],
          export_date: new Date().toISOString(),
          export_version: '1.0.0'
        };

        return exportData;
      } catch (criteriaError) {
        console.warn('获取入排标准失败，将导出不包含入排标准的项目数据:', criteriaError);

        // 如果获取入排标准失败，仍然导出项目基本数据
        const exportData: ProjectExportData = {
          ...projectDetails,
          criteria: [],
          export_date: new Date().toISOString(),
          export_version: '1.0.0'
        };

        return exportData;
      }
    } catch (error) {
      console.error('导出项目数据失败:', error);
      throw error;
    }
  },

  // 获取项目导出数据
  async getProjectsExportData(query: ProjectExportQuery = {}): Promise<ProjectWithDetails[]> {
    try {
      return await invoke('get_projects_export_data', {
        query: {
          project_status_item_id: query.project_status_item_id,
          recruitment_status_item_id: query.recruitment_status_item_id,
          disease_item_id: query.disease_item_id,
          project_stage_item_id: query.project_stage_item_id,
          project_ids: query.project_ids,
          include_criteria: query.include_criteria
        },
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目导出数据失败:', error);
      throw error;
    }
  },

  // 获取项目完整导出数据（包含入排标准）
  async getProjectsCompleteExportData(query: ProjectExportQuery = {}): Promise<any[]> {
    try {
      return await invoke('get_projects_complete_export_data', {
        query: {
          project_status_item_id: query.project_status_item_id,
          recruitment_status_item_id: query.recruitment_status_item_id,
          disease_item_id: query.disease_item_id,
          project_stage_item_id: query.project_stage_item_id,
          project_ids: query.project_ids,
          include_criteria: query.include_criteria ?? true
        },
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目完整导出数据失败:', error);
      throw error;
    }
  },

  // 批量导出指定项目
  async exportSelectedProjects(projectIds: string[], includeCriteria: boolean = true): Promise<any[]> {
    try {
      return await this.getProjectsCompleteExportData({
        project_ids: projectIds,
        include_criteria: includeCriteria
      });
    } catch (error) {
      console.error('批量导出项目失败:', error);
      throw error;
    }
  }
};
