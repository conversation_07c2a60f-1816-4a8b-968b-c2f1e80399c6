use crate::models::filter_config::{
    CreateFilterConfigRequest, FilterCondition, FilterConfig, UpdateFilterConfigRequest,
};
use chrono::Utc;
use rusqlite::{params, Connection, Result};
use uuid::Uuid;

pub struct FilterConfigRepository;

impl FilterConfigRepository {
    pub fn init_tables(conn: &Connection) -> Result<()> {
        conn.execute(
            "CREATE TABLE IF NOT EXISTS filter_configs (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                conditions TEXT NOT NULL, -- JSON string
                is_default INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )",
            [],
        )?;

        Ok(())
    }

    pub fn get_all(conn: &Connection) -> Result<Vec<FilterConfig>> {
        let mut stmt = conn.prepare(
            "SELECT id, name, description, conditions, is_default, created_at, updated_at 
             FROM filter_configs 
             ORDER BY created_at DESC",
        )?;

        let filter_iter = stmt.query_map([], |row| {
            let conditions_json: String = row.get(3)?;
            let conditions: Vec<FilterCondition> =
                serde_json::from_str(&conditions_json).map_err(|e| {
                    rusqlite::Error::InvalidColumnType(
                        3,
                        "conditions".to_string(),
                        rusqlite::types::Type::Text,
                    )
                })?;

            Ok(FilterConfig {
                id: Some(row.get(0)?),
                name: row.get(1)?,
                description: row.get(2)?,
                conditions,
                is_default: Some(row.get::<_, i32>(4)? == 1),
                created_at: Some(row.get(5)?),
                updated_at: Some(row.get(6)?),
            })
        })?;

        let mut configs = Vec::new();
        for config in filter_iter {
            configs.push(config?);
        }

        Ok(configs)
    }

    pub fn get_by_id(conn: &Connection, id: &str) -> Result<Option<FilterConfig>> {
        let mut stmt = conn.prepare(
            "SELECT id, name, description, conditions, is_default, created_at, updated_at 
             FROM filter_configs 
             WHERE id = ?",
        )?;

        let mut filter_iter = stmt.query_map([id], |row| {
            let conditions_json: String = row.get(3)?;
            let conditions: Vec<FilterCondition> =
                serde_json::from_str(&conditions_json).map_err(|e| {
                    rusqlite::Error::InvalidColumnType(
                        3,
                        "conditions".to_string(),
                        rusqlite::types::Type::Text,
                    )
                })?;

            Ok(FilterConfig {
                id: Some(row.get(0)?),
                name: row.get(1)?,
                description: row.get(2)?,
                conditions,
                is_default: Some(row.get::<_, i32>(4)? == 1),
                created_at: Some(row.get(5)?),
                updated_at: Some(row.get(6)?),
            })
        })?;

        match filter_iter.next() {
            Some(config) => Ok(Some(config?)),
            None => Ok(None),
        }
    }

    pub fn create(conn: &Connection, request: CreateFilterConfigRequest) -> Result<FilterConfig> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now().to_rfc3339();
        let conditions_json = serde_json::to_string(&request.conditions).map_err(|e| {
            rusqlite::Error::InvalidColumnType(
                0,
                "conditions".to_string(),
                rusqlite::types::Type::Text,
            )
        })?;

        conn.execute(
            "INSERT INTO filter_configs (id, name, description, conditions, is_default, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            params![
                id,
                request.name,
                request.description,
                conditions_json,
                request.is_default.unwrap_or(false) as i32,
                now,
                now
            ],
        )?;

        Ok(FilterConfig {
            id: Some(id),
            name: request.name,
            description: request.description,
            conditions: request.conditions,
            is_default: request.is_default,
            created_at: Some(now.clone()),
            updated_at: Some(now),
        })
    }

    pub fn update(
        conn: &Connection,
        id: &str,
        request: UpdateFilterConfigRequest,
    ) -> Result<FilterConfig> {
        // 首先获取现有配置
        let existing =
            Self::get_by_id(conn, id)?.ok_or_else(|| rusqlite::Error::QueryReturnedNoRows)?;

        let now = Utc::now().to_rfc3339();
        let name = request.name.unwrap_or(existing.name);
        let description = request.description.or(existing.description);
        let conditions = request.conditions.unwrap_or(existing.conditions);
        let is_default = request.is_default.or(existing.is_default);

        let conditions_json = serde_json::to_string(&conditions).map_err(|e| {
            rusqlite::Error::InvalidColumnType(
                0,
                "conditions".to_string(),
                rusqlite::types::Type::Text,
            )
        })?;

        conn.execute(
            "UPDATE filter_configs 
             SET name = ?, description = ?, conditions = ?, is_default = ?, updated_at = ?
             WHERE id = ?",
            params![
                name,
                description,
                conditions_json,
                is_default.unwrap_or(false) as i32,
                now,
                id
            ],
        )?;

        Ok(FilterConfig {
            id: Some(id.to_string()),
            name,
            description,
            conditions,
            is_default,
            created_at: existing.created_at,
            updated_at: Some(now),
        })
    }

    pub fn delete(conn: &Connection, id: &str) -> Result<()> {
        conn.execute("DELETE FROM filter_configs WHERE id = ?", [id])?;
        Ok(())
    }
}
