<script context="module" lang="ts">
  export interface MultiSelectOption {
    value: any;
    label: string;
    searchText?: string;
  }
</script>

<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { ChevronDown, Search, X, Check } from 'lucide-svelte';

  export let options: MultiSelectOption[] = [];
  export let selectedValues: any[] = [];
  export let placeholder: string = '请选择...';
  export let searchPlaceholder: string = '搜索...';
  export let disabled: boolean = false;
  export let emptyMessage: string = '暂无选项';
  export let noResultsMessage: string = '未找到匹配项';
  export let loading: boolean = false;
  export let loadingMessage: string = '加载中...';

  const dispatch = createEventDispatcher<{ change: any[] }>();

  let isOpen = false;
  let searchTerm = '';
  let filteredOptions: MultiSelectOption[] = [];
  let dropdownElement: HTMLDivElement;
  let searchInputElement: HTMLInputElement;

  // 过滤选项（支持模糊匹配 label/value/searchText）
  $: {
    const term = searchTerm.trim().toLowerCase();
    if (!term) {
      filteredOptions = options;
    } else {
      filteredOptions = options.filter(opt => {
        const val = String(opt.value ?? '').toLowerCase();
        const lbl = String(opt.label ?? '').toLowerCase();
        const stx = String(opt.searchText ?? '').toLowerCase();
        return lbl.includes(term) || val.includes(term) || stx.includes(term);
      });
    }
  }

  function openDropdown() {
    if (disabled) return;
    isOpen = true;
    setTimeout(() => searchInputElement?.focus(), 0);
  }

  function closeDropdown() {
    isOpen = false;
    searchTerm = '';
  }

  function toggleOption(value: any) {
    const exists = selectedValues?.some(v => String(v) === String(value));
    const next = Array.isArray(selectedValues) ? [...selectedValues] : [];
    if (exists) {
      const idx = next.findIndex(v => String(v) === String(value));
      if (idx > -1) next.splice(idx, 1);
    } else {
      next.push(value);
    }
    selectedValues = next;
    dispatch('change', selectedValues);
  }

  function removeTag(value: any, e?: MouseEvent) {
    e?.stopPropagation?.();
    toggleOption(value);
  }

  function clearAll(e?: MouseEvent) {
    e?.stopPropagation?.();
    selectedValues = [];
    dispatch('change', selectedValues);
  }

  function isSelected(value: any): boolean {
    return selectedValues?.some(v => String(v) === String(value));
  }

  function handleClickOutside(event: MouseEvent) {
    if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
      closeDropdown();
    }
  }

  onMount(() => {
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  });
</script>

<div class="relative" bind:this={dropdownElement}>
  <!-- 触发器 -->
  <div class="relative">
    <button
      type="button"
      class="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed min-h-[40px]"
      {disabled}
      onclick={openDropdown}
      aria-haspopup="listbox"
      aria-expanded={isOpen}
    >
      <div class="flex items-center justify-between gap-2">
        <div class="flex flex-wrap gap-1 items-center">
          {#if selectedValues && selectedValues.length > 0}
            {#each selectedValues.slice(0, 3) as sv}
              {@const opt = options.find(o => String(o.value) === String(sv))}
              <span class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                {opt ? opt.label : sv}
                <button class="ml-1 text-blue-700 hover:text-blue-900" onclick={(e) => removeTag(sv, e)} aria-label="移除">
                  <X class="h-3 w-3" />
                </button>
              </span>
            {/each}
            {#if selectedValues.length > 3}
              <span class="text-xs text-gray-500">+{selectedValues.length - 3}</span>
            {/if}
          {:else}
            <span class="text-gray-500">{placeholder}</span>
          {/if}
        </div>
        <div class="flex items-center gap-1">
          {#if selectedValues && selectedValues.length > 0}
            <button type="button" class="p-1 hover:bg-gray-100 rounded" onclick={(e) => clearAll(e)} aria-label="清空">
              <X class="h-3 w-3 text-gray-400" />
            </button>
          {/if}
          <ChevronDown class={`h-4 w-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </div>
    </button>
  </div>

  <!-- 下拉框 -->
  {#if isOpen}
    <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-72 overflow-hidden">
      <!-- 搜索框 -->
      <div class="p-2 border-b border-gray-200">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            bind:this={searchInputElement}
            bind:value={searchTerm}
            type="text"
            class="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder={searchPlaceholder}
          />
        </div>
      </div>

      <!-- 选项列表 -->
      <div class="max-h-56 overflow-y-auto">
        {#if loading}
          <div class="px-3 py-2 text-sm text-gray-500 text-center">{loadingMessage}</div>
        {:else if filteredOptions.length === 0}
          <div class="px-3 py-2 text-sm text-gray-500 text-center">{searchTerm.trim() ? noResultsMessage : emptyMessage}</div>
        {:else}
          {#each filteredOptions as option}
            <button
              type="button"
              class="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 focus:bg-gray-50 focus:outline-none flex items-center gap-2"
              onclick={() => toggleOption(option.value)}
            >
              <span class={`h-4 w-4 inline-flex items-center justify-center rounded border ${isSelected(option.value) ? 'bg-blue-500 border-blue-500 text-white' : 'bg-white border-gray-300 text-transparent'}`}>
                <Check class="h-3 w-3" />
              </span>
              <span>{option.label}</span>
            </button>
          {/each}
        {/if}
      </div>
    </div>
  {/if}
</div>

