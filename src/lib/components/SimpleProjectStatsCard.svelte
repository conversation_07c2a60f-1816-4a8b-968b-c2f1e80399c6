<script lang="ts">
  import { cn } from "$lib/utils";

  // Props
  export let title = "";
  export let count = 0;
  export let icon: any;
  export let color = "blue";
  export let isActive = false;
  export let onClick = () => {};

  // Get color classes based on the provided color
  function getColorClasses(colorName: string) {
    const colorMap: Record<string, {
      bg: string,
      border: string,
      text: string,
      lightBg: string,
      gradient: string,
      shadow: string
    }> = {
      blue: {
        bg: "bg-blue-500",
        border: "border-blue-500",
        text: "text-blue-600",
        lightBg: "bg-blue-50",
        gradient: "bg-gradient-to-br from-blue-400 to-blue-600",
        shadow: "shadow-blue-200"
      },
      green: {
        bg: "bg-green-500",
        border: "border-green-500",
        text: "text-green-600",
        lightBg: "bg-green-50",
        gradient: "bg-gradient-to-br from-green-400 to-green-600",
        shadow: "shadow-green-200"
      },
      purple: {
        bg: "bg-purple-500",
        border: "border-purple-500",
        text: "text-purple-600",
        lightBg: "bg-purple-50",
        gradient: "bg-gradient-to-br from-purple-400 to-purple-600",
        shadow: "shadow-purple-200"
      },
      orange: {
        bg: "bg-orange-500",
        border: "border-orange-500",
        text: "text-orange-600",
        lightBg: "bg-orange-50",
        gradient: "bg-gradient-to-br from-orange-400 to-orange-600",
        shadow: "shadow-orange-200"
      }
    };

    return colorMap[colorName] || colorMap.blue;
  }

  const colorClasses = getColorClasses(color);
</script>

<div class="relative h-full min-h-[140px]">
  <div
    class={cn(
      "bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer h-full",
      "border border-gray-100 hover:border-gray-200",
      isActive ? `ring-2 ${colorClasses.border} ${colorClasses.shadow}` : ""
    )}
    onclick={onClick}
    role="button"
    tabindex="0"
    onkeydown={(e) => e.key === 'Enter' && onClick()}
    aria-label={`查看${title}项目列表`}
  >
    <!-- 顶部装饰条 -->
    <div class={cn("absolute top-0 left-0 right-0 h-1", colorClasses.gradient)}></div>

    <!-- 图标区域 -->
    <div class={cn(`${colorClasses.gradient} p-2.5 rounded-lg shadow-sm inline-flex mb-3`)}>
      <svelte:component this={icon} class="h-5 w-5 text-white" />
    </div>

    <!-- 内容区域 -->
    <div class="space-y-1">
      <p class="text-xs text-gray-500 font-medium uppercase tracking-wide">{title}</p>
      <div class="flex items-baseline">
        <p class={cn("text-2xl font-bold", colorClasses.text)}>
          {count}
        </p>
        <p class="text-sm text-gray-500 ml-1">个项目</p>
      </div>
    </div>

    <!-- 活动指示器 -->
    {#if isActive}
      <div class="absolute bottom-0 left-0 right-0 flex justify-center">
        <div class={cn("w-16 h-1 rounded-t-full", colorClasses.gradient)}></div>
      </div>
    {/if}
  </div>
</div>
