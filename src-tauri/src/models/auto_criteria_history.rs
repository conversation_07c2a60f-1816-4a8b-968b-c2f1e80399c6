use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct AutoCriteriaHistory {
    pub history_id: Option<i64>,
    pub created_at: Option<String>,
    pub input_text: String,
    pub segmented_text: Option<String>,
    pub model_id: Option<String>,
    pub prompt_template: Option<String>,
    pub generated_json: Option<String>,
    pub processing_details: Option<String>,
    pub success_count: Option<i64>,
    pub failed_count: Option<i64>,
    pub notes: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateAutoCriteriaHistoryRequest {
    pub input_text: String,
    pub segmented_text: Option<String>,
    pub model_id: Option<String>,
    pub prompt_template: Option<String>,
    pub generated_json: Option<String>,
    pub processing_details: Option<String>,
    pub success_count: Option<i64>,
    pub failed_count: Option<i64>,
    pub notes: Option<String>,
}

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AutoCriteriaHistoryQuery {
    pub limit: Option<i64>,
    pub offset: Option<i64>,
    pub keyword: Option<String>,
}
