// 筛选条件类型
export interface FilterCondition {
  field: string;
  operator: 'equals' | 'in' | 'contains' | 'between' | 'not_equals';
  value: any;
  label?: string;
}

// 筛选配置
export interface FilterConfig {
  id?: string;
  name: string;
  description?: string;
  conditions: FilterCondition[];
  isDefault?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 可用的筛选字段
export interface FilterField {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'text' | 'date' | 'daterange';
  options?: Array<{ value: any; label: string }>;
  placeholder?: string;
}

// 预定义的筛选字段
export const FILTER_FIELDS: FilterField[] = [
  {
    key: 'project_status_item_id',
    label: '项目状态',
    type: 'select',
    placeholder: '选择项目状态'
  },
  {
    key: 'recruitment_status_item_id',
    label: '招募状态',
    type: 'select',
    placeholder: '选择招募状态'
  },
  {
    key: 'disease_item_id',
    label: '疾病类型',
    type: 'select',
    placeholder: '选择疾病类型'
  },
  {
    key: 'project_stage_item_id',
    label: '研究分期',
    type: 'select',
    placeholder: '选择研究分期'
  },
  {
    key: 'sponsor_item_ids',
    label: '申办方',
    type: 'multiselect',
    placeholder: '选择申办方'
  },
  {
    key: 'pi_personnel_ids',
    label: '主要研究者(PI)',
    type: 'multiselect',
    placeholder: '选择主要研究者'
  },
  {
    key: 'crc_personnel_ids',
    label: '临床研究协调员(CRC)',
    type: 'multiselect',
    placeholder: '选择CRC'
  },
  {
    key: 'project_start_date',
    label: '启动日期',
    type: 'daterange',
    placeholder: '选择日期范围'
  },
  {
    key: 'project_name',
    label: '项目名称',
    type: 'text',
    placeholder: '输入项目名称关键词'
  }
];

// 快捷筛选标签
export interface QuickFilterTag {
  id: string;
  label: string;
  count?: number;
  color?: string;
  isActive?: boolean;
  filterConfig?: FilterConfig;
  onClick?: () => void;
}
