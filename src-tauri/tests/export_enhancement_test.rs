#[cfg(test)]
mod export_enhancement_tests {
    use super::*;
    use crate::commands::project_management_commands::{
        get_projects_complete_export_data, get_projects_export_data, ProjectExportQuery,
    };

    #[tokio::test]
    async fn test_enhanced_export_query_structure() {
        // 测试新的查询参数结构
        let query = ProjectExportQuery {
            project_status_item_id: Some(1),
            recruitment_status_item_id: Some(2),
            disease_item_id: Some(3),
            project_stage_item_id: Some(4),
            project_ids: Some(vec![
                "test_project_1".to_string(),
                "test_project_2".to_string(),
            ]),
            include_criteria: Some(true),
        };

        // 验证查询参数结构正确
        assert_eq!(query.project_status_item_id, Some(1));
        assert_eq!(query.recruitment_status_item_id, Some(2));
        assert_eq!(query.disease_item_id, Some(3));
        assert_eq!(query.project_stage_item_id, Some(4));
        assert!(query.project_ids.is_some());
        assert_eq!(query.include_criteria, Some(true));
    }

    #[test]
    fn test_export_query_serialization() {
        // 测试查询参数的序列化
        let query = ProjectExportQuery {
            project_status_item_id: Some(1),
            recruitment_status_item_id: None,
            disease_item_id: Some(3),
            project_stage_item_id: None,
            project_ids: Some(vec!["test_project".to_string()]),
            include_criteria: Some(true),
        };

        // 验证可以正确序列化
        let serialized = serde_json::to_string(&query);
        assert!(serialized.is_ok());

        if let Ok(json_str) = serialized {
            assert!(json_str.contains("project_status_item_id"));
            assert!(json_str.contains("include_criteria"));
            assert!(json_str.contains("test_project"));
        }
    }
}
