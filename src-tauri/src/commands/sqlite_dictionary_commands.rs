use crate::models::sqlite_dictionary::{Dictionary, DictionaryItem, DictionaryQuery};
use crate::services::sqlite_dictionary_service::get_sqlite_dictionary_service;
use log::{error, info};
use serde::Serialize;

/// 通用响应
#[derive(Debug, Serialize)]
pub struct DictionaryResponse<T> {
    pub success: bool,
    pub error: Option<String>,
    pub data: Option<T>,
}

/// 获取所有字典
#[tauri::command]
pub async fn sqlite_get_all_dicts() -> Result<DictionaryResponse<Vec<Dictionary>>, String> {
    info!("获取所有字典");

    match get_sqlite_dictionary_service() {
        Ok(service) => match service.get_dictionaries(None) {
            Ok(dictionaries) => Ok(DictionaryResponse {
                success: true,
                error: None,
                data: Some(dictionaries),
            }),
            Err(e) => {
                error!("获取所有字典失败: {}", e);
                Ok(DictionaryResponse {
                    success: false,
                    error: Some(format!("获取所有字典失败: {}", e)),
                    data: None,
                })
            }
        },
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 查询字典
#[tauri::command]
pub async fn sqlite_query_dicts(
    query: DictionaryQuery,
) -> Result<DictionaryResponse<Vec<Dictionary>>, String> {
    info!("查询字典");

    match get_sqlite_dictionary_service() {
        Ok(service) => match service.get_dictionaries(Some(&query)) {
            Ok(dictionaries) => Ok(DictionaryResponse {
                success: true,
                error: None,
                data: Some(dictionaries),
            }),
            Err(e) => {
                error!("查询字典失败: {}", e);
                Ok(DictionaryResponse {
                    success: false,
                    error: Some(format!("查询字典失败: {}", e)),
                    data: None,
                })
            }
        },
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 根据 ID 获取字典
#[tauri::command]
pub async fn sqlite_get_dict_by_id(id: i64) -> Result<DictionaryResponse<Dictionary>, String> {
    info!(
        "根据 ID 获取字典: {}, 类型 = {}",
        id,
        std::any::type_name::<i64>()
    );

    match get_sqlite_dictionary_service() {
        Ok(service) => {
            info!("获取到 SQLite 字典服务");
            match service.get_dictionary_by_id(id) {
                Ok(Some(dictionary)) => {
                    info!(
                        "根据 ID 获取字典成功: ID = {}, 名称 = {}",
                        id, dictionary.name
                    );
                    Ok(DictionaryResponse {
                        success: true,
                        error: None,
                        data: Some(dictionary),
                    })
                }
                Ok(None) => {
                    info!("字典不存在: ID = {}", id);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("字典不存在: ID = {}", id)),
                        data: None,
                    })
                }
                Err(e) => {
                    error!("根据 ID 获取字典失败: {}", e);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("根据 ID 获取字典失败: {}", e)),
                        data: None,
                    })
                }
            }
        }
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 根据名称获取字典
#[tauri::command]
pub async fn sqlite_get_dict_by_name(
    name: String,
) -> Result<DictionaryResponse<Dictionary>, String> {
    info!("根据名称获取字典: {}", name);

    match get_sqlite_dictionary_service() {
        Ok(service) => match service.get_dictionary_by_name(&name) {
            Ok(Some(dictionary)) => Ok(DictionaryResponse {
                success: true,
                error: None,
                data: Some(dictionary),
            }),
            Ok(None) => Ok(DictionaryResponse {
                success: false,
                error: Some(format!("字典不存在: name = {}", name)),
                data: None,
            }),
            Err(e) => {
                error!("根据名称获取字典失败: {}", e);
                Ok(DictionaryResponse {
                    success: false,
                    error: Some(format!("根据名称获取字典失败: {}", e)),
                    data: None,
                })
            }
        },
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 创建字典
#[tauri::command]
pub async fn sqlite_create_dict(dictionary: Dictionary) -> Result<DictionaryResponse<i64>, String> {
    info!("创建字典: {}", dictionary.name);

    match get_sqlite_dictionary_service() {
        Ok(service) => match service.create_dictionary(&dictionary) {
            Ok(id) => Ok(DictionaryResponse {
                success: true,
                error: None,
                data: Some(id),
            }),
            Err(e) => {
                error!("创建字典失败: {}", e);
                Ok(DictionaryResponse {
                    success: false,
                    error: Some(format!("创建字典失败: {}", e)),
                    data: None,
                })
            }
        },
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 更新字典
#[tauri::command]
pub async fn sqlite_update_dict(
    id: i64,
    dictionary: Dictionary,
) -> Result<DictionaryResponse<bool>, String> {
    info!("更新字典: ID = {}", id);

    match get_sqlite_dictionary_service() {
        Ok(service) => match service.update_dictionary(id, &dictionary) {
            Ok(updated) => Ok(DictionaryResponse {
                success: updated,
                error: if updated {
                    None
                } else {
                    Some(format!("字典不存在: ID = {}", id))
                },
                data: Some(updated),
            }),
            Err(e) => {
                error!("更新字典失败: {}", e);
                Ok(DictionaryResponse {
                    success: false,
                    error: Some(format!("更新字典失败: {}", e)),
                    data: None,
                })
            }
        },
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 删除字典
#[tauri::command]
pub async fn sqlite_delete_dict(id: i64) -> Result<DictionaryResponse<bool>, String> {
    info!(
        "删除字典: ID = {}, 类型 = {}",
        id,
        std::any::type_name::<i64>()
    );

    match get_sqlite_dictionary_service() {
        Ok(service) => {
            info!("获取到 SQLite 字典服务");
            match service.delete_dictionary(id) {
                Ok(deleted) => {
                    info!("删除字典结果: {}", deleted);
                    Ok(DictionaryResponse {
                        success: deleted,
                        error: if deleted {
                            None
                        } else {
                            Some(format!("字典不存在: ID = {}", id))
                        },
                        data: Some(deleted),
                    })
                }
                Err(e) => {
                    error!("删除字典失败: {}", e);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("删除字典失败: {}", e)),
                        data: None,
                    })
                }
            }
        }
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 获取字典项
#[tauri::command]
pub async fn sqlite_get_dict_items(
    dictionary_id: i64,
) -> Result<DictionaryResponse<Vec<DictionaryItem>>, String> {
    info!(
        "获取字典项: dictionary_id = {}, 类型 = {}",
        dictionary_id,
        std::any::type_name::<i64>()
    );

    match get_sqlite_dictionary_service() {
        Ok(service) => {
            info!("获取到 SQLite 字典服务");
            match service.get_dictionary_items(dictionary_id) {
                Ok(items) => {
                    info!("获取字典项成功: 找到 {} 个项", items.len());
                    Ok(DictionaryResponse {
                        success: true,
                        error: None,
                        data: Some(items),
                    })
                }
                Err(e) => {
                    error!("获取字典项失败: {}", e);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("获取字典项失败: {}", e)),
                        data: None,
                    })
                }
            }
        }
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 添加字典项
#[tauri::command]
pub async fn sqlite_add_dict_item(
    dictionary_id: i64,
    item: DictionaryItem,
) -> Result<DictionaryResponse<i64>, String> {
    info!(
        "添加字典项: dictionary_id = {}, key = {}, 类型 = {}",
        dictionary_id,
        item.key,
        std::any::type_name::<i64>()
    );
    info!(
        "字典项数据: value = {}, description = {}, status = {}",
        item.value,
        item.description.as_deref().unwrap_or(""),
        item.status
    );
    info!(
        "参数详情: dictionary_id = {:?}, item = {:?}",
        dictionary_id, item
    );

    // 检查参数是否有效
    if item.key.is_empty() {
        let error_msg = "字典项键不能为空";
        error!("{}", error_msg);
        return Ok(DictionaryResponse {
            success: false,
            error: Some(error_msg.to_string()),
            data: None,
        });
    }

    if item.value.is_empty() {
        let error_msg = "字典项值不能为空";
        error!("{}", error_msg);
        return Ok(DictionaryResponse {
            success: false,
            error: Some(error_msg.to_string()),
            data: None,
        });
    }

    // 检查字典项键是否符合规范（只允许英文、数字、下划线）
    let key_regex = regex::Regex::new(r"^[a-zA-Z0-9_]+$").unwrap();
    if !key_regex.is_match(&item.key) {
        let error_msg = "字典项键只能包含英文字母、数字和下划线";
        error!("{}: {}", error_msg, item.key);
        return Ok(DictionaryResponse {
            success: false,
            error: Some(error_msg.to_string()),
            data: None,
        });
    }

    // 尝试获取 SQLite 字典服务
    let service = match get_sqlite_dictionary_service() {
        Ok(service) => {
            info!("获取到 SQLite 字典服务");
            service
        }
        Err(e) => {
            let error_msg = format!("获取 SQLite 字典服务失败: {}", e);
            error!("{}", error_msg);
            return Ok(DictionaryResponse {
                success: false,
                error: Some(error_msg),
                data: None,
            });
        }
    };

    // 先检查字典是否存在
    let _dict = match service.get_dictionary_by_id(dictionary_id) {
        Ok(Some(dict)) => {
            info!("字典存在: ID = {}, 名称 = {}", dictionary_id, dict.name);
            dict
        }
        Ok(None) => {
            let error_msg = format!("字典不存在: ID = {}", dictionary_id);
            error!("{}", error_msg);
            return Ok(DictionaryResponse {
                success: false,
                error: Some(error_msg),
                data: None,
            });
        }
        Err(e) => {
            let error_msg = format!("检查字典失败: {}", e);
            error!("{}", error_msg);
            return Ok(DictionaryResponse {
                success: false,
                error: Some(error_msg),
                data: None,
            });
        }
    };

    // 检查字典项键是否已存在
    let items = match service.get_dictionary_items(dictionary_id) {
        Ok(items) => items,
        Err(e) => {
            let error_msg = format!("获取字典项失败: {}", e);
            error!("{}", error_msg);
            return Ok(DictionaryResponse {
                success: false,
                error: Some(error_msg),
                data: None,
            });
        }
    };

    for existing_item in &items {
        if existing_item.key == item.key {
            let error_msg = format!("字典项键已存在: {}", item.key);
            error!("{}", error_msg);
            return Ok(DictionaryResponse {
                success: false,
                error: Some(error_msg),
                data: None,
            });
        }
    }

    // 继续添加字典项
    info!(
        "准备添加字典项: dictionary_id = {}, key = {}",
        dictionary_id, item.key
    );
    match service.add_dictionary_item(dictionary_id, &item) {
        Ok(id) => {
            info!("添加字典项成功: id = {}", id);
            Ok(DictionaryResponse {
                success: true,
                error: None,
                data: Some(id),
            })
        }
        Err(e) => {
            let error_msg = format!("添加字典项失败: {}", e);
            error!("{}", error_msg);
            Ok(DictionaryResponse {
                success: false,
                error: Some(error_msg),
                data: None,
            })
        }
    }
}

/// 更新字典项
#[tauri::command]
pub async fn sqlite_update_dict_item(
    dictionary_id: i64,
    key: String,
    item: DictionaryItem,
) -> Result<DictionaryResponse<bool>, String> {
    info!(
        "更新字典项: dictionary_id = {}, key = {}",
        dictionary_id, key
    );

    match get_sqlite_dictionary_service() {
        Ok(service) => match service.update_dictionary_item(dictionary_id, &key, &item) {
            Ok(updated) => Ok(DictionaryResponse {
                success: updated,
                error: if updated {
                    None
                } else {
                    Some(format!(
                        "字典项不存在: dictionary_id = {}, key = {}",
                        dictionary_id, key
                    ))
                },
                data: Some(updated),
            }),
            Err(e) => {
                error!("更新字典项失败: {}", e);
                Ok(DictionaryResponse {
                    success: false,
                    error: Some(format!("更新字典项失败: {}", e)),
                    data: None,
                })
            }
        },
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 删除字典项
#[tauri::command]
pub async fn sqlite_delete_dict_item(
    dictionary_id: i64,
    key: String,
) -> Result<DictionaryResponse<bool>, String> {
    info!(
        "删除字典项: dictionary_id = {}, key = {}, 类型 = {}",
        dictionary_id,
        key,
        std::any::type_name::<i64>()
    );
    info!(
        "参数详情: dictionary_id = {:?}, key = {:?}",
        dictionary_id, key
    );

    // 检查参数是否有效
    if key.is_empty() {
        error!("字典项键为空");
        return Ok(DictionaryResponse {
            success: false,
            error: Some("字典项键不能为空".to_string()),
            data: None,
        });
    }

    match get_sqlite_dictionary_service() {
        Ok(service) => {
            info!("获取到 SQLite 字典服务");

            // 先检查字典是否存在
            match service.get_dictionary_by_id(dictionary_id) {
                Ok(Some(dict)) => {
                    info!("字典存在: ID = {}, 名称 = {}", dictionary_id, dict.name);

                    // 继续删除字典项
                    match service.delete_dictionary_item(dictionary_id, &key) {
                        Ok(deleted) => {
                            info!("删除字典项结果: {}", deleted);
                            Ok(DictionaryResponse {
                                success: deleted,
                                error: if deleted {
                                    None
                                } else {
                                    Some(format!(
                                        "字典项不存在: dictionary_id = {}, key = {}",
                                        dictionary_id, key
                                    ))
                                },
                                data: Some(deleted),
                            })
                        }
                        Err(e) => {
                            error!("删除字典项失败: {}", e);
                            Ok(DictionaryResponse {
                                success: false,
                                error: Some(format!("删除字典项失败: {}", e)),
                                data: None,
                            })
                        }
                    }
                }
                Ok(None) => {
                    error!("字典不存在: ID = {}", dictionary_id);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("字典不存在: ID = {}", dictionary_id)),
                        data: None,
                    })
                }
                Err(e) => {
                    error!("检查字典失败: {}", e);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("检查字典失败: {}", e)),
                        data: None,
                    })
                }
            }
        }
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 批量删除字典项
#[tauri::command]
pub async fn sqlite_batch_delete_dict_items(
    dictionary_id: i64,
    keys: Vec<String>,
) -> Result<DictionaryResponse<i32>, String> {
    info!(
        "批量删除字典项: dictionary_id = {}, keys = {:?}",
        dictionary_id, keys
    );

    // 检查参数是否有效
    if keys.is_empty() {
        error!("字典项键列表为空");
        return Ok(DictionaryResponse {
            success: false,
            error: Some("字典项键列表不能为空".to_string()),
            data: None,
        });
    }

    match get_sqlite_dictionary_service() {
        Ok(service) => {
            info!("获取到 SQLite 字典服务");

            // 先检查字典是否存在
            match service.get_dictionary_by_id(dictionary_id) {
                Ok(Some(dict)) => {
                    info!("字典存在: ID = {}, 名称 = {}", dictionary_id, dict.name);

                    // 批量删除字典项
                    match service.batch_delete_dictionary_items(dictionary_id, &keys) {
                        Ok(deleted_count) => {
                            info!("批量删除字典项结果: 删除了 {} 个项目", deleted_count);
                            Ok(DictionaryResponse {
                                success: true,
                                error: None,
                                data: Some(deleted_count),
                            })
                        }
                        Err(e) => {
                            error!("批量删除字典项失败: {}", e);
                            Ok(DictionaryResponse {
                                success: false,
                                error: Some(format!("批量删除字典项失败: {}", e)),
                                data: None,
                            })
                        }
                    }
                }
                Ok(None) => {
                    error!("字典不存在: ID = {}", dictionary_id);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("字典不存在: ID = {}", dictionary_id)),
                        data: None,
                    })
                }
                Err(e) => {
                    error!("检查字典失败: {}", e);
                    Ok(DictionaryResponse {
                        success: false,
                        error: Some(format!("检查字典失败: {}", e)),
                        data: None,
                    })
                }
            }
        }
        Err(e) => {
            error!("获取 SQLite 字典服务失败: {}", e);
            Ok(DictionaryResponse {
                success: false,
                error: Some(format!("获取 SQLite 字典服务失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 测试调用
#[tauri::command]
pub async fn sqlite_test_command(param: String) -> Result<DictionaryResponse<String>, String> {
    info!("测试调用: param = {}", param);

    Ok(DictionaryResponse {
        success: true,
        error: None,
        data: Some(format!("收到参数: {}", param)),
    })
}
