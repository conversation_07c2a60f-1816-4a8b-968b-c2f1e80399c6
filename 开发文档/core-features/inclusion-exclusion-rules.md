# 入排标准规则开发文档

## 文档说明

> **注意**: 本文档提供入排标准功能的详细技术实现信息。用户功能说明请参考 [功能使用手册](../../FUNCTIONAL_MANUAL.md#2-入排标准规则引擎)。

## 概述

本文档详细描述入排标准规则引擎的技术架构、代码组织和实现细节，主要面向开发人员。该功能允许用户为每个项目定义一系列的"入选标准"（Inclusion Criteria）和"排除标准"（Exclusion Criteria），基于预定义的规则模板并支持参数化配置。

## 数据库结构

基于提供的 ER 图：

- **`projects`**: 项目主表。
- **`rule_definitions`**: 定义可用的规则，包含规则名称、描述、类别和参数模式（JSON schema）。
  - `rule_definition_id` (PK)
  - `rule_name`
  - `rule_description`
  - `category`
  - `parameter_schema` (JSON String, 定义规则所需的参数及其类型、约束等)
- **`project_criteria`**: 存储项目与规则的关联，以及具体参数值。
  - `project_criterion_id` (PK)
  - `project_id` (FK to `projects`)
  - `rule_definition_id` (FK to `rule_definitions`)
  - `criterion_type` (String: 'inclusion' 或 'exclusion')
  - `parameter_values` (JSON String, 存储用户为该规则配置的具体参数值，需符合对应 `rule_definitions.parameter_schema`)
  - `is_active`
  - `display_order`

## 前端实现

### 路由与页面

- **规则定义管理页面**: @`src/routes/rules/definitions/+page.svelte` - 用于查看和管理所有可用的规则定义。
- **项目入排标准配置页面**: [`src/routes/projects/[projectId]/criteria/+page.svelte`](mdc:src/routes/projects/[projectId]/criteria/+page.svelte) - 用于配置特定项目的入排标准。

### 核心组件

- **规则定义列表**: @`src/lib/components/rule-designer/RuleDefinitionList.svelte` - 显示规则定义列表，提供增删改查入口。
- **规则定义表单**: @`src/lib/components/rule-designer/RuleDefinitionForm.svelte` - 用于创建和编辑规则定义。
- **项目标准配置**: @`src/lib/components/rule-designer/ProjectCriteriaConfig.svelte` - 在项目页面内嵌入，用于管理该项目的入排标准列表。
- **项目标准表单**: @`src/lib/components/rule-designer/ProjectCriterionForm.svelte` - 用于添加或编辑单个项目标准，包括选择规则定义和填写参数。

### 服务层

- **规则设计器服务**: @`src/lib/services/ruleDesignerService.ts` - 封装与后端规则设计器相关的 API 调用，包括 `rule_definitions` 和 `project_criteria` 的 CRUD 操作。

## 后端实现 (src-tauri/)

### 命令 (Commands)

- **规则设计器命令**: @`src-tauri/src/commands/rule_designer_commands.rs` - 定义 Tauri 命令，处理前端关于 `rule_definitions` 和 `project_criteria` 的请求。
  - `init_rule_designer_tables`
  - `get_rule_definitions`, `get_rule_definition_by_id`
  - `create_rule_definition`, `update_rule_definition`, `delete_rule_definition`
  - `get_project_criteria`, `get_project_criterion_by_id`
  - `create_project_criterion`, `update_project_criterion`, `delete_project_criterion`

### 服务层 (Services)

- **规则设计器服务**: @`src-tauri/src/services/rule_designer_service.rs` - 实现业务逻辑，调用仓库层进行数据操作，包含参数验证逻辑（JSON schema 验证）。

### 仓库层 (Repositories)

- **规则设计器仓库**: @`src-tauri/src/repositories/rule_designer_repository.rs` - 负责与 SQLite 数据库交互，执行 `rule_definitions` 和 `project_criteria` 表的 CRUD 操作。

### 数据模型 (Models)

- **规则设计器模型**: @`src-tauri/src/models/rule_designer.rs` - 定义 Rust 结构体，对应数据库表 (`RuleDefinition`, `ProjectCriterion`) 以及 API 请求/响应 (`CreateRuleDefinitionRequest`, `ProjectCriterionWithRule` 等)。

### 数据库初始化

- 表结构在 @`src-tauri/src/repositories/rule_designer_repository.rs` 中的 `init_tables` 函数中定义。
- 初始化调用通常在前端页面加载时触发（如 `src/routes/rules/definitions/+page.svelte` 和 `src/routes/projects/[projectId]/criteria/+page.svelte`），通过调用后端 `init_rule_designer_tables` 命令完成。

## 注意事项

- **参数验证**: 后端服务层 `RuleDesignerService` 负责验证 `project_criteria.parameter_values` 是否符合对应 `rule_definitions.parameter_schema` 定义的 JSON schema。
- **前后端数据同步**: 前端通过 `ruleDesignerService.ts` 调用后端命令，后端通过 `rule_designer_commands.rs` 接收请求并调用 `rule_designer_service.rs` 处理。

## 相关文档

- [功能使用手册 - 入排标准规则引擎](../../FUNCTIONAL_MANUAL.md#2-入排标准规则引擎)
- [AI 自动化生成文档](./ai-automation.md)
- [项目管理开发文档](./project-management.md)
- [数据库结构文档](../database/database-schema.md)

---

**最后更新**: 2024年12月  
**维护者**: 开发团队  
**文档版本**: v2.0
