# Project Structure & Organization

## Root Structure
```
tauri-app/
├── src/                    # Frontend (SvelteKit)
├── src-tauri/             # Backend (Rust)
├── docs/                  # Documentation
├── 开发文档/              # Development docs (Chinese)
├── static/                # Static assets
└── [config files]         # Build & dependency configs
```

## Frontend Architecture (`src/`)

### Core Organization
- **`lib/`**: Shared code library
  - **`components/`**: UI components organized by feature
  - **`services/`**: Frontend service layer (API calls)
  - **`stores/`**: Svelte state management
  - **`utils/`**: Utility functions and helpers
  - **`types/`**: TypeScript type definitions
  - **`models/`**: Frontend data models
- **`routes/`**: File-system based routing (SvelteKit)
- **`app.html`**: Main HTML template
- **`app.css`**: Global styles (Tailwind CSS)

### Component Organization Pattern
```
components/
├── ui/                    # Base UI components (buttons, forms, etc.)
├── [feature]/             # Feature-specific components
│   ├── [Feature]Form.svelte
│   ├── [Feature]List.svelte
│   └── [Feature]Detail.svelte
└── dashboard/             # Dashboard-specific components
    ├── charts/            # Chart components
    └── filters/           # Filter components
```

### Service Layer Pattern
- One service file per major feature (e.g., `projectService.ts`)
- Services handle all Tauri command calls
- Consistent error handling and response formatting
- TypeScript interfaces for all API responses

## Backend Architecture (`src-tauri/`)

### Layered Architecture
```
src/
├── commands/              # Tauri command layer (API endpoints)
├── services/              # Business logic layer
├── repositories/          # Data access layer
├── models/                # Data structures
├── config/                # Configuration management
├── lib.rs                 # Main library (command registration)
├── main.rs                # Application entry point
├── error.rs               # Error handling
└── response.rs            # Response formatting
```

### Naming Conventions
- **Commands**: `[feature]_commands.rs` (e.g., `project_commands.rs`)
- **Services**: `[feature]_service.rs`
- **Repositories**: `[feature]_repository.rs`
- **Models**: `[feature].rs` or descriptive names

### Repository Pattern
- Abstract data access behind repository interfaces
- Support for multiple backends (SQLite, MongoDB)
- Organized in subdirectories: `sqlite/`, `mongodb/`

## File Naming Conventions

### Frontend
- **Components**: PascalCase (e.g., `ProjectForm.svelte`)
- **Services**: camelCase + Service (e.g., `projectManagementService.ts`)
- **Utilities**: camelCase (e.g., `noteClassifier.ts`)
- **Types**: camelCase (e.g., `project.ts`)

### Backend (Rust)
- **Files**: snake_case (e.g., `project_management_commands.rs`)
- **Structs**: PascalCase
- **Functions**: snake_case
- **Constants**: UPPER_SNAKE_CASE

### Directories
- **kebab-case** for multi-word directories (e.g., `rule-designer/`)
- **Descriptive and concise** names

## Route Organization (`src/routes/`)
```
routes/
├── +layout.svelte         # Root layout
├── +page.svelte           # Home page
├── projects/              # Project management
│   ├── +page.svelte       # Project list
│   ├── new/               # New project
│   ├── [projectId]/       # Dynamic project routes
│   └── batch-import/      # Batch operations
├── staff/                 # Personnel management
├── dashboard/             # Analytics
├── notes/                 # Smart notes
└── settings/              # System configuration
```

## Documentation Structure
- **`docs/`**: User-facing documentation (English)
- **`开发文档/`**: Technical development docs (Chinese)
  - **`core-features/`**: Feature implementation docs
  - **`database/`**: Database schema and design
  - **`api-integration/`**: External API documentation
  - **`system-features/`**: System functionality docs

## Configuration Files
- **`package.json`**: Node.js dependencies and scripts
- **`Cargo.toml`**: Rust dependencies and metadata
- **`tauri.conf.json`**: Tauri application configuration
- **`svelte.config.js`**: Svelte/SvelteKit configuration
- **`tailwind.config.ts`**: Tailwind CSS customization
- **`tsconfig.json`**: TypeScript compiler options
- **`vite.config.js`**: Vite build configuration

## Import/Export Patterns
- Use `$lib` alias for library imports in frontend
- Barrel exports in `index.ts` files for clean imports
- Consistent module organization with clear boundaries
- Avoid circular dependencies between modules