/**
 * 入排标准文本处理工具
 */

/**
 * 将文本分割为段落
 * @param text 要分割的文本
 * @returns 分割后的段落数组
 */
export function segmentText(text: string): string[] {
  if (!text || text.trim() === '') {
    return [];
  }

  // 预处理：规范换行与空格
  const norm = (s: string) => s
    .replace(/\u00A0/g, ' ')
    .replace(/\s+$/gm, '')
    .replace(/^[\s\t]+/gm, '')
    .trim();

  const input = norm(text);

  // 先按单行拆分（更符合常见粘贴格式：一条一行，以分号收尾）
  const lines = input.split(/\r?\n/).map(s => s.trim()).filter(Boolean);

  // 分号外层拆分（忽略括号内分号）
  const splitBySemicolonOutsideParens = (s: string): string[] => {
    const result: string[] = [];
    let buf = '';
    let depth = 0;
    for (let i = 0; i < s.length; i++) {
      const ch = s[i];
      if (ch === '（' || ch === '(') depth++;
      if (ch === '）' || ch === ')') depth = Math.max(0, depth - 1);
      if ((ch === '；' || ch === ';') && depth === 0) {
        if (buf.trim()) result.push(buf.trim());
        buf = '';
      } else {
        buf += ch;
      }
    }
    if (buf.trim()) result.push(buf.trim());
    return result;
  };

  const segments: string[] = [];
  const isHRCTLine = (s: string) => /(hrct|高分辨率|支气管扩张|ncfb)/i.test(s);
  const isWeightOrBMI = (s: string) => /(体重|bmi)/i.test(s) && /或/.test(s);
  const isColorWithScore = (s: string) => /(粘液脓性|黏液脓性).{0,4}或.{0,4}脓性/.test(s) && /评分/.test(s);

  for (const line of lines) {
    if (!line) continue;
    // HRCT/BMI/颜色+评分行保留为整体
    if (isHRCTLine(line) || isWeightOrBMI(line) || isColorWithScore(line)) {
      segments.push(line);
      continue;
    }
    // 常规：按分号（括号外）拆分
    const parts = splitBySemicolonOutsideParens(line);
    if (parts.length <= 1) {
      segments.push(line);
    } else {
      // 合并过短片段，避免“被切断”的语块
      const merged: string[] = [];
      for (const p of parts) {
        if (merged.length > 0 && p.length < 12) {
          merged[merged.length - 1] = (merged[merged.length - 1] + '；' + p).trim();
        } else {
          merged.push(p);
        }
      }
      segments.push(...merged);
    }
  }

  // 过滤空与重复空白
  return segments.map(s => s.replace(/[；;]+$/,'').trim()).filter(Boolean);
}

/**
 * 尝试识别段落是入选标准还是排除标准
 * @param segment 文本段落
 * @returns 'inclusion' 或 'exclusion' 或 null（无法确定）
 */
export function identifyCriterionType(segment: string): 'inclusion' | 'exclusion' | null {
  const lowerSegment = segment.toLowerCase();

  // 入选标准的常见关键词
  const inclusionKeywords = [
    '入选标准', '入组标准', '纳入标准', '入选条件', '入组条件', '纳入条件',
    '符合以下条件', '满足以下条件', '年龄在', '年龄介于', '年龄≥', '年龄≤'
  ];

  // 排除标准的常见关键词
  const exclusionKeywords = [
    '排除标准', '排除条件', '不符合以下', '不满足以下', '存在以下情况',
    '有下列情况', '有以下疾病', '患有', '过敏', '不能耐受', '禁忌症'
  ];

  // 检查是否包含入选关键词
  for (const keyword of inclusionKeywords) {
    if (lowerSegment.includes(keyword)) {
      return 'inclusion';
    }
  }

  // 检查是否包含排除关键词
  for (const keyword of exclusionKeywords) {
    if (lowerSegment.includes(keyword)) {
      return 'exclusion';
    }
  }

  // 无法确定
  return null;
}

/**
 * 检测是否包含“或/OR”关系
 */
export function hasOrRelationship(segment: string): boolean {
  // 忽略非条件性的“或”用法：一个或以上、或不伴、和（或）、或以上/或以下、（或）、或需要 等
  const avoidOrPatterns = /(一个或以上|或不伴|和（或）|和\(或\)|（或）|或以上|或以下|或需要|或等效|如.*?或)/;
  if (avoidOrPatterns.test(segment)) return false;

  // 仅识别括号外的“或”
  let depth = 0;
  for (let i = 0; i < segment.length; i++) {
    const ch = segment[i];
    if (ch === '（' || ch === '(') depth++;
    else if (ch === '）' || ch === ')') depth = Math.max(0, depth - 1);
    else if ((ch === '或' || (segment.slice(i, i+2).toLowerCase() === 'or')) && depth === 0) {
      return true;
    }
  }
  return false;
}

/**
 * 粗粒度拆分“或”关系为子句
 * 示例："≥2次中度或≥1次重度COPD急性加重" -> ["≥2次中度COPD急性加重", "≥1次重度COPD急性加重"]
 */
export function splitOrClauses(segment: string): string[] {
  if (!hasOrRelationship(segment)) return [segment];

  // 文本预清洗：修复“中度2/重度1”之类紧跟严重程度的冗余数字
  let cleaned = segment
    .replace(/(中度|重度)\s*([0-9一二三四五六七八九十]+)(?=\s*(或|，|；|,|;|$))/g, '$1');

  // 特判：颜色或同类属性的“或”不应拆分（例如“粘液脓性或脓性”），尤其当本句包含“评分”这类量化指标
  const avoidSplitColorOr = /(粘液脓性|黏液脓性).{0,4}或.{0,4}脓性/.test(cleaned) && /评分/.test(cleaned);
  if (avoidSplitColorOr) {
    return [segment.trim()];
  }

  // 优先按“或”字分割
  const parts = cleaned.split(/或|OR|or/).map(s => s.trim()).filter(Boolean);
  if (parts.length < 2) return [segment.trim()];

  // 从原文中提取疾病尾缀（优先使用较具体的）
  const diseaseTails = ['COPD急性加重', 'AECOPD', '急性加重'];
  let chosenTail = '';
  for (const key of diseaseTails) {
    if (cleaned.includes(key)) { chosenTail = key; break; }
  }

  // HRCT/支扩/NCFB 等影像学描述通常为一体，避免拆分
  if (/(hrct|高分辨率|支气管扩张|ncfb)/i.test(cleaned)) {
    return [segment.trim()];
  }

  // 维持治疗/ICS/LABA/LAMA/LTRA 等治疗方案通常为一体，避免拆分
  if (/(维持治疗|\bics\b|\blaba\b|\blama\b|\bltra\b|茶碱|吸入治疗)/i.test(cleaned)) {
    return [segment.trim()];
  }

  // 将尾缀补到不包含疾病词的子句后
  const rebuilt = parts.map((p) => {
    if (!chosenTail) return p;
    if (p.includes('急性加重') || p.toLowerCase().includes('aecopd') || p.includes('COPD急性加重')) {
      return p;
    }
    // 避免重复标点
    return p.replace(/[；;。\.]$/,'') + chosenTail;
  });

  // 吸烟史场景：如果尾部包含“的吸烟史…”，为不含“吸烟史”的子句补全该后缀
  const smokingIdx = cleaned.indexOf('的吸烟史');
  if (smokingIdx >= 0) {
    const smokingTail = cleaned.slice(smokingIdx);
    for (let i = 0; i < rebuilt.length; i++) {
      if (!/吸烟史/.test(rebuilt[i])) {
        rebuilt[i] = rebuilt[i].replace(/[；;。\.]$/,'') + smokingTail;
      }
    }
  }

  return rebuilt;
}

/**
 * 验证生成的JSON是否符合预期格式
 * @param json 要验证的JSON对象
 * @returns 验证结果，包含是否有效和错误信息
 */
export function validateGeneratedJson(json: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查基本结构
  if (!json) {
    errors.push('JSON为空');
    return { valid: false, errors };
  }

  // 检查criteria数组
  if (!Array.isArray(json.criteria)) {
    errors.push('缺少criteria数组或格式不正确');
    return { valid: false, errors };
  }

  // 检查每个criterion
  for (let i = 0; i < json.criteria.length; i++) {
    const criterion = json.criteria[i];

    // 检查必要字段
    if (!criterion.rule_definition_id) {
      errors.push(`第${i+1}条标准缺少rule_definition_id`);
    }

    if (!criterion.parameter_values || typeof criterion.parameter_values !== 'object') {
      errors.push(`第${i+1}条标准缺少parameter_values或格式不正确`);
    }

    if (!criterion.display_order && criterion.display_order !== 0) {
      errors.push(`第${i+1}条标准缺少display_order`);
    }
  }

  // 检查or_groups（如果存在）
  if (json.or_groups && !Array.isArray(json.or_groups)) {
    errors.push('or_groups格式不正确，应为数组');
  }

  if (json.or_groups && Array.isArray(json.or_groups)) {
    for (let i = 0; i < json.or_groups.length; i++) {
      const group = json.or_groups[i];

      if (!Array.isArray(group.criteria_indices)) {
        errors.push(`第${i+1}个or_group的criteria_indices不是数组`);
      } else if (group.criteria_indices.length < 2) {
        errors.push(`第${i+1}个or_group至少需要包含2个标准索引`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 格式化生成的JSON字符串
 * @param jsonString JSON字符串
 * @returns 格式化后的JSON字符串
 */
export function formatJsonString(jsonString: string): string {
  try {
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
  } catch (error) {
    console.error('格式化JSON失败:', error);
    return jsonString; // 返回原始字符串
  }
}

/**
 * 合并多个处理结果
 * @param results 处理结果数组
 * @returns 合并后的JSON对象
 */
export function mergeResults(results: any[]): any {
  // 初始化结果
  const merged: {
    criteria: any[];
    or_groups: any[];
  } = {
    criteria: [],
    or_groups: []
  };

  // 合并criteria
  for (const result of results) {
    if (result.criteria && Array.isArray(result.criteria)) {
      merged.criteria.push(...result.criteria);
    }

    if (result.or_groups && Array.isArray(result.or_groups)) {
      merged.or_groups.push(...result.or_groups);
    }
  }

  // 重新排序display_order
  merged.criteria = merged.criteria.map((criterion: any, index: number) => ({
    ...criterion,
    display_order: index + 1
  }));

  return merged;
}
