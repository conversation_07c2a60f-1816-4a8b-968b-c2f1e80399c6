## Summary

Describe the change and the problem it solves. Link related issues.

## Changes

- [ ] Feature / Fix / Refactor (choose one)
- Key points:
  - 

## Screenshots / Demos

Add visuals for UI changes (before/after if applicable).

## Tests

- [ ] Added/updated Rust tests (`cargo test`)
- [ ] Frontend tests (`npm run test`)

## Checklist

- [ ] `npm run check` passes (Svelte/TS)
- [ ] `npm run lint` passes (fmt + clippy)
- [ ] No secrets committed; docs updated if needed
- [ ] Tauri config/capabilities updated when applicable

