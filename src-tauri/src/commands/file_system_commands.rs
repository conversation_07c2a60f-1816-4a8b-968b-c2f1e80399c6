use crate::models::file_system::{DialogOptions, DialogResult, FileResponse, TagRule};
use crate::services::file_system_service::FILE_SYSTEM_SERVICE;
use log::{error, info};

/// 获取文件列表
#[tauri::command]
pub async fn get_files(dir_path: String) -> Result<FileResponse, String> {
    info!("获取文件列表: {}", dir_path);

    match FILE_SYSTEM_SERVICE.get_files(&dir_path) {
        Ok(files) => {
            let data =
                serde_json::to_value(files).map_err(|e| format!("序列化文件列表失败: {}", e))?;

            Ok(FileResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Err(e) => {
            error!("获取文件列表失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("获取文件列表失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 打开文件
#[tauri::command]
pub async fn open_file(file_path: String) -> Result<FileResponse, String> {
    info!("打开文件: {}", file_path);

    match FILE_SYSTEM_SERVICE.open_file(&file_path) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("打开文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("打开文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 打开文件夹
#[tauri::command]
pub async fn open_folder(folder_path: String) -> Result<FileResponse, String> {
    info!("打开文件夹: {}", folder_path);

    match FILE_SYSTEM_SERVICE.open_folder(&folder_path) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("打开文件夹失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("打开文件夹失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 显示打开文件对话框
#[tauri::command]
pub async fn show_open_dialog(options: DialogOptions) -> Result<DialogResult, String> {
    info!("显示打开文件对话框");

    // 暂时返回一个空的结果，因为 Tauri 2.0 的对话框 API 需要不同的实现方式
    Ok(DialogResult {
        canceled: true,
        paths: Vec::new(),
    })
}

/// 显示保存文件对话框
#[tauri::command]
pub async fn show_save_dialog(options: DialogOptions) -> Result<DialogResult, String> {
    info!("显示保存文件对话框");

    // 暂时返回一个空的结果，因为 Tauri 2.0 的对话框 API 需要不同的实现方式
    Ok(DialogResult {
        canceled: true,
        paths: Vec::new(),
    })
}

/// 创建文件
#[tauri::command]
pub async fn create_file(file_path: String, content: String) -> Result<FileResponse, String> {
    info!("创建文件: {}", file_path);

    match FILE_SYSTEM_SERVICE.create_file(&file_path, &content) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("创建文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("创建文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 读取文件
#[tauri::command]
pub async fn read_file(file_path: String) -> Result<FileResponse, String> {
    info!("读取文件: {}", file_path);

    match FILE_SYSTEM_SERVICE.read_file(&file_path) {
        Ok(content) => Ok(FileResponse {
            success: true,
            error: None,
            data: Some(serde_json::Value::String(content)),
        }),
        Err(e) => {
            error!("读取文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("读取文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 删除文件
#[tauri::command]
pub async fn delete_file(file_path: String) -> Result<FileResponse, String> {
    info!("删除文件: {}", file_path);

    match FILE_SYSTEM_SERVICE.delete_file(&file_path) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("删除文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("删除文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 创建目录
#[tauri::command]
pub async fn create_directory(dir_path: String) -> Result<FileResponse, String> {
    info!("创建目录: {}", dir_path);

    match FILE_SYSTEM_SERVICE.create_directory(&dir_path) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("创建目录失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("创建目录失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 复制文件
#[tauri::command]
pub async fn copy_file(src_path: String, dest_path: String) -> Result<FileResponse, String> {
    info!("复制文件: {} -> {}", src_path, dest_path);

    match FILE_SYSTEM_SERVICE.copy_file(&src_path, &dest_path) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("复制文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("复制文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 导出项目数据到用户选择的文件夹
#[tauri::command]
pub async fn export_projects_to_folder(
    content: String,
    filename: String,
    format: String,
) -> Result<FileResponse, String> {
    info!("导出项目数据: {} (格式: {})", filename, format);

    // 暂时使用默认的下载文件夹
    let home_dir = dirs::home_dir().ok_or("无法获取用户主目录")?;
    let downloads_dir = home_dir.join("Downloads");

    // 确保下载目录存在
    if !downloads_dir.exists() {
        std::fs::create_dir_all(&downloads_dir).map_err(|e| format!("创建下载目录失败: {}", e))?;
    }

    let file_path = downloads_dir.join(&filename);
    let file_path_str = file_path.to_string_lossy().to_string();

    info!("保存文件到: {}", file_path_str);

    // 保存文件
    match FILE_SYSTEM_SERVICE.create_file(&file_path_str, &content) {
        Ok(_) => {
            info!("文件导出成功: {}", file_path_str);
            Ok(FileResponse {
                success: true,
                error: None,
                data: Some(serde_json::json!({
                    "file_path": file_path_str
                })),
            })
        }
        Err(e) => {
            error!("保存文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("保存文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 移动文件
#[tauri::command]
pub async fn move_file(src_path: String, dest_path: String) -> Result<FileResponse, String> {
    info!("移动文件: {} -> {}", src_path, dest_path);

    match FILE_SYSTEM_SERVICE.move_file(&src_path, &dest_path) {
        Ok(_) => Ok(FileResponse {
            success: true,
            error: None,
            data: None,
        }),
        Err(e) => {
            error!("移动文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("移动文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 重命名文件
#[tauri::command]
pub async fn rename_file(file_path: String, new_name: String) -> Result<FileResponse, String> {
    info!("重命名文件: {} -> {}", file_path, new_name);

    match FILE_SYSTEM_SERVICE.rename_file(&file_path, &new_name) {
        Ok(new_path) => Ok(FileResponse {
            success: true,
            error: None,
            data: Some(serde_json::Value::String(new_path)),
        }),
        Err(e) => {
            error!("重命名文件失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("重命名文件失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 获取文件信息
#[tauri::command]
pub async fn get_file_info(file_path: String) -> Result<FileResponse, String> {
    info!("获取文件信息: {}", file_path);

    match FILE_SYSTEM_SERVICE.get_file_info(&file_path) {
        Ok(info) => {
            let data =
                serde_json::to_value(info).map_err(|e| format!("序列化文件信息失败: {}", e))?;

            Ok(FileResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Err(e) => {
            error!("获取文件信息失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("获取文件信息失败: {}", e)),
                data: None,
            })
        }
    }
}

/// 检查文件夹缺失标签
#[tauri::command]
pub async fn check_missing_tags(
    folder_path: String,
    tag_rules: Vec<TagRule>,
) -> Result<FileResponse, String> {
    info!("检查文件夹缺失标签: {}", folder_path);

    match FILE_SYSTEM_SERVICE.check_missing_tags(&folder_path, &tag_rules) {
        Ok(missing_tags) => {
            let data = serde_json::to_value(missing_tags)
                .map_err(|e| format!("序列化缺失标签失败: {}", e))?;

            Ok(FileResponse {
                success: true,
                error: None,
                data: Some(data),
            })
        }
        Err(e) => {
            error!("检查文件夹缺失标签失败: {}", e);
            Ok(FileResponse {
                success: false,
                error: Some(format!("检查文件夹缺失标签失败: {}", e)),
                data: None,
            })
        }
    }
}
