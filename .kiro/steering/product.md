# Product Overview

## PeckByte - Clinical Research Project Management System

A modern desktop application for clinical research institutions, providing comprehensive project lifecycle management with AI-powered automation capabilities.

### Core Purpose
- **Primary Users**: Clinical research institutions, project managers, research coordinators
- **Main Goal**: Streamline clinical research project management from initiation to completion
- **Key Value**: Intelligent automation of routine tasks, comprehensive data management, and regulatory compliance support

### Key Features
- **Project Management**: Complete lifecycle management for clinical research projects
- **Inclusion/Exclusion Rules Engine**: AI-powered criteria generation and management
- **Personnel Management**: Research staff coordination and authorization tracking
- **Smart Notes System**: AI-driven note classification and Notion integration
- **Dashboard Analytics**: Multi-dimensional project data visualization and reporting
- **CSV Import System**: Bulk data import with quality control and validation
- **External Integrations**: Notion, Lighthouse API, and other clinical systems

### Business Context
- Specialized for clinical research workflows and regulatory requirements
- Supports both local (SQLite) and cloud (MongoDB) data storage strategies
- Designed for desktop deployment with cross-platform compatibility
- Emphasizes data security and compliance with clinical research standards