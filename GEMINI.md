# Gemini Workspace Guide

This document provides a guide for developers working on this project, with a focus on how to use Gemini for development.

## Project Overview

This is a Tauri application, which means it consists of a Rust-based backend and a web-based frontend. The frontend is built with Svelte and TypeScript, and styled with Tailwind CSS.

## Getting Started

### Prerequisites

- Node.js and npm
- Rust and Cargo

### Installation

1.  Install the frontend dependencies:
    ```bash
    npm install
    ```

2.  Install the backend dependencies:
    ```bash
    cargo build
    ```

### Development

To run the application in development mode, use the following command:

```bash
npm run tauri dev
```

This will start the Vite development server for the frontend and build and run the Tauri application.

### Building

To build the application for production, use the following command:

```bash
npm run tauri build
```

This will create a production-ready executable in the `src-tauri/target/release` directory.

## Project Structure

- `src`: Contains the Svelte frontend code.
- `src-tauri`: Contains the Rust backend code.
- `public`: Contains static assets that are copied to the output directory.
- `node_modules`: Contains the frontend dependencies.
- `target`: Contains the compiled Rust code.

## Key Files

- `package.json`: Defines the project's metadata, dependencies, and scripts.
- `svelte.config.js`: Configuration file for Svelte.
- `tailwind.config.ts`: Configuration file for Tailwind CSS.
- `vite.config.js`: Configuration file for Vite.
- `src-tauri/tauri.conf.json`: Configuration file for Tauri.
- `src-tauri/Cargo.toml`: Defines the Rust dependencies and project metadata.

## NPM Scripts

- `npm run dev`: Starts the Vite development server.
- `npm run build`: Builds the frontend for production.
- `npm run preview`: Previews the production build.
- `npm run check`: Type-checks the Svelte code.
- `npm run tauri`: Runs the Tauri CLI.

## Key Dependencies

### Frontend

- `@tauri-apps/api`: Provides the JavaScript API for interacting with the Tauri backend.
- `svelte`: A component-based JavaScript framework.
- `tailwindcss`: A utility-first CSS framework.
- `typescript`: A typed superset of JavaScript.
- `vite`: A fast frontend build tool.
- `@langchain/core`, `@langchain/openai`, `langchain`: For integrating with LangChain services.

### Backend (Rust)

- `tauri`: The main Tauri library.
- `serde`: For serializing and deserializing data.
- `tokio`: For asynchronous programming.
