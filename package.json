{"name": "tauri-app", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "tauri": "tauri", "lint:fe": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "fmt:rs": "cd src-tauri && cargo fmt", "lint:rs": "cd src-tauri && cargo fmt -- --check && cargo clippy -- -D warnings", "lint": "npm run lint:fe && npm run lint:rs", "test": "vitest", "test:run": "vitest run"}, "license": "MIT", "dependencies": {"@langchain/core": "^0.3.51", "@langchain/openai": "^0.5.10", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-clipboard-manager": "^2.2.2", "@tauri-apps/plugin-dialog": "^2.2.1", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-http": "^2.4.3", "@tauri-apps/plugin-opener": "^2", "class-variance-authority": "^0.7.1", "echarts": "^5.6.0", "langchain": "^0.3.24", "svelte-echarts": "^1.0.0"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tauri-apps/cli": "^2.5.0", "autoprefixer": "^10.4.20", "bits-ui": "^0.22.0", "clsx": "^2.1.1", "lucide-svelte": "^0.488.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^2.1.3", "jsdom": "^25.0.1"}}