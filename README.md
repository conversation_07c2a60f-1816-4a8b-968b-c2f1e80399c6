# 临床研究项目管理系统

本项目是一个基于 Tauri、SvelteKit 和 TypeScript 构建的现代化临床研究项目管理桌面应用程序。系统专为临床研究机构设计，提供完整的项目生命周期管理、智能化的入排标准配置、人员管理、数据分析等功能，并集成了 AI 能力来提升工作效率。

## ✨ 系统特色

- **专业化设计**: 专门针对临床研究项目管理需求设计，涵盖项目全生命周期
- **现代化技术栈**: Tauri (Rust), SvelteKit, TypeScript, Tailwind CSS，确保高性能和优秀的用户体验
- **智能化功能**: 集成 Langchain.js 和大型语言模型，提供智能笔记分类、自动化入排标准生成等功能
- **灵活的数据管理**: 支持 SQLite 本地存储和 MongoDB 云端配置，满足不同场景需求
- **丰富的集成能力**: 支持 Notion、Lighthouse 等外部系统集成，实现数据互通
- **可视化分析**: 内置仪表盘和图表分析，提供项目进度和数据洞察

## 📚 文档导航

- **[用户使用手册](./docs/USER_MANUAL.md)** - 详细的功能使用指南
- **[系统架构文档](./docs/ARCHITECTURE.md)** - 技术架构和设计原理
- **[项目结构说明](./docs/PROJECT_STRUCTURE.md)** - 完整的项目文件结构
- **[开发指南](./CLAUDE.md)** - 开发环境配置和命令说明
- **[开发文档](./开发文档/)** - 技术开发文档集合

## 🚀 核心功能

### 📋 项目管理
- 完整的临床研究项目生命周期管理
- 申办方、研究药物、人员分配管理
- 批量人员授权导入和质量控制
- 补贴方案设计和费用计算

### 🎯 入排标准规则引擎
- 智能化的入排标准管理系统
- 规则模板定义和参数化配置
- AI 自动生成和批量操作
- JSON 格式的标准导入导出

### 👥 人员管理
- 全面的研究人员信息管理
- 智能搜索和筛选功能
- 组织架构和权限管理
- PI 标识和角色分配

### 🤖 智能笔记系统
- AI 驱动的智能笔记分类
- Notion 自动同步集成
- 批量处理和实时保存
- 富文本编辑和标签管理

### 📊 数据分析仪表盘
- 多维度的项目数据可视化
- 实时统计和趋势分析
- 人员分析和财务报表
- 自定义仪表盘配置

### ⚙️ 系统配置管理
- API 密钥安全管理
- 外部服务连接配置
- 界面个性化设置
- 路径和偏好配置

## 🛠️ 技术栈

### 核心框架
- **[Tauri](https://tauri.app/)**: 跨平台桌面应用开发框架
- **[SvelteKit](https://kit.svelte.dev/)**: 现代化的全栈 Web 应用框架

### 前端技术
- **[Svelte](https://svelte.dev/)** + **[TypeScript](https://www.typescriptlang.org/)**
- **[Tailwind CSS](https://tailwindcss.com/)** + **[ECharts](https://echarts.apache.org/)**

### 后端技术
- **[Rust](https://www.rust-lang.org/)** + **[rusqlite](https://github.com/rusqlite/rusqlite)**
- **[SQLite](https://www.sqlite.org/)** + **[MongoDB](https://www.mongodb.com/)**

### AI 与集成
- **[Langchain.js](https://js.langchain.com/)** + **[OpenAI API](https://openai.com/api/)**
- **[Notion API](https://developers.notion.com/)** 集成

## 🚀 快速开始

### 环境要求
- Node.js (推荐 v18+)
- Rust (最新稳定版)
- 系统特定依赖：
  - **macOS**: `xcode-select --install`
  - **Windows**: Visual Studio C++ Build Tools
  - **Linux**: `sudo apt install libwebkit2gtk-4.0-dev build-essential curl wget file`

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd tauri-app
   ```

2. **安装依赖**
   ```bash
   # 安装前端依赖
   npm install
   # 或使用 pnpm
   pnpm install
   ```

3. **配置数据库**
   ```bash
   # 创建数据库目录
   mkdir -p "/Users/<USER>/我的文档/sqlite"
   # 数据库将在首次运行时自动初始化
   ```

4. **启动开发服务器**
   ```bash
   # 启动开发模式
   npm run tauri dev
   ```

### 生产构建
```bash
# 构建生产版本
npm run tauri build
```

安装包将生成在：
- **macOS**: `src-tauri/target/release/bundle/dmg/`
- **Windows**: `src-tauri/target/release/bundle/msi/`
- **Linux**: `src-tauri/target/release/bundle/deb/`

## ⚙️ 配置说明

### 数据库配置
- **SQLite**: `/Users/<USER>/我的文档/sqlite/peckbyte.db`
- **MongoDB**: 通过系统配置模块设置连接信息

### 外部服务配置
- **OpenAI API**: 在系统设置中配置 API 密钥
- **Notion API**: 在系统设置中配置集成密钥
- **Lighthouse API**: 在系统设置中配置服务器地址

## 🔧 开发命令

```bash
# 开发模式
npm run dev              # 启动前端开发服务器
npm run tauri dev        # 启动 Tauri 开发模式

# 构建
npm run build            # 构建前端
npm run tauri build      # 构建 Tauri 应用

# 代码检查
npm run check            # TypeScript 类型检查
npm run check:watch      # 监听模式类型检查

# 预览
npm run preview          # 预览生产构建
```

## 📁 项目结构

```
tauri-app/
├── src/                    # 前端源码 (SvelteKit)
│   ├── lib/               # 共享代码库
│   │   ├── components/    # UI 组件
│   │   ├── services/      # 前端服务层
│   │   └── stores/        # 状态管理
│   └── routes/            # 页面路由
├── src-tauri/             # 后端源码 (Rust)
│   └── src/
│       ├── commands/      # Tauri 命令层
│       ├── services/      # 业务逻辑层
│       └── repositories/  # 数据访问层
├── docs/                  # 文档目录
├── 开发文档/              # 开发技术文档
└── README.md              # 项目说明
```

## 🤝 贡献指南

1. Fork 项目并创建功能分支
2. 遵循代码规范进行开发
3. 编写相应的测试用例
4. 提交 Pull Request 并描述变更内容

## 📞 技术支持

- 📧 **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 **文档**: 查看 `docs/` 目录下的详细文档
- 🔧 **开发文档**: 查看 `开发文档/` 目录下的技术文档

---

**© 2024 临床研究项目管理系统. 保留所有权利.**