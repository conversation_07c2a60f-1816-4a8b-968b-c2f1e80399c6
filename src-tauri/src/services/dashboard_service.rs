use log::{debug, error, info};
use std::error::Error;

use crate::models::dashboard::{
    ActiveCriteriaRatio, ChartDataPoint, CriteriaTypeDistribution, DashboardFilterParams,
    DashboardOverview, DateRangeParams, DiseaseDistribution, DrugGroupOverview, FinancialMetrics,
    MonthlyNewProjects, PIDistribution, PersonnelMetrics, PersonnelWorkload, PhaseTransition,
    ProjectCriteriaCount, ProjectDrugCount, ProjectStageDistribution, ProjectStatusDistribution,
    ProjectSubsidyOverview, RecruitmentStatusDistribution, RoleDistribution, SponsorDistribution,
    SubsidySchemeCount, SubsidyTypeDistribution, TimelineMetrics, UpcomingDeadline,
};
use crate::repositories::dashboard_repository::DashboardRepository;

/// 仪表盘服务
pub struct DashboardService {
    pub repository: DashboardRepository,
}

impl DashboardService {
    /// 创建新的仪表盘服务
    pub fn new(db_path: String) -> Self {
        let repository = DashboardRepository::new(db_path);
        Self { repository }
    }

    /// 获取仪表盘概览数据
    pub fn get_dashboard_overview(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<DashboardOverview, Box<dyn Error>> {
        info!("获取仪表盘概览数据");
        self.repository.get_dashboard_overview(filter_params)
    }

    /// 获取项目状态分布
    pub fn get_project_status_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<ProjectStatusDistribution>, Box<dyn Error>> {
        info!("获取项目状态分布");
        self.repository
            .get_project_status_distribution(filter_params)
    }

    /// 将项目状态分布转换为图表数据点
    pub fn convert_status_distribution_to_chart_data(
        &self,
        distribution: Vec<ProjectStatusDistribution>,
    ) -> Vec<ChartDataPoint> {
        distribution
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.status_name,
                value: item.project_count as f64,
                series: None,
            })
            .collect()
    }

    /// 获取项目阶段分布
    pub fn get_project_stage_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<ProjectStageDistribution>, Box<dyn Error>> {
        info!("获取项目阶段分布");
        if let Some(filter) = filter_params {
            debug!("项目阶段分布筛选参数: {:?}", filter);
        } else {
            debug!("项目阶段分布无筛选参数");
        }
        self.repository
            .get_project_stage_distribution(filter_params)
    }

    /// 将项目阶段分布转换为图表数据点
    pub fn convert_stage_distribution_to_chart_data(
        &self,
        distribution: Vec<ProjectStageDistribution>,
    ) -> Vec<ChartDataPoint> {
        distribution
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.stage_name,
                value: item.project_count as f64,
                series: None,
            })
            .collect()
    }

    /// 获取招募状态分布
    pub fn get_recruitment_status_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<RecruitmentStatusDistribution>, Box<dyn Error>> {
        info!("获取招募状态分布");
        if let Some(filter) = filter_params {
            debug!("招募状态分布筛选参数: {:?}", filter);
        } else {
            debug!("招募状态分布无筛选参数");
        }
        self.repository
            .get_recruitment_status_distribution(filter_params)
    }

    /// 将招募状态分布转换为图表数据点
    pub fn convert_recruitment_status_distribution_to_chart_data(
        &self,
        distribution: Vec<RecruitmentStatusDistribution>,
    ) -> Vec<ChartDataPoint> {
        distribution
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.status_name,
                value: item.project_count as f64,
                series: None,
            })
            .collect()
    }

    /// 获取疾病领域分布
    pub fn get_disease_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<DiseaseDistribution>, Box<dyn Error>> {
        info!("获取疾病领域分布");
        if let Some(filter) = filter_params {
            debug!("疾病领域分布筛选参数: {:?}", filter);
        } else {
            debug!("疾病领域分布无筛选参数");
        }
        self.repository.get_disease_distribution(filter_params)
    }

    /// 将疾病领域分布转换为图表数据点
    pub fn convert_disease_distribution_to_chart_data(
        &self,
        distribution: Vec<DiseaseDistribution>,
    ) -> Vec<ChartDataPoint> {
        distribution
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.disease_name,
                value: item.project_count as f64,
                series: None,
            })
            .collect()
    }

    /// 获取申办方项目分布
    pub fn get_sponsor_distribution(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<SponsorDistribution>, Box<dyn Error>> {
        info!("获取申办方项目分布");
        self.repository.get_sponsor_distribution(filter_params)
    }

    /// 将申办方项目分布转换为图表数据点
    pub fn convert_sponsor_distribution_to_chart_data(
        &self,
        distribution: Vec<SponsorDistribution>,
    ) -> Vec<ChartDataPoint> {
        distribution
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.sponsor_name,
                value: item.project_count as f64,
                series: None,
            })
            .collect()
    }

    /// 获取每月新启动项目数
    pub fn get_monthly_new_projects(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<Vec<MonthlyNewProjects>, Box<dyn Error>> {
        info!("获取每月新启动项目数");
        if let Some(filter) = filter_params {
            debug!("每月新启动项目数筛选参数: {:?}", filter);
        } else {
            debug!("每月新启动项目数无筛选参数");
        }

        match self.repository.get_monthly_new_projects(filter_params) {
            Ok(data) => {
                if data.is_empty() {
                    info!("未找到月度项目数据，可能数据库中没有有效的项目启动日期");
                    // 调用调试方法来检查数据状态
                    if let Err(e) = self.repository.get_project_date_statistics() {
                        error!("获取项目数据统计失败: {}", e);
                    }
                }
                Ok(data)
            }
            Err(e) => {
                error!("获取每月新启动项目数失败: {}", e);
                Err(e)
            }
        }
    }

    /// 将每月新启动项目数转换为图表数据点
    pub fn convert_monthly_new_projects_to_chart_data(
        &self,
        monthly_data: Vec<MonthlyNewProjects>,
    ) -> Vec<ChartDataPoint> {
        monthly_data
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.month,
                value: item.project_count as f64,
                series: None,
            })
            .collect()
    }

    /// 获取财务指标数据
    pub fn get_financial_metrics(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<FinancialMetrics, Box<dyn Error>> {
        info!("获取财务指标数据");

        // 获取补助类型分布
        let subsidy_types = self
            .repository
            .get_subsidy_type_distribution(filter_params)?;
        let subsidy_by_type: Vec<ChartDataPoint> = subsidy_types
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.subsidy_type_name,
                value: item.total_amount,
                series: None,
            })
            .collect();

        // 获取项目补助概览
        let project_subsidies = self
            .repository
            .get_project_subsidy_overview(filter_params)?;

        // 计算总补助金额
        let total_subsidy_amount: f64 = project_subsidies.iter().map(|p| p.total_amount).sum();

        // 计算平均项目补助
        let average_subsidy_per_project = if !project_subsidies.is_empty() {
            total_subsidy_amount / project_subsidies.len() as f64
        } else {
            0.0
        };

        // 获取前10个补助最高的项目
        let mut top_subsidized_projects = project_subsidies;
        top_subsidized_projects
            .sort_by(|a, b| b.total_amount.partial_cmp(&a.total_amount).unwrap());
        top_subsidized_projects.truncate(10);

        Ok(FinancialMetrics {
            total_subsidy_amount,
            average_subsidy_per_project,
            subsidy_by_type,
            top_subsidized_projects,
        })
    }

    /// 获取人员指标数据
    pub fn get_personnel_metrics(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<PersonnelMetrics, Box<dyn Error>> {
        info!("获取人员指标数据");

        // 获取角色分布
        let role_distributions = self.repository.get_role_distribution(filter_params)?;
        let personnel_by_role: Vec<ChartDataPoint> = role_distributions
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.role_name,
                value: item.project_count as f64,
                series: None,
            })
            .collect();

        // 获取人员工作负荷
        let personnel_utilization = self.repository.get_personnel_workload(filter_params)?;

        // 计算总人员数
        let total_personnel = personnel_utilization.len() as i64;

        // 获取PI分布（这里需要实现相应的仓储方法）
        let pi_distribution = vec![]; // 暂时为空，需要实现

        Ok(PersonnelMetrics {
            total_personnel,
            personnel_by_role,
            personnel_utilization,
            pi_distribution,
        })
    }

    /// 获取时间线指标数据
    pub fn get_timeline_metrics(
        &self,
        filter_params: Option<&DashboardFilterParams>,
    ) -> Result<TimelineMetrics, Box<dyn Error>> {
        info!("获取时间线指标数据");

        // 获取按月启动的项目数据
        let monthly_projects = self.repository.get_monthly_new_projects(filter_params)?;
        let projects_by_start_month: Vec<ChartDataPoint> = monthly_projects
            .into_iter()
            .map(|item| ChartDataPoint {
                name: item.month,
                value: item.project_count as f64,
                series: None,
            })
            .collect();

        // 计算平均项目持续时间（这里需要实现相应的计算逻辑）
        let average_project_duration = 365.0; // 暂时设为365天

        // 获取即将到期的项目（这里需要实现相应的仓储方法）
        let upcoming_deadlines = vec![]; // 暂时为空，需要实现

        // 获取项目阶段转换（这里需要实现相应的仓储方法）
        let project_phase_transitions = vec![]; // 暂时为空，需要实现

        Ok(TimelineMetrics {
            projects_by_start_month,
            average_project_duration,
            upcoming_deadlines,
            project_phase_transitions,
        })
    }
}
