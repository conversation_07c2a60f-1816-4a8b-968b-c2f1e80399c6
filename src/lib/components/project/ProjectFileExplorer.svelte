<script lang="ts">
  import { onDestroy } from 'svelte';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import type { FileInfo } from '$lib/services/fileSystemService';
  import Button from '$lib/components/ui/button/button.svelte';
  import { formatFileSize, formatDate } from '$lib/utils/index';

  // 组件属性
  const props = $props<{projectPath: string}>();

  // 状态
  let files = $state<FileInfo[]>([]);
  let isLoading = $state(false);
  let error = $state<string | null>(null);
  let refreshInterval: number | null = null;

  // 监听项目路径变化
  $effect(() => {
    if (props.projectPath) {
      loadFiles();

      // 设置定时刷新（每30秒）
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }

      refreshInterval = setInterval(() => {
        if (props.projectPath) {
          loadFiles(false); // 静默刷新，不显示加载状态
        }
      }, 30000) as unknown as number;
    } else {
      files = [];
    }
  });

  // 加载文件列表
  async function loadFiles(showLoading = true) {
    if (!props.projectPath) {
      console.error('项目路径为空，无法加载文件列表');
      error = '项目路径为空，无法加载文件列表';
      return;
    }

    console.log('正在加载文件列表，路径:', props.projectPath);

    if (showLoading) {
      isLoading = true;
    }
    error = null;

    try {
      // 检查路径是否存在
      if (!props.projectPath.trim()) {
        throw new Error('项目路径不能为空');
      }

      // 调用文件系统服务获取文件列表
      files = await fileSystemService.getFiles(props.projectPath);
      console.log('成功获取文件列表，共', files.length, '个文件');

      // 按照类型排序：文件夹在前，文件在后
      files.sort((a, b) => {
        // 判断是否为文件夹的简单方法（没有扩展名的可能是文件夹）
        const aIsDir = !a.name.includes('.');
        const bIsDir = !b.name.includes('.');

        if (aIsDir && !bIsDir) return -1;
        if (!aIsDir && bIsDir) return 1;
        return a.name.localeCompare(b.name);
      });
    } catch (err: any) {
      console.error('加载文件列表失败:', err);
      error = err.message || '加载文件列表失败';
      files = [];
    } finally {
      if (showLoading) {
        isLoading = false;
      }
    }
  }

  // 打开文件
  async function openFile(filePath: string) {
    try {
      await fileSystemService.openFile(filePath);
    } catch (err: any) {
      console.error('打开文件失败:', err);
      error = err.message || '打开文件失败';
    }
  }

  // 打开文件夹
  async function openFolder(folderPath: string) {
    try {
      await fileSystemService.openFolder(folderPath);
    } catch (err: any) {
      console.error('打开文件夹失败:', err);
      error = err.message || '打开文件夹失败';
    }
  }

  // 刷新文件列表
  function refreshFiles() {
    loadFiles();
  }

  // 是否为目录（简单基于是否包含扩展名判断）
  function isDirectory(fileName: string): boolean {
    return !fileName.includes('.');
  }

  // 获取文件扩展名（不含点，小写）；目录返回空串
  function getFileExtension(fileName: string): string {
    if (isDirectory(fileName)) return '';
    return fileName.split('.').pop()?.toLowerCase() || '';
  }

  // 获取文件图标
  function getFileIcon(fileName: string): string {
    const extension = getFileExtension(fileName);

    // 判断是否为文件夹（简单判断，没有扩展名的可能是文件夹）
    if (isDirectory(fileName)) {
      return 'folder';
    }

    // 根据扩展名返回不同图标
    switch (extension) {
      case 'pdf':
        return 'file-text';
      case 'doc':
      case 'docx':
        return 'file-text';
      case 'xls':
      case 'xlsx':
        return 'file-spreadsheet';
      case 'ppt':
      case 'pptx':
        return 'file-presentation';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'video';
      case 'mp3':
      case 'wav':
        return 'audio';
      case 'zip':
      case 'rar':
      case '7z':
        return 'archive';
      default:
        return 'file';
    }
  }

  // 组件销毁时清除定时器
  onDestroy(() => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }
  });
</script>

<div>
  <div class="flex justify-between items-center mb-2">
    <div class="flex items-center">
      <span class="text-sm text-gray-500">{files.length} 个文件</span>
    </div>
    <div class="flex gap-2">
      <Button variant="ghost" size="sm" on:click={refreshFiles} disabled={isLoading} class="h-8 px-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path><path d="M16 21h5v-5"></path></svg>
        刷新
      </Button>
    </div>
  </div>

  {#if !props.projectPath}
    <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 text-amber-800">
      <div class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
        <p>项目路径未设置，请在项目基本信息中设置项目路径</p>
      </div>
    </div>
  {:else if isLoading}
    <div class="flex justify-center items-center py-8 bg-white border rounded-lg">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 rounded-full animate-spin mr-2"></div>
      <span>正在加载项目文件...</span>
    </div>
  {:else if error}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
      <div class="flex items-start">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 mt-0.5"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>
        <div>
          <h3 class="font-semibold text-sm mb-1">无法加载项目文件</h3>
          <p class="text-sm">{error}</p>
          <p class="text-xs mt-1 text-red-600/70">项目路径: {props.projectPath || '未设置'}</p>
          <div class="flex gap-2 mt-2">
            <Button variant="outline" size="sm" on:click={() => openFolder(props.projectPath)} class="h-7 px-2 text-xs">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
              打开文件夹
            </Button>
          </div>
        </div>
      </div>
    </div>
  {:else if files.length === 0}
    <div class="bg-white border rounded-lg p-6 text-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto text-gray-400 mb-3"><path d="M20 11v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h8"></path><path d="M20 11h-4a2 2 0 0 1-2-2V5"></path><path d="M18 22V5"></path></svg>
      <h3 class="text-base font-medium text-gray-900 mb-1">项目文件夹为空</h3>
      <p class="text-gray-500 text-sm mb-3">该项目文件夹中暂无任何文件</p>
      <div class="flex justify-center">
        <Button variant="outline" size="sm" on:click={() => openFolder(props.projectPath)} class="h-8 px-3 text-sm">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
          打开文件夹
        </Button>
      </div>
    </div>
  {:else}
    <div class="bg-white border rounded-lg overflow-hidden">
      <div class="overflow-auto" style="max-height: 300px;">
        <table class="w-full border-collapse">
          <thead class="sticky top-0 bg-gray-50 z-10">
            <tr>
              <th class="text-left py-2 px-3 font-medium text-xs text-gray-700 w-full">文件名</th>
              <th class="text-left py-2 px-3 font-medium text-xs text-gray-700 hidden sm:table-cell w-[90px]">后缀</th>
              <th class="text-left py-2 px-3 font-medium text-xs text-gray-700 hidden md:table-cell w-[100px]">大小</th>
              <th class="text-left py-2 px-3 font-medium text-xs text-gray-700 hidden md:table-cell w-[160px]">修改日期</th>
              <th class="w-[80px] text-right py-2 px-3 font-medium text-xs text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            {#each files as file}
              <tr class="border-t hover:bg-gray-50">
                <td class="py-2 px-3 w-full">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-500 flex-shrink-0">
                      {#if getFileIcon(file.name) === 'folder'}
                        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                      {:else if getFileIcon(file.name) === 'file-text'}
                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <line x1="10" y1="9" x2="8" y2="9"></line>
                      {:else if getFileIcon(file.name) === 'image'}
                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                        <circle cx="9" cy="9" r="2"></circle>
                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                      {:else}
                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                      {/if}
                    </svg>
                    <span class="truncate min-w-0 flex-1 text-sm">{file.name}</span>
                    {#if !isDirectory(file.name)}
                      <span class="ml-2 inline-block sm:hidden text-[10px] px-1.5 py-0.5 rounded bg-gray-100 text-gray-700 border">{getFileExtension(file.name)}</span>
                    {/if}
                  </div>
                </td>
                <td class="py-2 px-3 hidden sm:table-cell text-xs text-gray-600 w-[90px]">
                  {#if isDirectory(file.name)}
                    —
                  {:else}
                    {getFileExtension(file.name)}
                  {/if}
                </td>
                <td class="py-2 px-3 hidden md:table-cell text-xs text-gray-600 w-[100px]">{formatFileSize(file.size)}</td>
                <td class="py-2 px-3 hidden md:table-cell text-xs text-gray-600 w-[160px]">{formatDate(file.modified_time)}</td>
                <td class="text-right py-2 px-3">
                  <button
                    onclick={() => getFileIcon(file.name) === 'folder' ? openFolder(file.path) : openFile(file.path)}
                    class="inline-flex items-center justify-center rounded-md text-xs font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-7 rounded-md px-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                  >
                    打开
                  </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
      <div class="p-2 bg-gray-50 border-t text-xs text-gray-500 flex justify-between items-center">
        <span>{files.length} 个文件</span>
        <Button variant="outline" size="sm" on:click={() => openFolder(props.projectPath)} class="h-7 px-2 text-xs">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
          打开文件夹
        </Button>
      </div>
    </div>
  {/if}
</div>
