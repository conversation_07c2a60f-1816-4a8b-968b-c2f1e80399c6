<script lang="ts">
  import { sqliteDictionaryService, type SqliteDict, type SqliteDictItem } from '$lib/services/sqliteDictionaryService';
  import { Button } from '$lib/components/ui/button';
  import { onMount } from 'svelte';
  import { fade } from 'svelte/transition';
  import { invoke } from '@tauri-apps/api/core';

  // 属性
  let { dictId } = $props<{
    dictId: number;
  }>();

  // 状态管理
  let dict = $state<SqliteDict | null>(null);
  let items = $state<SqliteDictItem[]>([]);
  let isLoading = $state(false);
  let isItemsLoading = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);
  let showAddForm = $state(false);

  // 搜索和过滤
  let searchQuery = $state('');
  let statusFilter = $state<'all' | 'active' | 'inactive'>('all');

  // 新字典项
  let newItem = $state<SqliteDictItem>({
    key: '',
    value: '',
    description: '',
    status: 'active'
  });

  // 编辑模式
  let editMode = $state(false);
  let editingItem = $state<SqliteDictItem | null>(null);
  let originalKey = $state<string>('');

  // 确认对话框
  let showConfirmDialog = $state(false);
  let confirmDialogTitle = $state('');
  let confirmDialogMessage = $state('');
  let confirmDialogCallback = $state<(() => void) | null>(null);

  // 批量操作
  let selectedItems = $state<string[]>([]);
  let selectAll = $state(false);
  let isBatchDeleting = $state(false);

  // 响应式计算全选状态
  $effect(() => {
    // 当过滤结果或选择项发生变化时，重新计算全选状态
    const filteredKeys = filteredItems.map(item => item.key);
    const selectedFilteredCount = filteredKeys.filter(key => selectedItems.includes(key)).length;
    selectAll = filteredKeys.length > 0 && selectedFilteredCount === filteredKeys.length;
  });

  // 过滤后的字典项
  let filteredItems = $derived(items.filter(item => {
    // 状态过滤
    if (statusFilter !== 'all' && item.status !== statusFilter) {
      return false;
    }

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        item.key.toLowerCase().includes(query) ||
        item.value.toLowerCase().includes(query) ||
        (item.description && item.description.toLowerCase().includes(query))
      );
    }

    return true;
  }));

  // 加载字典详情
  async function loadDict() {
    console.log(`开始加载字典: dictId = ${dictId}, 类型 = ${typeof dictId}`);
    console.log('当前路径:', window.location.pathname);

    // 先清除之前的错误信息
    error = null;
    isLoading = true;

    try {
      // 检查 dictId 是否有效
      if (!dictId) {
        console.error('字典ID无效:', dictId);
        error = '字典ID无效';
        isLoading = false;
        return;
      }

      // 尝试将 dictId 转换为数字
      const numericId = Number(dictId);
      console.log(`转换后的 ID: ${numericId}, 类型: ${typeof numericId}`);

      // 检查转换后的ID是否有效
      if (isNaN(numericId)) {
        console.error('字典ID不是有效的数字:', dictId);
        error = '字典ID不是有效的数字';
        isLoading = false;
        return;
      }

      // 尝试使用服务层方法加载字典
      console.log('尝试使用服务层方法加载字典');
      try {
        dict = await sqliteDictionaryService.getDictById(numericId);
        console.log('使用服务层加载字典成功:', dict);

        if (dict) {
          // 字典加载成功后，加载字典项
          console.log('字典加载成功，准备加载字典项');

          // 先将 isLoading 设置为 false，然后再加载字典项
          isLoading = false;

          // 使用 setTimeout 确保字典已经加载完成
          setTimeout(() => {
            loadDictItems();
          }, 100);

          return;
        }
      } catch (serviceErr: any) {
        console.error('服务层加载字典失败:', serviceErr);
        // 服务层失败，继续尝试直接调用
      }

      // 直接使用 invoke 调用后端命令
      console.log(`准备调用 sqlite_get_dict_by_id 命令，id = ${numericId}`);

      try {
        // 构造参数对象
        const params = { id: numericId };
        console.log('调用参数:', params);

        const response = await invoke<DictResponse>('sqlite_get_dict_by_id', params);
        console.log('加载字典响应:', response);

        if (!response) {
          console.error('响应为空');
          error = '加载字典失败: 响应为空';
          isLoading = false;
          return;
        }

        if (response.success && response.data) {
          dict = response.data;
          console.log('字典加载成功:', dict);

          // 字典加载成功后，加载字典项
          console.log('字典加载成功，准备加载字典项');

          // 先将 isLoading 设置为 false，然后再加载字典项
          isLoading = false;

          // 使用 setTimeout 确保字典已经加载完成
          setTimeout(() => {
            loadDictItems();
          }, 100);
        } else {
          error = response.error || '加载字典失败';
          console.error('加载字典失败:', response.error);
          isLoading = false;
        }
      } catch (invokeErr: any) {
        console.error('invoke 调用异常:', invokeErr);
        console.error('异常详情:', JSON.stringify(invokeErr));
        error = `调用异常: ${invokeErr.message || '未知错误'}`;
        isLoading = false;
      }
    } catch (err: any) {
      console.error('加载字典异常:', err);
      console.error('异常详情:', JSON.stringify(err));
      error = `加载字典异常: ${err.message || '未知错误'}`;
      isLoading = false;
    }
  }

  // 加载字典项
  async function loadDictItems() {
    console.log(`开始加载字典项: dictId = ${dictId}, 类型 = ${typeof dictId}`);
    console.log('当前路径:', window.location.pathname);

    // 先清除之前的错误信息
    error = null;
    isItemsLoading = true;

    try {
      // 检查 dictId 是否有效
      if (!dictId) {
        console.error('字典ID无效:', dictId);
        error = '字典ID无效';
        isItemsLoading = false;
        return;
      }

      // 尝试将 dictId 转换为数字
      const numericId = Number(dictId);
      console.log(`转换后的 ID: ${numericId}, 类型: ${typeof numericId}`);

      // 检查转换后的ID是否有效
      if (isNaN(numericId)) {
        console.error('字典ID不是有效的数字:', dictId);
        error = '字典ID不是有效的数字';
        isItemsLoading = false;
        return;
      }

      // 尝试使用服务层方法加载字典项
      console.log('尝试使用服务层方法加载字典项');
      try {
        const serviceItems = await sqliteDictionaryService.getDictItems(numericId);
        console.log('使用服务层加载字典项成功:', serviceItems);

        if (serviceItems && Array.isArray(serviceItems)) {
          items = serviceItems;
          console.log(`加载到 ${items.length} 个字典项`);
          isItemsLoading = false;
          return;
        }
      } catch (serviceErr: any) {
        console.error('服务层加载字典项失败:', serviceErr);
        // 服务层失败，继续尝试直接调用
      }

      console.log(`准备调用 sqlite_get_dict_items 命令，dictionary_id = ${numericId}`);

      try {
        // 构造参数对象
        const params = { dictionaryId: numericId };
        console.log('调用参数:', params);

        // 使用 try-catch 包裹 invoke 调用，以捕获可能的异常
        const response = await invoke<DictResponse>('sqlite_get_dict_items', params);
        console.log('加载字典项响应:', response);

        if (!response) {
          console.error('响应为空');
          error = '加载字典项失败: 响应为空';
          isItemsLoading = false;
          return;
        }

        if (response.success && response.data) {
          // 检查响应数据是否是数组
          if (Array.isArray(response.data)) {
            // 更新字典项列表
            items = response.data;
            console.log(`加载到 ${items.length} 个字典项`);
            // 成功加载后清除错误信息
            error = null;
          } else {
            console.error('响应数据不是数组:', response.data);
            error = '加载字典项失败: 响应数据格式错误';
          }
        } else {
          console.error('加载字典项失败:', response.error);
          error = response.error || '加载字典项失败';
        }
      } catch (invokeErr: any) {
        console.error('invoke 调用异常:', invokeErr);
        console.error('异常详情:', JSON.stringify(invokeErr));
        error = `调用异常: ${invokeErr.message || '未知错误'}`;
      }
    } catch (err: any) {
      console.error('加载字典项异常:', err);
      console.error('异常详情:', JSON.stringify(err));
      error = `加载字典项异常: ${err.message || '未知错误'}`;
    } finally {
      isItemsLoading = false;
    }
  }

  // 验证字典项键是否符合规范（只允许英文、数字、下划线）
  function validateItemKey(key: string): boolean {
    const regex = /^[a-zA-Z0-9_]+$/;
    return regex.test(key);
  }

  // 开始编辑字典项
  function startEditItem(item: SqliteDictItem) {
    editMode = true;
    editingItem = { ...item };
    originalKey = item.key;
    showAddForm = true;

    // 填充表单
    newItem = {
      key: item.key,
      value: item.value,
      description: item.description || '',
      status: item.status || 'active'
    };
  }

  // 取消编辑
  function cancelEdit() {
    editMode = false;
    editingItem = null;
    originalKey = '';

    // 重置表单
    newItem = {
      key: '',
      value: '',
      description: '',
      status: 'active'
    };
  }

  // 切换添加表单显示
  // 注意: 这个函数在模板中通过 Button 组件的 onclick 属性使用
  // <Button onclick={() => showAddForm = !showAddForm}>
  function toggleAddForm() {
    showAddForm = !showAddForm;
    if (!showAddForm) {
      cancelEdit();
    }
  }

  // 清除搜索和过滤
  // 注意: 这个函数在模板中通过 Button 组件的 onclick 属性使用
  // <Button onclick={() => { searchQuery = ''; statusFilter = 'all'; }}>
  function clearFilters() {
    searchQuery = '';
    statusFilter = 'all';
  }

  // 显示确认对话框
  function showConfirm(title: string, message: string, callback: () => void) {
    confirmDialogTitle = title;
    confirmDialogMessage = message;
    confirmDialogCallback = callback;
    showConfirmDialog = true;
  }

  // 注意: 这些函数将在模板中使用

  // 添加或更新字典项
  async function addDictItem() {
    console.log(`添加或更新字典项函数被调用，dictId = ${dictId}`);
    console.log(`当前表单数据:`, newItem);
    console.log(`编辑模式: ${editMode}, 原始键: ${originalKey}`);
    console.log('当前路径:', window.location.pathname);

    // 检查 dictId 是否有效
    if (!dictId) {
      console.error('字典ID无效:', dictId);
      error = '字典ID无效';
      return;
    }

    // 检查表单数据是否有效
    if (!newItem.key) {
      console.error('字典项键为空');
      error = '字典项键不能为空';
      return;
    }

    if (!newItem.value) {
      console.error('字典项值为空');
      error = '字典项值不能为空';
      return;
    }

    if (!validateItemKey(newItem.key)) {
      console.error('字典项键格式无效:', newItem.key);
      error = '字典项键只能包含英文字母、数字和下划线';
      return;
    }

    error = null;
    success = null;

    try {
      // 尝试将 dictId 转换为数字
      const numericId = Number(dictId);
      console.log(`转换后的 ID: ${numericId}, 类型: ${typeof numericId}`);

      // 检查转换后的ID是否有效
      if (isNaN(numericId)) {
        console.error('字典ID不是有效的数字:', dictId);
        error = '字典ID不是有效的数字';
        return;
      }

      if (editMode && editingItem) {
        // 更新字典项
        console.log(`准备调用 sqlite_update_dict_item 命令，dictionary_id = ${numericId}, key = ${originalKey}`);
        console.log(`参数类型: dictionary_id = ${typeof numericId}, key = ${typeof originalKey}, item = ${typeof newItem}`);

        // 构造参数对象
        const params = {
          dictionaryId: numericId,
          key: originalKey,
          item: newItem
        };
        console.log('调用参数:', params);

        try {
          const response = await invoke<DictResponse>('sqlite_update_dict_item', params);
          console.log('更新字典项响应:', response);

          if (!response) {
            console.error('响应为空');
            error = '更新字典项失败: 响应为空';
            return;
          }

          if (response.success) {
            success = '更新字典项成功';
          } else {
            error = response.error || '更新字典项失败';
            return;
          }
        } catch (invokeErr: any) {
          console.error('invoke 调用异常:', invokeErr);
          console.error('异常详情:', JSON.stringify(invokeErr));
          error = `调用异常: ${invokeErr.message || '未知错误'}`;
          return;
        }
      } else {
        // 添加新字典项
        console.log(`准备调用 sqlite_add_dict_item 命令，dictionary_id = ${numericId}`);
        console.log(`参数类型: dictionary_id = ${typeof numericId}, item = ${typeof newItem}`);

        // 构造参数对象
        const params = {
          dictionaryId: numericId,
          item: newItem
        };
        console.log('调用参数:', params);

        try {
          console.log('开始调用 sqlite_add_dict_item 命令...');

          // 使用 try-catch 包裹 invoke 调用，以捕获可能的异常
          let response;
          try {
            response = await invoke<DictResponse>('sqlite_add_dict_item', params);
            console.log('添加字典项响应:', response);
          } catch (innerErr: any) {
            console.error('内部 invoke 调用异常:', innerErr);
            if (typeof innerErr === 'object') {
              console.error('内部异常对象属性:', Object.keys(innerErr));
              for (const key in innerErr) {
                console.error(`${key}:`, innerErr[key]);
              }
            }
            throw innerErr;
          }

          if (!response) {
            console.error('响应为空');
            error = '添加字典项失败: 响应为空';
            return;
          }

          if (response.success) {
            success = '添加字典项成功';
          } else {
            error = response.error || '添加字典项失败';
            return;
          }
        } catch (invokeErr: any) {
          console.error('invoke 调用异常:', invokeErr);
          console.error('异常类型:', typeof invokeErr);
          console.error('异常详情:', JSON.stringify(invokeErr, null, 2));

          // 尝试提取更多错误信息
          let errorMessage = '未知错误';
          if (invokeErr) {
            if (typeof invokeErr === 'string') {
              errorMessage = invokeErr;
            } else if (invokeErr.message) {
              errorMessage = invokeErr.message;
            } else if (invokeErr.toString) {
              errorMessage = invokeErr.toString();
            }
          }

          error = `调用异常: ${errorMessage}`;
          return;
        }
      }

      // 重置表单
      newItem = {
        key: '',
        value: '',
        description: '',
        status: 'active'
      };
      editMode = false;
      editingItem = null;
      originalKey = '';
      showAddForm = false;

      // 重新加载字典项
      console.log('准备重新加载字典项');
      await loadDictItems();
    } catch (err: any) {
      console.error('添加或更新字典项失败:', err);
      console.error('异常详情:', JSON.stringify(err));
      error = `添加或更新字典项失败: ${err.message || '未知错误'}`;
    }
  }

  // 定义响应类型
  interface DictResponse {
    success: boolean;
    error?: string;
    data?: any;
  }

  // 删除字典项
  async function deleteDictItem(key: string) {
    console.log(`删除字典项函数被调用，dictId = ${dictId}, key = ${key}`);
    console.log('当前路径:', window.location.pathname);

    // 使用自定义确认对话框
    showConfirm(
      '删除字典项',
      `确定要删除字典项 "${key}" 吗？此操作不可撤销。`,
      async () => {
        console.log('用户确认删除字典项');

        error = null;
        success = null;

        // 检查 dictId 是否有效
        if (!dictId) {
          console.error('字典ID无效:', dictId);
          error = '字典ID无效';
          return;
        }

        // 检查 key 是否有效
        if (!key) {
          console.error('字典项键无效:', key);
          error = '字典项键无效';
          return;
        }

        // 尝试使用服务层方法删除字典项
        console.log('尝试使用服务层方法删除字典项');
        try {
          const numericId = Number(dictId);
          const result = await sqliteDictionaryService.deleteDictItem(numericId, key);
          console.log('使用服务层删除字典项结果:', result);

          if (result) {
            success = '删除字典项成功';

            // 从当前列表中移除已删除的项
            console.log(`从列表中移除字典项: key = ${key}`);
            console.log(`删除前列表长度: ${items.length}`);
            items = items.filter(item => item.key !== key);
            console.log(`删除后列表长度: ${items.length}`);

            // 重新加载字典项，确保数据同步
            await loadDictItems();
            return;
          }
        } catch (serviceErr: any) {
          console.error('服务层删除字典项失败:', serviceErr);
          // 服务层失败，继续尝试直接调用
        }

        try {
          console.log(`准备调用 sqlite_delete_dict_item 命令，dictionary_id = ${dictId}, key = ${key}`);
          console.log(`参数类型: dictionary_id = ${typeof dictId}, key = ${typeof key}`);

          // 尝试将 dictId 转换为数字
          const numericId = Number(dictId);
          console.log(`转换后的 ID: ${numericId}, 类型: ${typeof numericId}`);

          // 构造参数对象
          const params = {
            dictionaryId: numericId,
            key: key
          };
          console.log('调用参数:', params);

          const response = await invoke<DictResponse>('sqlite_delete_dict_item', params);
          console.log('调用响应:', response);

          if (!response) {
            console.error('响应为空');
            error = '删除字典项失败: 响应为空';
            return;
          }

          if (response.success) {
            success = '删除字典项成功';

            // 从当前列表中移除已删除的项
            console.log(`从列表中移除字典项: key = ${key}`);
            console.log(`删除前列表长度: ${items.length}`);
            items = items.filter(item => item.key !== key);
            console.log(`删除后列表长度: ${items.length}`);

            // 重新加载字典项，确保数据同步
            await loadDictItems();
          } else {
            error = response.error || '删除字典项失败';
            console.error('删除字典项失败:', response.error);
          }
        } catch (err: any) {
          console.error('调用失败:', err);
          console.error('异常详情:', JSON.stringify(err));
          error = `调用失败: ${err.message || '未知错误'}`;
        }
      }
    );
  }

  // 删除字典
  async function deleteDict() {
    console.log(`删除字典函数被调用，dictId = ${dictId}`);

    // 使用自定义确认对话框
    showConfirm(
      '删除字典',
      '确定要删除此字典吗？字典中的所有字典项也将被删除。此操作不可撤销。',
      async () => {
        console.log('用户确认删除字典');

        error = null;
        success = null;

        console.log(`准备调用 sqlite_delete_dict 命令，ID = ${dictId}`);
        console.log(`参数类型: id = ${typeof dictId}`);

        try {
          // 尝试直接使用数字类型
          const numericId = Number(dictId);
          console.log(`转换后的 ID: ${numericId}, 类型: ${typeof numericId}`);

          const response = await invoke<DictResponse>('sqlite_delete_dict', { id: numericId });
          console.log('调用响应:', response);

          if (response.success) {
            success = '删除字典成功';
            // 延迟跳转，给用户时间看到成功提示
            setTimeout(() => {
              window.location.href = '/sqlite-dictionaries';
            }, 1500);
          } else {
            error = response.error || '删除字典失败';
          }
        } catch (err: any) {
          console.error('调用失败:', err);
          error = err.message || '删除字典失败';
        }
      }
    );
  }

  // 批量操作函数
  function toggleSelectAll() {
    const filteredKeys = filteredItems.map(item => item.key);
    const selectedFilteredCount = filteredKeys.filter(key => selectedItems.includes(key)).length;

    if (selectedFilteredCount === filteredKeys.length) {
      // 如果当前过滤结果中的所有项目都已选中，则取消选择这些项目
      selectedItems = selectedItems.filter(key => !filteredKeys.includes(key));
    } else {
      // 否则选择当前过滤结果中的所有项目
      const newSelected = [...selectedItems];
      filteredKeys.forEach(key => {
        if (!newSelected.includes(key)) {
          newSelected.push(key);
        }
      });
      selectedItems = newSelected;
    }
  }

  function toggleSelectItem(key: string) {
    if (selectedItems.includes(key)) {
      selectedItems = selectedItems.filter(item => item !== key);
    } else {
      selectedItems = [...selectedItems, key];
    }
  }

  // 批量删除字典项
  async function batchDeleteItems() {
    if (selectedItems.length === 0) {
      error = '请选择要删除的字典项';
      return;
    }

    const keysToDelete = selectedItems;
    const selectedItemsInfo = keysToDelete.map(key => {
      const item = items.find((item: any) => item.key === key);
      return item ? `${key} (${item.value})` : key;
    });

    const confirmMessage = `确定要删除以下 ${keysToDelete.length} 个字典项吗？此操作不可撤销。\n\n${selectedItemsInfo.slice(0, 10).join('\n')}${keysToDelete.length > 10 ? `\n... 还有 ${keysToDelete.length - 10} 个项目` : ''}`;

    showConfirm(
      '批量删除字典项',
      confirmMessage,
      async () => {
        console.log('用户确认批量删除字典项');

        error = null;
        success = null;
        isBatchDeleting = true;

        try {
          const deletedCount = await sqliteDictionaryService.batchDeleteDictItems(dictId, keysToDelete);
          success = `成功删除 ${deletedCount} 个字典项`;

          // 清空选择
          selectedItems = [];

          // 重新加载字典项
          await loadDictItems();
        } catch (err: any) {
          console.error('批量删除字典项失败:', err);
          error = err.message || '批量删除字典项失败';
        } finally {
          isBatchDeleting = false;
        }
      }
    );
  }

  // 组件挂载时加载字典
  onMount(() => {
    if (dictId) {
      loadDict();
    }
  });
</script>

<div class="dictionary-detail">
  <!-- 页面标题和返回按钮 -->
  <div class="flex justify-between items-center mb-4">
    <h1 class="text-xl font-bold">字典详情</h1>
    <a href="/sqlite-dictionaries" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>
      返回字典列表
    </a>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800 rounded-md p-3 text-red-800 dark:text-red-200 mb-4 flex items-center gap-2" transition:fade={{duration: 200}}>
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-circle"><circle cx="12" cy="12" r="10"/><line x1="12" x2="12" y1="8" y2="12"/><line x1="12" x2="12.01" y1="16" y2="16"/></svg>
      <p>{error}</p>
      <button
        class="ml-auto text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
        onclick={() => error = null}
        aria-label="关闭错误提示"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
      </button>
    </div>
  {/if}

  <!-- 成功提示 -->
  {#if success}
    <div class="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-md p-3 text-green-800 dark:text-green-200 mb-4 flex items-center gap-2" transition:fade={{duration: 200}}>
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><polyline points="22 4 12 14.01 9 11.01"/></svg>
      <p>{success}</p>
      <button
        class="ml-auto text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
        onclick={() => success = null}
        aria-label="关闭成功提示"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
      </button>
    </div>
  {/if}

  <!-- 加载中 -->
  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
      <p>加载中...</p>
    </div>
  {:else if !dict}
    <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800 rounded-md p-4 text-amber-800 dark:text-amber-200 mb-6">
      <p>字典不存在或已被删除</p>
    </div>
  {:else}
    <!-- 字典信息 -->
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden mb-6">
      <div class="flex justify-between items-center p-4 border-b border-slate-200 dark:border-slate-700">
        <div class="flex items-center gap-2">
          <h2 class="text-lg font-semibold">字典信息</h2>
          <span class="text-xs bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 px-2 py-0.5 rounded-full">
            ID: {dict.id}
          </span>
        </div>

        <div class="flex gap-2">
          <Button variant="outline" size="sm" href={`/sqlite-dictionaries/${dict.id}/edit`} class="flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
            编辑字典
          </Button>
          <button
            type="button"
            class="inline-flex items-center gap-1 h-9 px-3 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:bg-red-800 dark:hover:bg-red-700 dark:focus:ring-red-600"
            onclick={() => {
              console.log('删除字典按钮被点击');
              deleteDict();
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
            删除字典
          </button>
        </div>
      </div>

      <div class="p-5">
        <div class="flex flex-col gap-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
              <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">字典名称</p>
              <p class="text-base font-medium text-slate-800 dark:text-slate-200">{dict.name}</p>
            </div>

            <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
              <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">类型</p>
              <p class="text-base font-medium text-slate-800 dark:text-slate-200">{dict.type_ || 'list'}</p>
            </div>

            <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
              <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">创建时间</p>
              <p class="text-base font-medium text-slate-800 dark:text-slate-200">
                {dict.created_at ? new Date(dict.created_at).toLocaleString() : '-'}
              </p>
            </div>
          </div>

          {#if dict.description}
            <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
              <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">描述</p>
              <p class="text-base text-slate-800 dark:text-slate-200">{dict.description}</p>
            </div>
          {/if}

          <div class="bg-slate-50 dark:bg-slate-700/30 rounded-md p-3">
            <p class="text-xs text-slate-500 dark:text-slate-400 mb-1">标签</p>
            <div class="flex flex-wrap gap-1 mt-1">
              {#if dict.tags && dict.tags.length > 0}
                {#each dict.tags as tag}
                  <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                    {tag}
                  </span>
                {/each}
              {:else}
                <span class="text-slate-500 dark:text-slate-400">-</span>
              {/if}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 字典项管理 -->
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden">
      <div class="flex justify-between items-center p-4 border-b border-slate-200 dark:border-slate-700">
        <div class="flex items-center gap-2">
          <h2 class="text-lg font-semibold">字典项管理</h2>
          <span class="text-xs bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 px-2 py-0.5 rounded-full">
            {items.length} 条记录
          </span>
        </div>

        <Button
          variant="outline"
          size="sm"
          class="flex items-center gap-1"
          onclick={() => showAddForm = !showAddForm}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          {showAddForm ? '隐藏表单' : '添加字典项'}
        </Button>
      </div>

      <!-- 搜索和过滤工具栏 -->
      <div class="border-b border-slate-200 dark:border-slate-700 p-4 bg-slate-50 dark:bg-slate-800/50">
        <!-- 批量操作工具栏 -->
        {#if selectedItems.length > 0}
          <div class="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <span class="text-sm text-blue-800 dark:text-blue-200">
                  已选择 {selectedItems.length} 个字典项
                </span>
                {#if isBatchDeleting}
                  <div class="flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400">
                    <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>删除中...</span>
                  </div>
                {/if}
              </div>
              <div class="flex gap-2">
                <button
                  type="button"
                  class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isBatchDeleting}
                  onclick={() => {
                    selectedItems = [];
                  }}
                >
                  取消选择
                </button>
                <button
                  type="button"
                  class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isBatchDeleting}
                  onclick={batchDeleteItems}
                >
                  {isBatchDeleting ? '删除中...' : '批量删除'}
                </button>
              </div>
            </div>
          </div>
        {/if}

        <div class="flex flex-col md:flex-row gap-3 items-end">
          <div class="flex-1">
            <label for="search-query" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">搜索字典项</label>
            <div class="relative">
              <input
                id="search-query"
                type="text"
                bind:value={searchQuery}
                placeholder="搜索键、值或描述..."
                class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 pl-9 pr-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 -translate-y-1/2 text-slate-400"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
            </div>
          </div>

          <div class="w-full md:w-48">
            <label for="status-filter" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">状态过滤</label>
            <select
              id="status-filter"
              bind:value={statusFilter}
              class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">所有状态</option>
              <option value="active">启用</option>
              <option value="inactive">禁用</option>
            </select>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onclick={() => { searchQuery = ''; statusFilter = 'all'; }}
            class="h-9"
          >
            清除过滤
          </Button>
        </div>
      </div>

      <!-- 添加字典项表单 -->
      {#if showAddForm}
        <div class="border-b border-slate-200 dark:border-slate-700 p-4" transition:fade={{duration: 200}}>
          <h3 class="text-base font-medium mb-3">{editMode ? '编辑字典项' : '添加字典项'}</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label for="item-key" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">键</label>
              <input
                id="item-key"
                type="text"
                bind:value={newItem.key}
                placeholder="输入字典项键"
                class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                键只能包含英文字母、数字和下划线
              </p>
            </div>

            <div>
              <label for="item-value" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">值</label>
              <input
                id="item-value"
                type="text"
                bind:value={newItem.value}
                placeholder="输入字典项值"
                class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label for="item-status" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">状态</label>
              <select
                id="item-status"
                bind:value={newItem.status}
                class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </select>
            </div>

            <div>
              <label for="item-description" class="block text-xs font-medium text-slate-500 dark:text-slate-400 mb-1">描述</label>
              <input
                id="item-description"
                type="text"
                bind:value={newItem.description}
                placeholder="输入字典项描述"
                class="w-full h-9 rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-1.5 text-sm text-slate-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div class="flex justify-end gap-2">
            {#if editMode}
              <Button variant="ghost" size="sm" onclick={cancelEdit}>
                取消
              </Button>
            {/if}
            <Button size="sm" onclick={addDictItem}>
              {editMode ? '保存更改' : '添加字典项'}
            </Button>
          </div>
        </div>
      {/if}

      <!-- 字典项列表 -->
      {#if isItemsLoading}
        <div class="flex justify-center items-center py-8">
          <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 dark:border-blue-400 rounded-full animate-spin mr-2"></div>
          <p>加载中...</p>
        </div>
      {:else if items.length === 0}
        <div class="text-center py-8 text-slate-500 dark:text-slate-400">
          <p>暂无字典项数据</p>
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-slate-50 dark:bg-slate-700/50">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onchange={toggleSelectAll}
                    disabled={isBatchDeleting || filteredItems.length === 0}
                    class="rounded border-slate-300 dark:border-slate-600 text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">键</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">值</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">状态</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">描述</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-slate-200 dark:divide-slate-700">
              {#each filteredItems as item}
                <tr class="hover:bg-slate-50 dark:hover:bg-slate-700/50">
                  <td class="px-4 py-3 text-sm w-12">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.key)}
                      onchange={() => toggleSelectItem(item.key)}
                      disabled={isBatchDeleting}
                      class="rounded border-slate-300 dark:border-slate-600 text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                  </td>
                  <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200 font-mono">{item.key}</td>
                  <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">{item.value}</td>
                  <td class="px-4 py-3 text-sm">
                    <span class="px-2 py-1 text-xs rounded-full {
                      item.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300' :
                      'bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300'
                    }">
                      {item.status === 'active' ? '启用' : '禁用'}
                    </span>
                  </td>
                  <td class="px-4 py-3 text-sm text-slate-800 dark:text-slate-200">{item.description || '-'}</td>
                  <td class="px-4 py-3 text-sm">
                    <div class="flex gap-2">
                      <button
                        type="button"
                        class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                        onclick={() => startEditItem(item)}
                      >
                        编辑
                      </button>
                      <button
                        type="button"
                        class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        onclick={() => {
                          console.log(`删除字典项按钮被点击，key = ${item.key}`);
                          deleteDictItem(item.key);
                        }}
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>

        <!-- 无搜索结果提示 -->
        {#if filteredItems.length === 0}
          <div class="text-center py-8 text-slate-500 dark:text-slate-400">
            <p>没有符合条件的字典项</p>
            <Button
              variant="ghost"
              size="sm"
              onclick={() => { searchQuery = ''; statusFilter = 'all'; }}
              class="mt-2"
            >
              清除过滤条件
            </Button>
          </div>
        {/if}
      {/if}
    </div>
  {/if}
  <!-- 自定义确认对话框 -->
  {#if showConfirmDialog}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold mb-2">{confirmDialogTitle}</h3>
        <p class="text-slate-600 dark:text-slate-300 mb-6">{confirmDialogMessage}</p>
        <div class="flex justify-end gap-3">
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-slate-200 text-slate-800 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600"
            onclick={() => showConfirmDialog = false}
          >
            取消
          </button>
          <button
            type="button"
            class="px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600"
            onclick={() => {
              showConfirmDialog = false;
              if (confirmDialogCallback) {
                confirmDialogCallback();
              }
            }}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
