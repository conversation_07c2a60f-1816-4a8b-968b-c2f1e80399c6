<script lang="ts">
  import { cn } from "$lib/utils";

  // Props
  export let title = "";
  export let count = 0;
  export let color = "blue";
  export let isActive = false;
  export let onClick = () => {};

  // Get color classes based on the provided color
  function getColorClasses(colorName: string) {
    const colorMap: Record<string, {
      bg: string,
      activeBg: string,
      text: string,
      activeText: string,
      border: string,
      activeBorder: string
    }> = {
      blue: {
        bg: "bg-blue-50",
        activeBg: "bg-blue-500",
        text: "text-blue-700",
        activeText: "text-white",
        border: "border-blue-200",
        activeBorder: "border-blue-500"
      },
      green: {
        bg: "bg-green-50",
        activeBg: "bg-green-500",
        text: "text-green-700",
        activeText: "text-white",
        border: "border-green-200",
        activeBorder: "border-green-500"
      },
      purple: {
        bg: "bg-purple-50",
        activeBg: "bg-purple-500",
        text: "text-purple-700",
        activeText: "text-white",
        border: "border-purple-200",
        activeBorder: "border-purple-500"
      },
      orange: {
        bg: "bg-orange-50",
        activeBg: "bg-orange-500",
        text: "text-orange-700",
        activeText: "text-white",
        border: "border-orange-200",
        activeBorder: "border-orange-500"
      }
    };

    return colorMap[colorName] || colorMap.blue;
  }

  const colorClasses = getColorClasses(color);
</script>

<button
  class={cn(
    "inline-flex items-center justify-center px-4 py-2 rounded-full border transition-all duration-200 cursor-pointer",
    "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2",
    isActive 
      ? `${colorClasses.activeBg} ${colorClasses.activeText} ${colorClasses.activeBorder} shadow-md`
      : `${colorClasses.bg} ${colorClasses.text} ${colorClasses.border} hover:${colorClasses.activeBg} hover:${colorClasses.activeText}`,
    "min-h-[60px] text-sm font-medium"
  )}
  onclick={onClick}
  aria-label={`查看${title}项目列表`}
>
  <div class="flex flex-col items-center gap-1">
    <span class="text-xs opacity-90">{title}</span>
    <span class="text-lg font-bold">{count}</span>
  </div>
</button>
