# 批量人员授权导入系统开发文档

## 功能概述

批量人员授权导入系统是项目管理应用的重要功能模块，允许用户通过CSV文件批量导入项目人员角色分配信息，并提供质量控制检查功能，确保项目团队配置的完整性和合规性。

## 系统架构

### 前端架构 (src/)

#### 核心组件
- **CsvImportDialog.svelte** (`src/lib/components/csv-import/CsvImportDialog.svelte`)
  - 主要的CSV导入对话框组件
  - 支持文件上传、数据预览、验证和导入流程
  - 提供进度指示器和错误处理

- **QualityControlPanel.svelte** (`src/lib/components/csv-import/QualityControlPanel.svelte`)
  - 质量控制检查面板组件
  - 显示项目人员配置状态和缺失角色信息
  - 支持过滤和统计功能

#### 服务层
- **csvImportService.ts** (`src/lib/services/csvImportService.ts`)
  - 封装CSV导入相关的API调用
  - 提供文件读取、数据验证、导入执行等功能
  - 包含质量控制检查和统计信息获取

#### 页面集成
- **项目列表页** (`src/routes/projects/+page.svelte`)
  - 添加"批量导入"按钮
  - 集成质量控制指示器显示缺失关键角色
  - 导入完成后自动刷新项目列表

- **质量控制页** (`src/routes/projects/quality-control/+page.svelte`)
  - 专门的质量控制检查页面
  - 提供详细的项目人员配置分析
  - 包含操作指南和相关链接

### 后端架构 (src-tauri/)

#### 数据模型 (Models)
- **CSV导入数据结构** (`src-tauri/src/models/project_management.rs`)
  - `CsvPersonnelRow`: CSV行数据结构
  - `CsvImportValidation`: 验证结果结构
  - `CsvImportResult`: 导入结果结构
  - `QualityControlResult`: 质量控制检查结果

#### 服务层 (Services)
- **CsvImportService** (`src-tauri/src/services/csv_import_service.rs`)
  - 核心业务逻辑实现
  - CSV解析和数据验证
  - 项目匹配和人员角色分配
  - 质量控制检查算法

#### 命令层 (Commands)
- **CSV导入命令** (`src-tauri/src/commands/csv_import_commands.rs`)
  - `parse_csv_personnel_data`: 解析和验证CSV数据
  - `execute_csv_personnel_import`: 执行导入操作
  - `perform_project_quality_control`: 执行质量控制检查
  - `get_csv_import_template`: 获取CSV模板

## 核心功能详解

### 1. CSV文件解析与验证

#### 支持的CSV格式
```csv
序号,项目,项目全称,启动日期,授权人员,提交人,修改人,提交时间,修改时间,填写时长,填写设备,操作系统,浏览器,填写地区,IP
1,202102-阿斯利康-哮喘-LOGOS,一项随机、双盲、双模拟、平行分组、多中心研究,2021/12/3,"1：（人员名称：肖祖克，角色：主要研究者），2：（人员名称：李星，角色：SUBI），3：（人员名称：程晶晶，角色：临床协调员(CRC)），4：（人员名称：王丽艳，角色：CRA）",,,2025/6/12 16:45,2025/6/12 16:45,9分2秒,Windows,Windows 10,Chrome *********,江西省,***************
```

#### 解析逻辑
- 使用正则表达式解析复杂的人员授权格式："序号：（人员名称：姓名，角色：角色名）"
- 支持项目简称和全称匹配（优先使用项目简称）
- 支持多角色分配（一个人员可以担任多个角色）
- 自动角色名称映射（如"主要研究者"映射为"PI"）
- 验证人员和角色是否存在于数据库中

#### 验证规则
- 项目名称必须匹配现有项目
- 人员姓名必须存在于员工表中
- 角色名称必须存在于角色字典中
- 检查重复分配情况

### 2. 数据导入流程

#### 导入步骤
1. **文件上传**: 用户选择CSV文件
2. **格式验证**: 检查CSV文件基本格式
3. **数据解析**: 解析CSV内容为结构化数据
4. **业务验证**: 验证项目、人员、角色匹配
5. **预览确认**: 显示验证结果和导入预览
6. **执行导入**: 在数据库事务中执行导入
7. **结果反馈**: 显示导入结果和统计信息

#### 错误处理
- 格式错误：CSV文件格式不正确
- 数据错误：项目、人员或角色不存在
- 业务错误：重复分配或权限问题
- 系统错误：数据库连接或事务失败

### 3. 质量控制系统

#### 关键角色检查
系统检查每个项目是否配置了以下关键角色：
- **PI (Principal Investigator)**: 主要研究者
- **CRC (Clinical Research Coordinator)**: 临床研究协调员
- **CRA (Clinical Research Associate)**: 临床研究助理

#### 质量指标
- 项目总数和通过率统计
- 缺失角色的详细信息
- 改进建议和操作指导

#### 可视化展示
- 项目列表中的警告图标
- 悬浮提示显示缺失角色
- 专门的质量控制检查页面

## 数据库设计

### 相关表结构

#### 项目人员角色表 (project_personnel_roles)
```sql
CREATE TABLE project_personnel_roles (
    assignment_id INTEGER PRIMARY KEY,
    project_id TEXT NOT NULL,
    personnel_id INTEGER NOT NULL,
    role_item_id INTEGER NOT NULL,
    FOREIGN KEY (project_id) REFERENCES projects(project_id),
    FOREIGN KEY (personnel_id) REFERENCES staff(id),
    FOREIGN KEY (role_item_id) REFERENCES dictionary_items(item_id)
);
```

#### 员工表 (staff)
```sql
CREATE TABLE staff (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    -- 其他字段...
);
```

#### 角色字典 (dictionary_items)
```sql
-- 角色存储在字典系统中，字典名称为"研究角色"
SELECT di.item_id, di.item_value 
FROM dictionary_items di 
JOIN dictionaries d ON di.dictionary_id = d.id 
WHERE d.name = '研究角色';
```

## API接口文档

### 前端调用接口

#### 1. 解析CSV数据
```typescript
csvImportService.parseCsvPersonnelData(csvContent: string): Promise<CsvImportValidation>
```

#### 2. 执行导入
```typescript
csvImportService.executeCsvPersonnelImport(validation: CsvImportValidation): Promise<CsvImportResult>
```

#### 3. 质量控制检查
```typescript
csvImportService.performProjectQualityControl(projectIds?: string[]): Promise<QualityControlResult[]>
```

### 后端Tauri命令

#### 1. parse_csv_personnel_data
- **输入**: `csv_content: String, db_path: String`
- **输出**: `Result<CsvImportValidation, String>`
- **功能**: 解析和验证CSV文件内容

#### 2. execute_csv_personnel_import
- **输入**: `validation: CsvImportValidation, db_path: String`
- **输出**: `Result<CsvImportResult, String>`
- **功能**: 执行CSV数据导入

#### 3. perform_project_quality_control
- **输入**: `project_ids: Option<Vec<String>>, db_path: String`
- **输出**: `Result<Vec<QualityControlResult>, String>`
- **功能**: 执行项目质量控制检查

## 使用指南

### 1. 准备CSV文件

#### 下载模板
1. 在项目列表页点击"批量导入"按钮
2. 在导入对话框中点击"下载模板文件"
3. 获得标准格式的CSV模板（包含完整的15列结构）

#### 填写数据
1. 在"项目"列填写项目简称（如：202102-阿斯利康-哮喘-LOGOS）
2. 在"项目全称"列填写完整的项目名称
3. 在"授权人员"列按格式填写：`序号：（人员名称：姓名，角色：角色名），...`
4. 支持的角色类型：
   - 主要研究者（自动映射为PI）
   - 临床协调员(CRC)
   - CRA
   - SUBI
   - 研究护士、药品管理员、样本管理员、物资管理员、肺功能师、质控员等
5. 确保人员姓名和角色名称与系统中的数据一致

### 2. 执行导入

#### 导入流程
1. 点击"批量导入"按钮打开导入对话框
2. 选择准备好的CSV文件
3. 系统自动验证文件格式和数据
4. 查看验证结果和导入预览
5. 确认无误后点击"执行导入"
6. 查看导入结果和统计信息

#### 注意事项
- 导入过程中请勿关闭对话框
- 如有错误，请根据提示修改CSV文件后重新导入
- 导入成功后项目列表会自动刷新

### 3. 质量控制检查

#### 查看质量状态
1. 在项目列表中查看人员列的警告图标
2. 悬浮鼠标查看缺失的关键角色
3. 访问专门的质量控制页面查看详细信息

#### 修复问题项目
1. 点击项目的"编辑"按钮
2. 进入"研究人员"配置页面
3. 添加缺失的关键角色人员
4. 保存项目并重新检查

## 错误处理和故障排除

### 常见错误类型

#### 1. 文件格式错误
- **错误**: "CSV文件缺少必要的列标题"
- **解决**: 确保CSV文件包含"项目名称"和"授权人员"列

#### 2. 项目匹配失败
- **错误**: "未找到项目: XXX"
- **解决**: 检查项目名称是否正确，支持简称和全称

#### 3. 人员不存在
- **错误**: "未找到人员: XXX"
- **解决**: 确保人员已在系统中注册

#### 4. 角色不存在
- **错误**: "未找到角色: XXX"
- **解决**: 检查角色名称是否与系统中的角色字典一致

### 调试技巧

#### 1. 查看日志
- 前端：打开浏览器开发者工具查看控制台日志
- 后端：查看Tauri应用的日志输出

#### 2. 数据验证
- 使用SQL查询验证项目、人员、角色数据
- 检查数据库连接和权限设置

#### 3. 分步测试
- 先测试单个项目的导入
- 逐步增加数据量进行测试
- 验证每个步骤的输出结果

## 性能优化建议

### 1. 批量处理优化
- 使用数据库事务减少I/O操作
- 批量查询减少数据库连接次数
- 合理设置批处理大小

### 2. 内存管理
- 大文件分块处理避免内存溢出
- 及时释放不需要的数据结构
- 使用流式处理处理大量数据

### 3. 用户体验优化
- 提供进度指示器显示处理进度
- 异步处理避免界面阻塞
- 合理的错误提示和用户引导

## 扩展功能建议

### 1. 导入历史记录
- 记录每次导入的详细信息
- 支持导入历史查询和回滚
- 提供导入统计和分析报告

### 2. 模板管理
- 支持多种CSV模板格式
- 自定义字段映射配置
- 模板版本管理和兼容性

### 3. 高级验证规则
- 自定义业务验证规则
- 角色权限和冲突检查
- 项目状态和时间限制验证

### 4. 集成外部系统
- 支持从其他系统导入人员数据
- API接口对接第三方系统
- 数据同步和一致性保证
