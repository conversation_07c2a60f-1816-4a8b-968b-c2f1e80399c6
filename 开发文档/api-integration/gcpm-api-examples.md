# GCPM API 调用示例

## 文档说明

> **注意**: 本文档提供 GCPM 系统的 API 调用示例和规范。完整的 API 规范请参考 [GCPM OpenAPI 规范](./openapi-specs/gcpm-openapi-spec.json)。

## API 基本信息

- **BASE_URL**: `https://api.e3e4.club`
- **认证方式**: JWT Token
- **数据格式**: JSON

## 登录获取token

请求:
```
curl -X 'POST' \
  'https://api.e3e4.club/auth/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "username": "<PERSON><PERSON><PERSON><PERSON>",
  "password": "changethepassword"
}'
```

返回示例:
```
{"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.m7vJfDJuXxlmiAB1D05eZw3msNk_ABw-1TrxYnE4wdU","user":{"id":3,"username":"<PERSON><PERSON><PERSON><PERSON>","email":"lysan<PERSON>@qq.com","phone":"","userTypeId":0,"userProfile":null}
```

## /referral-management/referrer 分页查询推荐人

```
 curl -X 'GET' \
        'https://api.e3e4.club/auth/login/referral-management/referrer?limit=10&sortBy=createdAt&sortOrder=desc' \
        -H 'accept: application/json' \
        -H 'Authorization: Bearer <从POST /auth/login获取的token>'
```

```
{
  "items": [
    {
      "id": "string",
      "name": "string",
      "phone": "string",
      "createdAt": "2025-05-14T07:41:38.091Z",
      "updatedAt": "2025-05-14T07:41:38.091Z"
    }
  ],
  "nextCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "previousCursor": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或 12345",
  "sortBy": "createdAt",
  "sortOrder": "desc"
}
```

## 相关文档

- [GCPM OpenAPI 规范](./openapi-specs/gcpm-openapi-spec.json)
- [转诊管理 API 文档](./referral-management-api.md)
- [功能使用手册](../../FUNCTIONAL_MANUAL.md)

---

**最后更新**: 2024年12月  
**维护者**: 开发团队  
**文档版本**: v2.0
